# RPA-K8s 镜像构建指导

## 当前状态
- ✅ Docker配置已优化 (Playwright替代Chrome/ChromeDriver)
- ✅ 构建脚本已准备
- ⏳ 等待Docker Desktop完全启动

## 手动构建步骤

### 1. 确认Docker状态
```powershell
# 检查Docker版本
docker --version

# 检查Docker服务状态
docker info

# 测试Docker运行
docker ps
```

### 2. 构建基础镜像
```powershell
docker build --build-arg BUILD_DATE=$(Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ") --build-arg GIT_COMMIT="manual-build" -f Dockerfile.base -t rpa-k8s-base:latest .
```

### 3. 构建业务镜像
```powershell
docker build --build-arg BUSINESS_TYPE=shop_account_info --build-arg SCRIPT_NAME=shop_account_processor_async --build-arg BUILD_DATE=$(Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ") --build-arg GIT_COMMIT="manual-build" -f Dockerfile -t rpa-shop_account_info-shop_account_processor_async:latest .
```

### 4. 验证构建结果
```powershell
docker images | Select-String "rpa-"
```

## 自动化方式

### 方法1: 使用批处理脚本
```cmd
build-optimized.bat
```

### 方法2: 使用现有shell脚本 (如果WSL可用)
```bash
./docker-build-business.sh shop_account_info shop_account_processor_async
```

## 推送到阿里云

### 1. 登录阿里云镜像仓库
```powershell
docker login crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com
```

### 2. 标记镜像
```powershell
# 基础镜像
docker tag rpa-k8s-base:latest crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-k8s-base:latest

# 业务镜像
docker tag rpa-shop_account_info-shop_account_processor_async:latest crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-shop_account_info-shop_account_processor_async:latest
```

### 3. 推送镜像
```powershell
# 推送基础镜像
docker push crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-k8s-base:latest

# 推送业务镜像
docker push crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-shop_account_info-shop_account_processor_async:latest
```

## 故障排除

### Docker连接问题
- 确保Docker Desktop已启动并显示绿色状态
- 检查Docker设置中的资源分配
- 重启Docker Desktop如果有问题

### 构建失败
- 检查网络连接 (需要下载Python包和系统依赖)
- 确保磁盘空间充足 (至少5GB)
- 查看具体错误信息进行针对性解决 