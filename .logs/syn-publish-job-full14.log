=== RPA Container Starting (export_publish_list) ===
Timezone: Fri Jul 11 13:43:45 CST 2025
Python Path: 
Working Directory: /app
===================================================
Environment Variables:
BUSINESS_TYPE: export_publish_list
SCRIPT_NAME: export_publish_list
TZ: Asia/Shanghai
RPA_EXECUTION_MODE: docker
===================================================
Verifying browser installation...
total 24
drwxr-xr-x 1 <USER> <GROUP> 4096 Jul 10 17:45 .
drwxr-xr-x 1 <USER> <GROUP> 4096 Jul 10 17:41 ..
drwxr-xr-x 1 <USER> <GROUP> 4096 Jul 10 17:41 .links
drwxr-xr-x 1 <USER> <GROUP> 4096 Jul 10 17:45 chromium-1148
drwxr-xr-x 1 <USER> <GROUP> 4096 Jul 10 17:45 chromium_headless_shell-1148
drwxr-xr-x 1 <USER> <GROUP> 4096 Jul 10 17:45 ffmpeg-1010
Browser cache directory found
Verifying core dependencies...
3.12.13
playwright loaded
Creating download directory...
Starting export_publish_list main script...
===================================================
{
  "timestamp": "2025-07-11T13:43:46.922272Z",
  "level": "INFO",
  "message": "启动导出功能",
  "logger": "__main__",
  "module": "main",
  "function": "main",
  "line": 283
}
{
  "timestamp": "2025-07-11T13:43:46.923033Z",
  "level": "INFO",
  "message": "RPA任务开始",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "task_start"
}
{
  "timestamp": "2025-07-11T13:43:46.923181Z",
  "level": "INFO",
  "message": "异步RPA脚本初始化完成: export_publish_list.export_publish_list",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "async_rpa_init",
  "extra_data": {
    "business_type": "export_publish_list",
    "script_name": "export_publish_list",
    "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
    "config_keys": [
      "business_type",
      "script_name",
      "task_params",
      "timeout",
      "retry_count",
      "PLAYWRIGHT_PAGE_LOAD_WAIT",
      "PLAYWRIGHT_TIMEOUT",
      "LOG_LEVEL",
      "PLAYWRIGHT_HEADLESS",
      "PLAYWRIGHT_WINDOW_SIZE",
      "DB_HOST",
      "DB_PORT",
      "DB_DATABASE",
      "DB_USERNAME",
      "DB_PASSWORD"
    ],
    "async_mode": true,
    "driver_type": "playwright"
  }
}
{
  "timestamp": "2025-07-11T13:43:46.923309Z",
  "level": "INFO",
  "message": "导出客户端初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "base_url": "https://salecentersaasapi.yibainetwork.com",
    "poll_interval": 60,
    "max_poll_time": 5400
  }
}
{
  "timestamp": "2025-07-11T13:43:46.923399Z",
  "level": "INFO",
  "message": "通用Token提取器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:46.923994Z",
  "level": "INFO",
  "message": "异步亿迈登录管理器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "yimai_login_init",
  "extra_data": {
    "business_type": "export_publish_list",
    "script_name": "export_publish_list",
    "has_username": true,
    "has_password": true,
    "base_url": "https://dcmmaster.yibainetwork.com/",
    "cache_duration": 300
  }
}
{
  "timestamp": "2025-07-11T13:43:46.929407Z",
  "level": "INFO",
  "message": "RPA任务开始",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "ef97face-f280-442d-a233-7fe3b81d6f20",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "task_start"
}
{
  "timestamp": "2025-07-11T13:43:47.203075Z",
  "level": "INFO",
  "message": "创建数据库连接池: rm-bp151ouf41d67hk3v4o.mysql.rds.aliyuncs.com:3306/lingyi",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "ef97face-f280-442d-a233-7fe3b81d6f20",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "create_pool"
}
{
  "timestamp": "2025-07-11T13:43:47.203201Z",
  "level": "INFO",
  "message": "数据库管理器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "ef97face-f280-442d-a233-7fe3b81d6f20",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "db_init",
  "extra_data": {
    "host": "rm-bp151ouf41d67hk3v4o.mysql.rds.aliyuncs.com",
    "database": "lingyi",
    "pool_key": "rm-bp151ouf41d67hk3v4o.mysql.rds.aliyuncs.com:3306/lingyi"
  }
}
{
  "timestamp": "2025-07-11T13:43:47.203290Z",
  "level": "INFO",
  "message": "数据库操作器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "table_name": "publish_success_list",
    "insert_fields_count": 32,
    "update_fields_count": 30
  }
}
{
  "timestamp": "2025-07-11T13:43:47.203369Z",
  "level": "INFO",
  "message": "导出管理器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "business_type": "export_publish_list",
    "script_name": "export_publish_list",
    "download_dir": "downloads",
    "platform_code": "AMAZON",
    "use_temp_files": true,
    "auto_cleanup": true
  }
}
🔍 环境检测: kubernetes
🖥️  系统平台: Linux
🏠 本地开发: 否
☁️  生产环境: 是
🗄️  数据库: <EMAIL>:3306/lingyi
🌐 浏览器: 无头模式
==================================================
{
  "timestamp": "2025-07-11T13:43:47.206011Z",
  "level": "INFO",
  "message": "导出RPA初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:47.206091Z",
  "level": "INFO",
  "message": "开始执行异步RPA任务",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "async_task_start",
  "extra_data": {
    "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
    "business_type": "export_publish_list",
    "script_name": "export_publish_list"
  }
}
{
  "timestamp": "2025-07-11T13:43:47.206305Z",
  "level": "INFO",
  "message": "执行异步前置验证",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "async_pre_validation"
}
{
  "timestamp": "2025-07-11T13:43:47.207014Z",
  "level": "INFO",
  "message": "异步前置验证通过",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "async_pre_validation_passed"
}
{
  "timestamp": "2025-07-11T13:43:47.207090Z",
  "level": "INFO",
  "message": "执行主要RPA逻辑",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "main_execution_start"
}
{
  "timestamp": "2025-07-11T13:43:47.207148Z",
  "level": "INFO",
  "message": "开始执行导出任务并存储到数据库（智能模式）",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:47.207222Z",
  "level": "INFO",
  "message": "计算的昨天日期: 2025-07-10",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:47.207260Z",
  "level": "INFO",
  "message": "从数据库获取最新创建时间",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:47.212163Z",
  "level": "INFO",
  "message": "获取数据库连接",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "ef97face-f280-442d-a233-7fe3b81d6f20",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "get_connection"
}
{
  "timestamp": "2025-07-11T13:43:49.815036Z",
  "level": "INFO",
  "message": "数据库操作: select",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "ef97face-f280-442d-a233-7fe3b81d6f20",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "database_operation",
  "extra_data": {
    "operation": "select",
    "affected_rows": 1,
    "query": "\n                SELECT MAX(create_time) as latest_datetime \n                FROM publish_success_list \n                WHERE create_time IS NOT NULL\n            "
  }
}
{
  "timestamp": "2025-07-11T13:43:49.815180Z",
  "level": "INFO",
  "message": "查询执行完成，返回 1 行，耗时 2.608秒",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "ef97face-f280-442d-a233-7fe3b81d6f20",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "query_complete",
  "extra_data": {
    "affected_rows": 1,
    "execution_time": 2.607673406600952
  }
}
{
  "timestamp": "2025-07-11T13:43:49.824967Z",
  "level": "INFO",
  "message": "数据库连接已返回池中",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "ef97face-f280-442d-a233-7fe3b81d6f20",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "return_connection"
}
{
  "timestamp": "2025-07-11T13:43:49.825130Z",
  "level": "INFO",
  "message": "数据库最新创建时间: 2025-07-10 (优化查询)",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:49.825185Z",
  "level": "INFO",
  "message": "数据库最新创建时间: 2025-07-10",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:49.825237Z",
  "level": "INFO",
  "message": "使用智能计算的时间范围: 2025-07-10 00:00:00 ~ 2025-07-10 23:59:59",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:49.825289Z",
  "level": "INFO",
  "message": "智能计算的导出时间范围: ('2025-07-10 00:00:00', '2025-07-10 23:59:59')",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:49.825336Z",
  "level": "INFO",
  "message": "🎯 首先尝试智能模式（无Web驱动）",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:49.825388Z",
  "level": "INFO",
  "message": "开始智能导出并存储到数据库流程",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:49.825475Z",
  "level": "ERROR",
  "message": "智能导出并存储流程失败: 无法获取认证Token，请先通过Web驱动登录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "exception_type": "Exception",
    "exception_details": "无法获取认证Token，请先通过Web驱动登录",
    "platform_code": "AMAZON",
    "date_range": [
      "2025-07-10 00:00:00",
      "2025-07-10 23:59:59"
    ]
  }
}
{
  "timestamp": "2025-07-11T13:43:49.825567Z",
  "level": "INFO",
  "message": "⚠️ 智能模式失败，尝试标准Web驱动模式",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "smart_mode_error": "无法获取认证Token，请先通过Web驱动登录"
  }
}
{
  "timestamp": "2025-07-11T13:43:49.826511Z",
  "level": "INFO",
  "message": "RPA任务开始",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "task_start"
}
{
  "timestamp": "2025-07-11T13:43:49.826588Z",
  "level": "INFO",
  "message": "Playwright驱动管理器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "driver_init",
  "extra_data": {
    "browser_type": "chromium",
    "headless": true,
    "viewport": {
      "width": 1920,
      "height": 1080
    }
  }
}
{
  "timestamp": "2025-07-11T13:43:49.826677Z",
  "level": "INFO",
  "message": "RPA任务开始",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "task_start"
}
{
  "timestamp": "2025-07-11T13:43:49.826819Z",
  "level": "INFO",
  "message": "Web驱动创建成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "driver_factory",
  "extra_data": {
    "driver_type": "playwright",
    "business_type": "export_publish_list",
    "script_name": "export_publish_list",
    "use_cache": true,
    "cache_key": "playwright_export_publish_list_export_publish_list"
  }
}
{
  "timestamp": "2025-07-11T13:43:49.826888Z",
  "level": "INFO",
  "message": "Web驱动实例已创建",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "web_driver_created",
  "extra_data": {
    "driver_type": "chromium",
    "headless": true,
    "timeout": 45
  }
}
{
  "timestamp": "2025-07-11T13:43:50.273318Z",
  "level": "INFO",
  "message": "Playwright驱动初始化成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "driver_initialized",
  "extra_data": {
    "browser_type": "chromium",
    "context_options": {
      "viewport": {
        "width": 1920,
        "height": 1080
      },
      "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
      "ignore_https_errors": true,
      "bypass_csp": true,
      "java_script_enabled": true
    }
  }
}
{
  "timestamp": "2025-07-11T13:43:50.273468Z",
  "level": "INFO",
  "message": "标准模式使用相同的时间范围: ('2025-07-10 00:00:00', '2025-07-10 23:59:59')",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:50.273533Z",
  "level": "INFO",
  "message": "开始执行导出并存储到数据库流程",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:50.273591Z",
  "level": "INFO",
  "message": "检查登录状态",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:43:50.273636Z",
  "level": "INFO",
  "message": "开始确保亿迈系统登录状态",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "ensure_login_start"
}
{
  "timestamp": "2025-07-11T13:43:50.273752Z",
  "level": "INFO",
  "message": "用户未登录，开始执行登录操作",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "start_login"
}
{
  "timestamp": "2025-07-11T13:43:50.273792Z",
  "level": "INFO",
  "message": "开始执行登录操作 (第1次尝试)",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "perform_login_start"
}
{
  "timestamp": "2025-07-11T13:43:50.275997Z",
  "level": "INFO",
  "message": "导航到登录页面: https://dcmmaster.yibainetwork.com/#/login_page",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "navigate_to_login"
}
{
  "timestamp": "2025-07-11T13:43:52.694703Z",
  "level": "INFO",
  "message": "导航到URL成功: https://dcmmaster.yibainetwork.com/#/login_page",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "navigate",
  "extra_data": {
    "url": "https://dcmmaster.yibainetwork.com/#/login_page",
    "wait_until": "domcontentloaded",
    "timeout": 30000
  }
}
{
  "timestamp": "2025-07-11T13:44:09.748627Z",
  "level": "INFO",
  "message": "开始填写登录表单",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "fill_login_form"
}
{
  "timestamp": "2025-07-11T13:44:09.889268Z",
  "level": "INFO",
  "message": "输入文本成功: input[placeholder*=\"用户名\"], input[name=\"username\"], input[type=\"text\"]",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "input",
  "extra_data": {
    "selector": "input[placeholder*=\"用户名\"], input[name=\"username\"], input[type=\"text\"]",
    "normalized_selector": "input[placeholder*=\"用户名\"], input[name=\"username\"], input[type=\"text\"]",
    "text_length": 11,
    "clear_first": true
  }
}
{
  "timestamp": "2025-07-11T13:44:10.017575Z",
  "level": "INFO",
  "message": "输入文本成功: input[placeholder*=\"密码\"], input[name=\"password\"], input[type=\"password\"]",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "input",
  "extra_data": {
    "selector": "input[placeholder*=\"密码\"], input[name=\"password\"], input[type=\"password\"]",
    "normalized_selector": "input[placeholder*=\"密码\"], input[name=\"password\"], input[type=\"password\"]",
    "text_length": 8,
    "clear_first": true
  }
}
{
  "timestamp": "2025-07-11T13:44:11.018582Z",
  "level": "INFO",
  "message": "登录表单填写完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "fill_login_form_success"
}
{
  "timestamp": "2025-07-11T13:44:11.018732Z",
  "level": "INFO",
  "message": "点击登录按钮",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "click_login_button"
}
{
  "timestamp": "2025-07-11T13:44:11.153176Z",
  "level": "INFO",
  "message": "登录按钮点击成功，使用选择器: button:has-text('登录') (第1个匹配)",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "click_login_button_success"
}
{
  "timestamp": "2025-07-11T13:44:11.153293Z",
  "level": "INFO",
  "message": "等待登录完成，最大等待时间: 30秒",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "wait_login_completion"
}
{
  "timestamp": "2025-07-11T13:44:13.165320Z",
  "level": "INFO",
  "message": "检测到URL变化到主页面: https://dcmmaster.yibainetwork.com/#/home_page",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "url_change_detected"
}
{
  "timestamp": "2025-07-11T13:44:13.171016Z",
  "level": "INFO",
  "message": "登录操作执行成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "perform_login_success"
}
{
  "timestamp": "2025-07-11T13:44:13.171101Z",
  "level": "INFO",
  "message": "验证登录结果",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "verify_login_success"
}
{
  "timestamp": "2025-07-11T13:44:15.364807Z",
  "level": "INFO",
  "message": "页面标题验证登录成功: 亿迈商户系统",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "title_verification_success"
}
{
  "timestamp": "2025-07-11T13:44:15.364932Z",
  "level": "INFO",
  "message": "亿迈系统登录成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "login_success",
  "extra_data": {
    "attempts": 1,
    "successes": 1
  }
}
{
  "timestamp": "2025-07-11T13:44:15.365021Z",
  "level": "INFO",
  "message": "登录状态确认成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:15.365077Z",
  "level": "INFO",
  "message": "开始提取认证Token",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:17.365692Z",
  "level": "INFO",
  "message": "开始提取所有Token信息",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:17.370684Z",
  "level": "INFO",
  "message": "从Cookie提取JWT Token成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "jwt_token_preview": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzZXJ2aWNlU...",
    "jwt_token_length": 653,
    "source": "cookie"
  }
}
{
  "timestamp": "2025-07-11T13:44:17.628322Z",
  "level": "WARNING",
  "message": "未找到Token1-Check",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:17.873673Z",
  "level": "WARNING",
  "message": "未找到设备指纹，使用默认值: 9433e74fb0d88f3fcf842d3b479d5187",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.300761Z",
  "level": "WARNING",
  "message": "未找到签名",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.311805Z",
  "level": "INFO",
  "message": "成功提取 1 个Cookie",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "cookie_names": [
      "POS_COOKIE_WEBFRONT_userdata"
    ]
  }
}
{
  "timestamp": "2025-07-11T13:44:18.311939Z",
  "level": "INFO",
  "message": "Token提取完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "has_jwt_token": true,
    "has_token1_check": false,
    "has_device_number": true,
    "has_token2": false,
    "has_anticlimb_code": false,
    "has_sign": false,
    "has_cookies": true,
    "cookie_count": 1
  }
}
{
  "timestamp": "2025-07-11T13:44:18.312072Z",
  "level": "INFO",
  "message": "Token验证通过",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.312374Z",
  "level": "INFO",
  "message": "Token提取和验证成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.312471Z",
  "level": "INFO",
  "message": "开始执行导出流程，支持生成失败重试，最大重试次数: 3",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.312532Z",
  "level": "INFO",
  "message": "🚀 第1次尝试执行导出流程",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.312594Z",
  "level": "INFO",
  "message": "📝 开始新的导出请求",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.312648Z",
  "level": "INFO",
  "message": "请求导出数据 (第1次尝试)",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:44:18.312707Z",
  "level": "INFO",
  "message": "开始请求导出数据",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "platform_code": "AMAZON",
    "date_range": [
      "2025-07-10 00:00:00",
      "2025-07-10 23:59:59"
    ]
  }
}
{
  "timestamp": "2025-07-11T13:45:18.445371Z",
  "level": "ERROR",
  "message": "导出请求异常: 导出请求失败: 504, <html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>openresty/********</center>\r\n</body>\r\n</html>\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "exception_type": "Exception",
    "exception_details": "导出请求失败: 504, <html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>openresty/********</center>\r\n</body>\r\n</html>\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n",
    "exception_args": "('导出请求失败: 504, <html>\\r\\n<head><title>504 Gateway Time-out</title></head>\\r\\n<body>\\r\\n<center><h1>504 Gateway Time-out</h1></center>\\r\\n<hr><center>openresty/********</center>\\r\\n</body>\\r\\n</html>\\r\\n<!-- a padding to disable MSIE and Chrome friendly error page -->\\r\\n<!-- a padding to disable MSIE and Chrome friendly error page -->\\r\\n<!-- a padding to disable MSIE and Chrome friendly error page -->\\r\\n<!-- a padding to disable MSIE and Chrome friendly error page -->\\r\\n<!-- a padding to disable MSIE and Chrome friendly error page -->\\r\\n<!-- a padding to disable MSIE and Chrome friendly error page -->\\r\\n',)",
    "platform_code": "AMAZON",
    "date_range": [
      "2025-07-10 00:00:00",
      "2025-07-10 23:59:59"
    ],
    "url": "https://salecentersaasapi.yibainetwork.com/publish/publish_success/publish_success_export",
    "timeout_setting": "60秒"
  }
}
{
  "timestamp": "2025-07-11T13:45:18.445581Z",
  "level": "WARNING",
  "message": "检测到504网关超时错误，尝试检查已有导出任务",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "exception_details": "导出请求失败: 504, <html>\r\n<head><title>504 Gateway Time-out</title></head>\r\n<body>\r\n<center><h1>504 Gateway Time-out</h1></center>\r\n<hr><center>openresty/********</center>\r\n</body>\r\n</html>\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n<!-- a padding to disable MSIE and Chrome friendly error page -->\r\n",
    "retry_count": 1,
    "platform_code": "AMAZON",
    "date_range": [
      "2025-07-10 00:00:00",
      "2025-07-10 23:59:59"
    ]
  }
}
{
  "timestamp": "2025-07-11T13:45:18.445688Z",
  "level": "INFO",
  "message": "检查今日是否已有导出任务",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:45:18.445760Z",
  "level": "INFO",
  "message": "获取导出管理列表",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "page": 1,
    "limit": 20,
    "created_unix": "2025-07-11"
  }
}
{
  "timestamp": "2025-07-11T13:45:18.910114Z",
  "level": "INFO",
  "message": "获取导出列表成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_count": 9
  }
}
{
  "timestamp": "2025-07-11T13:45:18.910746Z",
  "level": "INFO",
  "message": "获取导出列表成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_count": 9
  }
}
{
  "timestamp": "2025-07-11T13:45:18.910901Z",
  "level": "INFO",
  "message": "发现今日最新有效导出任务",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "task_id": "3258",
    "task_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205",
    "created_time": "2025-07-11 13:44:18",
    "rows_sum": "3368",
    "status": "待生成",
    "total_candidates": 9
  }
}
{
  "timestamp": "2025-07-11T13:45:18.910988Z",
  "level": "INFO",
  "message": "任务状态为'待生成'，等待生成完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:45:18.911252Z",
  "level": "INFO",
  "message": "开始轮询等待导出完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "export_id": "3258",
    "target_date": "2025-07-11",
    "max_poll_time": 5400
  }
}
{
  "timestamp": "2025-07-11T13:45:18.911336Z",
  "level": "INFO",
  "message": "第 1 次轮询检查导出状态",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:45:18.911418Z",
  "level": "INFO",
  "message": "获取导出管理列表",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "page": 1,
    "limit": 20,
    "created_unix": "2025-07-11"
  }
}
{
  "timestamp": "2025-07-11T13:45:19.445543Z",
  "level": "INFO",
  "message": "获取导出列表成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_count": 9
  }
}
{
  "timestamp": "2025-07-11T13:45:19.446094Z",
  "level": "INFO",
  "message": "导出任务状态: 待生成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:46:19.468641Z",
  "level": "INFO",
  "message": "第 2 次轮询检查导出状态",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:46:19.468846Z",
  "level": "INFO",
  "message": "获取导出管理列表",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "page": 1,
    "limit": 20,
    "created_unix": "2025-07-11"
  }
}
{
  "timestamp": "2025-07-11T13:46:19.744238Z",
  "level": "INFO",
  "message": "获取导出列表成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_count": 9
  }
}
{
  "timestamp": "2025-07-11T13:46:19.744823Z",
  "level": "INFO",
  "message": "导出任务状态: 待生成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:19.773612Z",
  "level": "INFO",
  "message": "第 3 次轮询检查导出状态",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:19.773779Z",
  "level": "INFO",
  "message": "获取导出管理列表",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "page": 1,
    "limit": 20,
    "created_unix": "2025-07-11"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.012961Z",
  "level": "INFO",
  "message": "获取导出列表成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_count": 9
  }
}
{
  "timestamp": "2025-07-11T13:47:20.013650Z",
  "level": "INFO",
  "message": "导出任务状态: 生成完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:20.013754Z",
  "level": "INFO",
  "message": "导出任务完成!",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "task_id": "3258",
    "task_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205",
    "total_rows": "3368",
    "file_path": "/upload/yibai_sale_center/excel/202507/11/【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205.csv"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.013854Z",
  "level": "INFO",
  "message": "任务等待完成成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "task_id": "3258",
    "final_status": "生成完成",
    "rows_sum": "3368"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.013947Z",
  "level": "INFO",
  "message": "发现今日已完成的导出任务，直接使用",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "task_id": "3258",
    "task_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205",
    "status": "生成完成"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.014021Z",
  "level": "INFO",
  "message": "导出请求已提交",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "export_result": {
      "status": 1,
      "message": "使用已有今日导出任务",
      "existing_export_used": true,
      "export_data": {
        "id": "3258",
        "platform_code": "AMAZON",
        "task_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205",
        "data_type": "【刊登成功列表】-列表导出",
        "path": "/upload/yibai_sale_center/excel/202507/11/【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205.csv",
        "return_url": "/publish/publish_success/publish_success_export?export_type=downLoad&export_form=1&platform_code=AMAZON&export_id=3258",
        "start_time": "2025-07-11 13:44:18",
        "end_time": "2025-07-11 13:46:30",
        "status": "生成完成",
        "task_sum": "1",
        "done_task": "1",
        "rows_sum": "3368",
        "done_sum": "4937",
        "limit": "10000",
        "service_name": "end\\modules\\publish\\services\\Publish_success_service",
        "action_name": "get_export_attributes",
        "csv_header": "[\"\\u5e73\\u53f0\",\"\\u6765\\u6e90\",\"SKU\",\"SPU\",\"sellerSKU\\uff08\\u7236\\uff09\",\"sellerSKU\\uff08\\u5b50\\uff09\",\"Asin\",\"UPC\",\"EAN\",\"\\u54c1\\u724c\",\"\\u96f6\\u4ef6\\u53f7\",\"\\u53d8\\u4f53\\u7c7b\\u578b\",\"\\u53d8\\u4f53\\u540d\\u79f01\",\"\\u53d8\\u4f53\\u540d\\u79f02\",\"\\u53d8\\u4f53\\u540d\\u79f03\",\"\\u4ef7\\u683c\\uff08\\u672c\\u5e01\\uff09\",\"\\u6570\\u91cf\",\"\\u8d26\\u53f7\",\"\\u7ad9\\u70b9\",\"\\u5927\\u4ed3\",\"nodeID\",\"\\u5e73\\u53f0\\u7c7b\\u76ee\\u94fe\",\"\\u4ea7\\u54c1\\u7ebf\",\"\\u5546\\u54c1\\u7c7b\\u578b\",\"\\u4e2d\\u6587\\u6807\\u9898\",\"\\u9500\\u552e\\u5458\",\"\\u5e73\\u53f0\\u8fd4\\u56de\\u4fe1\\u606f\",\"\\u94fe\\u63a5\\u5728\\u7ebf\\u72b6\\u6001\",\"\\u521b\\u5efa\\u4eba\",\"\\u521b\\u5efa\\u65f6\\u95f4\",\"\\u6700\\u540e\\u62c9\\u53d6\\u65f6\\u95f4\",\"\\u540c\\u6b65\\u65f6\\u95f4\"]",
        "custom": "{\"platform_code\":\"AMAZON\",\"created_unix\":[1752076800,1752163199],\"export_form\":\"1\",\"jwt_token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzZXJ2aWNlUHJvdmlkZXJJZCI6MiwiYWNjb3VudE5hbWUiOiIxODI1ODExODEwMCIsInNvdXJjZSI6MSwidXNlck5hbWUiOiLmnY7li6QiLCJ1dWlkIjoiYmNjMzA3ZDYtOTQ0Yi00MjIyLWE3NzUtNTYwN2ExNjYxMTllIiwiYXV0aG9yaXR5SWQiOjE5OTI2LCJvcmdhbml6YXRpb25Db2RlIjoi5rex5Zyz5aWH54K55re76LSi5oqV6LWE5ZCI5LyZ5LyB5Lia77yI5pyJ6ZmQ5ZCI5LyZ77yJIiwibWVyY2hhbnRJZCI6IjYwNTciLCJvcmdDb2RlIjoib3JnXzAwMDAxIiwidXNlclR5cGUiOjEsInJlbGV2YW5jZUlkIjoxLCJleHAiOjE3NTIyNzAyNTEsImp0aSI6Ijg3OGU1YzgxLTA1MzAtNGZmMC04MGM1LWFmNGIzYWVlZDc1MCIsImZ4X3VzZXJfaWQiOiIxOTMxMSIsImZ4X3VzZXJfbmFtZSI6IuadjuWLpCIsImZ4X2Rpc3RyaWJ1dG9yX2lkIjoiNjA1NyJ9.NUcJunZ8ymqlBswHxetTXOLa7wLVWO_hCY-GsgbvKUU\",\"anticlimb_verify_code\":\"\",\"account_id\":[\"47680\",\"47681\",\"47682\",\"47683\",\"47684\",\"47685\",\"47686\",\"47687\",\"47688\",\"47689\",\"47690\",\"47691\",\"47692\",\"47708\",\"47709\",\"47710\",\"47711\",\"47712\",\"47713\",\"47714\",\"47715\",\"47716\",\"47717\",\"47718\",\"47719\",\"47720\",\"47721\",\"47722\",\"47723\",\"47724\",\"47725\",\"47726\",\"47727\",\"47728\",\"47729\",\"47730\",\"47731\",\"47732\",\"47733\",\"47734\",\"47735\",\"47736\",\"47737\",\"47738\",\"47739\",\"47740\",\"47741\",\"47742\",\"47743\",\"47744\",\"47745\",\"47757\",\"47758\",\"47840\",\"47841\",\"47842\",\"47843\",\"47844\",\"47845\",\"47846\",\"47847\",\"47848\",\"47849\",\"47850\",\"47851\",\"47852\",\"47853\",\"47854\",\"47855\",\"47856\",\"48091\",\"48092\",\"48093\",\"48094\",\"48095\",\"48096\",\"48097\",\"48098\",\"48099\",\"48100\",\"48101\",\"48102\",\"48103\",\"48104\",\"48105\",\"48126\",\"48127\",\"48128\",\"48129\",\"48130\",\"48131\",\"48132\",\"48133\",\"48134\",\"48135\",\"48136\",\"48137\",\"48277\",\"48279\",\"48280\",\"48281\",\"48282\",\"48283\",\"48284\",\"48292\",\"48293\",\"48294\",\"48297\",\"48298\",\"48299\",\"48300\",\"48301\",\"48302\",\"48303\",\"48304\",\"48305\",\"48306\",\"48307\",\"48308\",\"48309\",\"48310\",\"48311\",\"48312\",\"48313\",\"48314\",\"48315\",\"48316\",\"48317\",\"48318\",\"48319\",\"48320\",\"48321\",\"48322\",\"48323\",\"48324\",\"48325\",\"48326\",\"48327\",\"48328\",\"48329\",\"48330\",\"48331\",\"48332\",\"48333\",\"48334\",\"48335\",\"48336\",\"48337\",\"48338\",\"48339\",\"48340\",\"48341\",\"48342\",\"48343\",\"48344\",\"48345\",\"48346\",\"48347\",\"48348\",\"48371\",\"48372\",\"48373\",\"48374\",\"48785\",\"48786\",\"48787\",\"48796\",\"48797\",\"48834\",\"48903\",\"48931\",\"48932\",\"48933\",\"48934\",\"48935\",\"48936\",\"48937\",\"48938\",\"48939\",\"48940\",\"48941\",\"48942\",\"49033\",\"49034\",\"49035\",\"49036\",\"49360\",\"49447\",\"49601\",\"49602\",\"49603\",\"49604\",\"49605\",\"49606\",\"49607\",\"49608\",\"49609\",\"49610\",\"49910\",\"49911\",\"49912\",\"49913\",\"49914\",\"50168\",\"50444\",\"50445\",\"50446\",\"50447\",\"50449\",\"50450\",\"50451\",\"50452\",\"50453\",\"50454\",\"50455\",\"50456\",\"50457\",\"50458\",\"50459\",\"50460\",\"50461\",\"50462\",\"50463\",\"50529\",\"50530\",\"50531\",\"50678\",\"50679\",\"50680\",\"50681\",\"50682\",\"50683\",\"50684\",\"50685\",\"50686\",\"50687\",\"50688\",\"50689\",\"50690\",\"50691\",\"50878\",\"50879\",\"50880\",\"50881\",\"50882\",\"50883\",\"50884\",\"50885\",\"50886\",\"50937\",\"51658\",\"51659\",\"51660\",\"51661\",\"51662\",\"51663\",\"51664\",\"51665\",\"51666\",\"51667\",\"51668\",\"51669\",\"51670\",\"51671\",\"51672\",\"51673\",\"51674\",\"51675\",\"51676\",\"51677\",\"51678\",\"51679\",\"51680\",\"51681\",\"51682\",\"51683\",\"51684\",\"51685\",\"51686\",\"51687\",\"51688\",\"51689\",\"51690\",\"51692\",\"51693\",\"51694\",\"51695\",\"51736\",\"51737\",\"51738\",\"51739\",\"51740\",\"51741\",\"51742\",\"51743\",\"51744\",\"51745\",\"51746\",\"51747\",\"51748\",\"51749\",\"51750\",\"51751\",\"51752\",\"51753\",\"51754\",\"51755\",\"51756\",\"51757\",\"51758\",\"51869\",\"51870\",\"51871\",\"51872\",\"51873\",\"51874\",\"51875\",\"51876\",\"51877\",\"52813\",\"52814\",\"52815\",\"53033\",\"53034\",\"53035\",\"53036\",\"53037\",\"53038\",\"53039\",\"53040\",\"53041\",\"53843\",\"53844\",\"53845\",\"53846\",\"53847\",\"53848\",\"53849\",\"53850\",\"53851\",\"53852\",\"53853\",\"48788\",\"48789\",\"48790\",\"48791\",\"48792\",\"48793\",\"48794\",\"48795\",\"53854\",\"54355\",\"54356\",\"54357\",\"54358\",\"54359\",\"54360\",\"54361\",\"54362\",\"54363\",\"54898\",\"54899\",\"55888\",\"55889\",\"55890\",\"55933\",\"55934\",\"55935\",\"55956\",\"55957\",\"55958\",\"55959\",\"55960\",\"55961\",\"55962\",\"55963\",\"55964\",\"55965\",\"55966\",\"55967\",\"56027\",\"56028\",\"56029\",\"56030\",\"56031\",\"56032\",\"56033\",\"56034\",\"56035\",\"56036\",\"56037\",\"56038\",\"56039\",\"56695\",\"57496\",\"57700\",\"57701\",\"57702\",\"57703\",\"57704\",\"57705\",\"57706\",\"57707\",\"57708\",\"57709\",\"57710\",\"57711\",\"57712\",\"57713\",\"58169\",\"58170\",\"58171\",\"58172\",\"58173\",\"58174\",\"58175\",\"58176\",\"58177\",\"58178\",\"58179\",\"58180\",\"60493\",\"60494\",\"60495\",\"60496\",\"61169\",\"61170\",\"61427\",\"61428\",\"61429\",\"61430\",\"61431\",\"61460\",\"61461\",\"61462\",\"61463\",\"61464\",\"61465\",\"61466\",\"61467\",\"64277\",\"64278\",\"64279\",\"64280\",\"64355\",\"64356\",\"64357\",\"64358\",\"64359\",\"64360\",\"64361\",\"64362\",\"64363\",\"64364\",\"64365\",\"64366\",\"64367\",\"64370\",\"64371\",\"64372\",\"64374\",\"64375\",\"64376\",\"64377\",\"64383\",\"64384\",\"64385\",\"64386\",\"64387\",\"64388\",\"64389\",\"64390\",\"64391\",\"64392\",\"64633\",\"64634\",\"64635\",\"64636\",\"64955\",\"64956\",\"64957\",\"64958\",\"64959\",\"64960\",\"64961\",\"64962\",\"64963\",\"64964\",\"64965\",\"64966\",\"64967\",\"65093\",\"65094\",\"65095\",\"65096\",\"65100\",\"65101\",\"65102\",\"65103\",\"65104\",\"65105\",\"65106\",\"65107\",\"65108\",\"65109\",\"65110\",\"65111\",\"65112\",\"65158\",\"65159\",\"65160\",\"65161\",\"65810\",\"65811\",\"65812\",\"65813\",\"65885\",\"65886\",\"65887\",\"65888\",\"65889\",\"65890\",\"65891\",\"65892\",\"65893\",\"65981\",\"65982\",\"65983\",\"65984\",\"65985\",\"65986\",\"65987\",\"65988\",\"65989\",\"65990\",\"65991\",\"65992\",\"65993\",\"65994\",\"65995\",\"66391\",\"66392\",\"66393\",\"66394\",\"66395\",\"66396\",\"66397\",\"66398\",\"66399\",\"66494\",\"66495\",\"66496\",\"66497\",\"66498\",\"66499\",\"66500\",\"66501\",\"66502\",\"66503\",\"66504\",\"66505\",\"66506\",\"66507\",\"66508\",\"66509\",\"66510\",\"66511\",\"66558\",\"66559\",\"66560\",\"66561\",\"66562\",\"66563\",\"66564\",\"66565\",\"66566\",\"66569\",\"66570\",\"66571\",\"66572\",\"66573\",\"66574\",\"66575\",\"66576\",\"66578\",\"66579\",\"66580\",\"66582\",\"66583\",\"66584\",\"66585\",\"66586\",\"66587\",\"66715\",\"66716\",\"66717\",\"66718\",\"66729\",\"66730\",\"66731\",\"66732\",\"66733\",\"66734\",\"66735\",\"66736\",\"66737\",\"66738\",\"66739\",\"66740\",\"66741\",\"66742\",\"66743\",\"66744\",\"66745\",\"66746\",\"66747\",\"66748\",\"66749\",\"66750\",\"66751\",\"66752\",\"66753\",\"66754\",\"66755\",\"66756\",\"66757\",\"66758\",\"66759\",\"66760\",\"66761\",\"66762\",\"66763\",\"66764\",\"66765\",\"66766\",\"66767\",\"66768\",\"66797\",\"66798\",\"66799\",\"66800\",\"66801\",\"66802\",\"66804\",\"66805\",\"66806\",\"66807\",\"66808\",\"66811\",\"66812\",\"66813\",\"66814\",\"66815\",\"66816\",\"66817\",\"66818\",\"66819\",\"66821\",\"66822\",\"66823\",\"66824\",\"66825\",\"66826\",\"66827\",\"66828\",\"66829\",\"66830\",\"66831\",\"66832\",\"66833\",\"66834\",\"66835\",\"66836\",\"66837\",\"66838\",\"66840\",\"66841\",\"66842\",\"66843\",\"66844\",\"66845\",\"66846\",\"66847\",\"66916\",\"66917\",\"66918\",\"66919\",\"66920\",\"66921\",\"66922\",\"66923\",\"66924\",\"66925\",\"66926\",\"66927\",\"66928\",\"66929\",\"66930\",\"66931\",\"66932\",\"66933\",\"66934\",\"66935\",\"66936\",\"66937\",\"66938\",\"66939\",\"66940\",\"66941\",\"66942\",\"66946\",\"66947\",\"66948\",\"66952\",\"66953\",\"66954\",\"66955\",\"66956\",\"66957\",\"66980\",\"66981\",\"66982\",\"66983\",\"66984\",\"66985\",\"66986\",\"66987\",\"66988\",\"66989\",\"66990\",\"66991\",\"66992\",\"66993\",\"66994\",\"66995\",\"66996\",\"66997\",\"66998\",\"66999\",\"67000\",\"67001\",\"67002\",\"67003\",\"67004\",\"67005\",\"67006\",\"67007\",\"67008\",\"67009\",\"67010\",\"67011\",\"67012\",\"67013\",\"67014\",\"67015\",\"67017\",\"67037\",\"67038\",\"67039\",\"67040\",\"67041\",\"67042\",\"67043\",\"67044\",\"67045\",\"67077\",\"67078\",\"67079\",\"67081\",\"67083\",\"67084\",\"67085\",\"67086\",\"67087\",\"67208\",\"67209\",\"67210\",\"67211\",\"67212\",\"67213\",\"67214\",\"67215\",\"67216\",\"67262\",\"67263\",\"67264\",\"67265\",\"67266\",\"67267\",\"67268\",\"67269\",\"67270\",\"67352\",\"67353\",\"67354\",\"67355\",\"67356\",\"67357\",\"67358\",\"67359\",\"67360\",\"67361\",\"67362\",\"67363\",\"67364\",\"67365\",\"67366\",\"67392\",\"67393\",\"67394\",\"67395\",\"67396\",\"67397\",\"67398\",\"67399\",\"67400\",\"67559\",\"67560\",\"67561\",\"67562\",\"67563\",\"67564\",\"67565\",\"67566\",\"67585\",\"67586\",\"67587\",\"67599\",\"67600\",\"67601\",\"67602\",\"67603\",\"67604\",\"67607\",\"67608\",\"67609\",\"67610\",\"67611\",\"67612\",\"67613\",\"67614\",\"67615\",\"67567\",\"67697\",\"67698\",\"67699\",\"67705\",\"67706\",\"67707\",\"67708\",\"67709\",\"67710\"]}",
        "addition": "",
        "created_work_no": "李勤",
        "created_unix": "2025-07-11 13:44:18",
        "updated_work_no": "",
        "updated_unix": "1752212790",
        "down_num": "1"
      }
    },
    "attempt": "第1次尝试"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.014322Z",
  "level": "INFO",
  "message": "使用已有今日导出任务，跳过等待阶段",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:20.014396Z",
  "level": "INFO",
  "message": "导出流程完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "used_existing": true,
    "retry_count": 0,
    "task_id": "3258",
    "task_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205",
    "status": "生成完成"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.014498Z",
  "level": "INFO",
  "message": "获取导出文件内容 (第1次尝试)",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:20.014567Z",
  "level": "INFO",
  "message": "开始获取文件内容",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "file_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205.csv",
    "download_url": "https://salecentersaasapi.yibainetwork.com/upload/yibai_sale_center/excel/202507/11/【刊登成功列表】-列表导出-20...",
    "export_task_id": "3258"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.365802Z",
  "level": "INFO",
  "message": "文件内容获取成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "file_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205.csv",
    "file_size": 2285337,
    "content_type": "application/octet-stream"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.366585Z",
  "level": "INFO",
  "message": "文件内容获取成功: 2285337 字节",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:20.366723Z",
  "level": "INFO",
  "message": "开始将CSV内容存储到数据库",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:20.371793Z",
  "level": "INFO",
  "message": "CSV处理器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "supported_fields": 32,
    "validators": 2
  }
}
{
  "timestamp": "2025-07-11T13:47:20.372285Z",
  "level": "INFO",
  "message": "RPA任务开始",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "task_start"
}
{
  "timestamp": "2025-07-11T13:47:20.372394Z",
  "level": "INFO",
  "message": "数据库管理器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "db_init",
  "extra_data": {
    "host": "rm-bp151ouf41d67hk3v4o.mysql.rds.aliyuncs.com",
    "database": "lingyi",
    "pool_key": "rm-bp151ouf41d67hk3v4o.mysql.rds.aliyuncs.com:3306/lingyi"
  }
}
{
  "timestamp": "2025-07-11T13:47:20.372507Z",
  "level": "INFO",
  "message": "数据库操作器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "table_name": "publish_success_list",
    "insert_fields_count": 32,
    "update_fields_count": 30
  }
}
{
  "timestamp": "2025-07-11T13:47:20.372601Z",
  "level": "INFO",
  "message": "解析CSV内容",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.027193Z",
  "level": "INFO",
  "message": "编码检测完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "detected_encoding": "GB2312",
    "confidence": 0.99,
    "content_size": 2285337
  }
}
{
  "timestamp": "2025-07-11T13:47:23.038044Z",
  "level": "INFO",
  "message": "成功使用编码: gbk",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.046918Z",
  "level": "INFO",
  "message": "开始解析CSV内容",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "encoding": "gbk",
    "content_length": 1977674,
    "original_size": 2285337
  }
}
{
  "timestamp": "2025-07-11T13:47:23.372406Z",
  "level": "INFO",
  "message": "CSV解析完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_rows": 4937,
    "success_rows": 4937,
    "error_rows": 0,
    "success_rate": "100.0%"
  }
}
{
  "timestamp": "2025-07-11T13:47:23.372542Z",
  "level": "INFO",
  "message": "开始CSV数据去重处理",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "original_count": 4937
  }
}
{
  "timestamp": "2025-07-11T13:47:23.379506Z",
  "level": "INFO",
  "message": "CSV数据去重完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "original_count": 4937,
    "final_count": 4937,
    "duplicate_count": 0,
    "deduplication_rate": "0.0%",
    "unique_keys_count": 4937
  }
}
{
  "timestamp": "2025-07-11T13:47:23.380023Z",
  "level": "INFO",
  "message": "CSV解析完成，共4937条记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.380097Z",
  "level": "INFO",
  "message": "开始数据库批量操作 (强制更新模式)",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.380160Z",
  "level": "INFO",
  "message": "开始处理记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_records": 4937
  }
}
{
  "timestamp": "2025-07-11T13:47:23.380253Z",
  "level": "INFO",
  "message": "CSV处理器初始化完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "supported_fields": 32,
    "validators": 2
  }
}
{
  "timestamp": "2025-07-11T13:47:23.384562Z",
  "level": "INFO",
  "message": "CSV记录样例唯一键: ['*************0abeijwg#JULANCA', 'GS2228701beijwg#JULANCA', '31142300728114dbeijwg#JULANCA', '1616230089511fdbeijwg#JULANCA', '31132403224111fbeijwg#JULANCA']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.384642Z",
  "level": "INFO",
  "message": "查询已存在记录（临时表JOIN优化）",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "unique_keys_count": 4937,
    "sample_keys": [
      "*************0abeijwg#JULANCA",
      "GS2228701beijwg#JULANCA",
      "31142300728114dbeijwg#JULANCA",
      "1616230089511fdbeijwg#JULANCA",
      "31132403224111fbeijwg#JULANCA"
    ]
  }
}
{
  "timestamp": "2025-07-11T13:47:23.386509Z",
  "level": "INFO",
  "message": "开始分批查询：总计1批次，每批次10000个键",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.386619Z",
  "level": "INFO",
  "message": "执行第1/1批次查询，包含4937个键",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.391591Z",
  "level": "INFO",
  "message": "开始事务",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "transaction_start"
}
{
  "timestamp": "2025-07-11T13:47:23.398258Z",
  "level": "INFO",
  "message": "批次1数据库连接健康检查通过",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.403744Z",
  "level": "INFO",
  "message": "临时表 temp_search_1752212843386_4129_1 创建成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.404224Z",
  "level": "INFO",
  "message": "批次1临时表插入样例: [('*************0abeijwg', 'JULANCA'), ('GS2228701beijwg', 'JULANCA'), ('31142300728114dbeijwg', 'JULANCA')]",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.467674Z",
  "level": "INFO",
  "message": "向临时表插入4937条有效搜索条件",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.467778Z",
  "level": "INFO",
  "message": "批次1开始执行JOIN查询",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.971567Z",
  "level": "INFO",
  "message": "批次1查询执行完成，原始结果数量: 4937",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.971715Z",
  "level": "INFO",
  "message": "🔍 查询列名: ['id', 'seller_sku_child', 'account', 'platform', 'source']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.971785Z",
  "level": "INFO",
  "message": "🔍 原始行数据类型: <class 'dict'>",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.971852Z",
  "level": "INFO",
  "message": "🔍 原始行数据调试失败: unhashable type: 'slice', 类型: <class 'dict'>",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.983801Z",
  "level": "INFO",
  "message": "🔍 转换后结果样例: seller_sku_child=seller_sku_child, account=account",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.983963Z",
  "level": "INFO",
  "message": "批次1成功转换4937条记录为字典格式",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.984026Z",
  "level": "INFO",
  "message": "第1批次查询完成，找到4937条记录，累计4937条记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:23.988906Z",
  "level": "INFO",
  "message": "事务提交成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "transaction_commit"
}
{
  "timestamp": "2025-07-11T13:47:23.998368Z",
  "level": "INFO",
  "message": "临时表JOIN查询全部完成，总计找到 4937 条已存在记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.003219Z",
  "level": "INFO",
  "message": "获取数据库连接",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "get_connection"
}
{
  "timestamp": "2025-07-11T13:47:24.011965Z",
  "level": "INFO",
  "message": "数据库操作: select",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "database_operation",
  "extra_data": {
    "operation": "select",
    "affected_rows": 4,
    "query": "\n                    SELECT seller_sku_child, account, platform, source, sku \n                    FROM publish_success_list \n                    WHERE seller_sku_child IN ('seller_sku_child', '1011210..."
  }
}
{
  "timestamp": "2025-07-11T13:47:24.012130Z",
  "level": "INFO",
  "message": "查询执行完成，返回 4 行，耗时 0.013秒",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "query_complete",
  "extra_data": {
    "affected_rows": 4,
    "execution_time": 0.013390779495239258
  }
}
{
  "timestamp": "2025-07-11T13:47:24.022033Z",
  "level": "INFO",
  "message": "数据库连接已返回池中",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "return_connection"
}
{
  "timestamp": "2025-07-11T13:47:24.022150Z",
  "level": "INFO",
  "message": "🔍 数据库验证查询结果: [{'seller_sku_child': 'GS2228701beijwg', 'account': 'JULANUS', 'platform': 'Amazon', 'source': '精细刊登', 'sku': 'GS22287'}, {'seller_sku_child': '*************0abeijwg', 'account': 'JULANUS', 'platform': 'Amazon', 'source': '精细刊登', 'sku': '*************'}, {'seller_sku_child': 'GS2228701beijwg', 'account': 'JULANCA', 'platform': 'Amazon', 'source': '精细刊登', 'sku': 'GS22287'}]",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022227Z",
  "level": "INFO",
  "message": "🔍 记录结构调试: {'id': 'id', 'seller_sku_child': 'seller_sku_child', 'account': 'account', 'platform': 'platform', 'source': 'source'}",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022264Z",
  "level": "INFO",
  "message": "🔍 字段值调试: seller_sku_child='seller_sku_child', account='account'",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022305Z",
  "level": "INFO",
  "message": "🔍 记录结构调试: {'id': 'id', 'seller_sku_child': 'seller_sku_child', 'account': 'account', 'platform': 'platform', 'source': 'source'}",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022340Z",
  "level": "INFO",
  "message": "🔍 字段值调试: seller_sku_child='seller_sku_child', account='account'",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022371Z",
  "level": "INFO",
  "message": "🔍 记录结构调试: {'id': 'id', 'seller_sku_child': 'seller_sku_child', 'account': 'account', 'platform': 'platform', 'source': 'source'}",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022403Z",
  "level": "INFO",
  "message": "🔍 字段值调试: seller_sku_child='seller_sku_child', account='account'",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022454Z",
  "level": "INFO",
  "message": "🔍 记录结构调试: {'id': 'id', 'seller_sku_child': 'seller_sku_child', 'account': 'account', 'platform': 'platform', 'source': 'source'}",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022487Z",
  "level": "INFO",
  "message": "🔍 字段值调试: seller_sku_child='seller_sku_child', account='account'",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022523Z",
  "level": "INFO",
  "message": "🔍 记录结构调试: {'id': 'id', 'seller_sku_child': 'seller_sku_child', 'account': 'account', 'platform': 'platform', 'source': 'source'}",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022566Z",
  "level": "INFO",
  "message": "🔍 字段值调试: seller_sku_child='seller_sku_child', account='account'",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.022616Z",
  "level": "INFO",
  "message": "已存在记录样例唯一键: ['seller_sku_child#account', 'seller_sku_child#account', 'seller_sku_child#account', 'seller_sku_child#account', 'seller_sku_child#account']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.032222Z",
  "level": "INFO",
  "message": "已存在记录唯一键样例: ['seller_sku_child#account']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "existing_keys_count": 1,
    "existing_records_count": 4937
  }
}
{
  "timestamp": "2025-07-11T13:47:24.036625Z",
  "level": "INFO",
  "message": "唯一键匹配统计",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_csv_records": 4937,
    "match_found_count": 0,
    "match_not_found_count": 4937,
    "existing_keys_count": 1,
    "match_rate": "0.0%"
  }
}
{
  "timestamp": "2025-07-11T13:47:24.036747Z",
  "level": "WARNING",
  "message": "⚠️ 唯一键匹配率异常低，显示详细调试信息",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.036800Z",
  "level": "WARNING",
  "message": "CSV记录唯一键样例: ['*************0abeijwg#JULANCA', 'GS2228701beijwg#JULANCA', '31142300728114dbeijwg#JULANCA', '1616230089511fdbeijwg#JULANCA', '31132403224111fbeijwg#JULANCA']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.036836Z",
  "level": "WARNING",
  "message": "数据库记录唯一键样例: ['seller_sku_child#account']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.036876Z",
  "level": "WARNING",
  "message": "键格式比较 - CSV: ['*************0abeijwg', 'JULANCA'], 数据库: ['seller_sku_child', 'account']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.036912Z",
  "level": "ERROR",
  "message": "🚨 字段值不匹配！CSV: seller_sku='*************0abeijwg', account='JULANCA' vs 数据库: seller_sku='seller_sku_child', account='account'",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.036950Z",
  "level": "ERROR",
  "message": "字段长度比较 - CSV: seller_sku长度=21, account长度=7 vs 数据库: seller_sku长度=16, account长度=7",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.036985Z",
  "level": "INFO",
  "message": "记录分组完成 (强制更新模式)",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_records": 4937,
    "insert_count": 4937,
    "update_count": 0,
    "skip_count": 0,
    "force_update": true,
    "existing_records_count": 4937
  }
}
{
  "timestamp": "2025-07-11T13:47:24.037051Z",
  "level": "INFO",
  "message": "记录分组结果 - 新增: 4937条, 更新: 0条",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.037102Z",
  "level": "INFO",
  "message": "新增记录样例唯一键: ['*************0abeijwg#JULANCA', 'GS2228701beijwg#JULANCA', '31142300728114dbeijwg#JULANCA', '1616230089511fdbeijwg#JULANCA', '31132403224111fbeijwg#JULANCA']",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.037137Z",
  "level": "WARNING",
  "message": "⚠️ 检测到异常：所有4937条记录都被分到新增组，但数据库中存在4937条已存在记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.037229Z",
  "level": "INFO",
  "message": "🚀 第1次尝试执行批量插入: 4937条记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.037270Z",
  "level": "INFO",
  "message": "开始批量新增记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "records_count": 4937
  }
}
{
  "timestamp": "2025-07-11T13:47:24.054352Z",
  "level": "INFO",
  "message": "执行第1/1个事务，包含4937条记录",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.059407Z",
  "level": "INFO",
  "message": "开始事务",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "transaction_start"
}
{
  "timestamp": "2025-07-11T13:47:24.081458Z",
  "level": "WARNING",
  "message": "事务已回滚",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "transaction_rollback"
}
{
  "timestamp": "2025-07-11T13:47:24.081612Z",
  "level": "ERROR",
  "message": "事务执行失败: (1062, \"Duplicate entry '*************0abeijwg-JULANCA' for key 'publish_success_list.uk_seller_sku_account'\")",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "transaction_error",
  "exception": "Traceback (most recent call last):\n  File \"/app/app/core/database.py\", line 433, in transaction\n    yield conn\n  File \"/app/app/business/export_publish_list/db_operations.py\", line 355, in batch_insert_records\n    affected = cursor.executemany(sql, batch_params)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/cursors.py\", line 191, in executemany\n    self.rowcount = sum(self.execute(query, arg) for arg in args)\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/cursors.py\", line 191, in <genexpr>\n    self.rowcount = sum(self.execute(query, arg) for arg in args)\n                        ^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/cursors.py\", line 153, in execute\n    result = self._query(query)\n             ^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/cursors.py\", line 322, in _query\n    conn.query(q)\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/connections.py\", line 563, in query\n    self._affected_rows = self._read_query_result(unbuffered=unbuffered)\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/connections.py\", line 825, in _read_query_result\n    result.read()\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/connections.py\", line 1199, in read\n    first_packet = self.connection._read_packet()\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/connections.py\", line 775, in _read_packet\n    packet.raise_for_error()\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/protocol.py\", line 219, in raise_for_error\n    err.raise_mysql_exception(self._data)\n  File \"/usr/local/lib/python3.11/site-packages/pymysql/err.py\", line 150, in raise_mysql_exception\n    raise errorclass(errno, errval)\npymysql.err.IntegrityError: (1062, \"Duplicate entry '*************0abeijwg-JULANCA' for key 'publish_success_list.uk_seller_sku_account'\")"
}
{
  "timestamp": "2025-07-11T13:47:24.093830Z",
  "level": "ERROR",
  "message": "事务1失败: (1062, \"Duplicate entry '*************0abeijwg-JULANCA' for key 'publish_success_list.uk_seller_sku_account'\")",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.094098Z",
  "level": "WARNING",
  "message": "🚨 检测到唯一约束冲突，重复键值: *************0abeijwg-JULANCA",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.097868Z",
  "level": "WARNING",
  "message": "冲突记录详情: [{'seller_sku_child': '*************0abeijwg', 'account': 'JULANCA', 'transaction': 1}]",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.097970Z",
  "level": "INFO",
  "message": "批量新增完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_records": 4937,
    "affected_rows": 0
  }
}
{
  "timestamp": "2025-07-11T13:47:24.099234Z",
  "level": "INFO",
  "message": "✅ 批量插入完成: 0条",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list"
}
{
  "timestamp": "2025-07-11T13:47:24.099322Z",
  "level": "INFO",
  "message": "记录处理完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_records": 4937,
    "insert_count": 0,
    "update_count": 0,
    "error_count": 0,
    "success": true,
    "message": "处理完成：新增0条，更新0条，失败0条",
    "conflict_recovery_attempts": 0
  }
}
{
  "timestamp": "2025-07-11T13:47:24.105286Z",
  "level": "INFO",
  "message": "获取数据库连接",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "get_connection"
}
{
  "timestamp": "2025-07-11T13:47:30.586722Z",
  "level": "INFO",
  "message": "数据库操作: select",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "database_operation",
  "extra_data": {
    "operation": "select",
    "affected_rows": 1,
    "query": "SELECT COUNT(*) as total_count FROM publish_success_list"
  }
}
{
  "timestamp": "2025-07-11T13:47:30.586930Z",
  "level": "INFO",
  "message": "查询执行完成，返回 1 行，耗时 6.486秒",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "query_complete",
  "extra_data": {
    "affected_rows": 1,
    "execution_time": 6.486018657684326
  }
}
{
  "timestamp": "2025-07-11T13:47:30.596134Z",
  "level": "INFO",
  "message": "数据库连接已返回池中",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "return_connection"
}
{
  "timestamp": "2025-07-11T13:47:30.600986Z",
  "level": "INFO",
  "message": "获取数据库连接",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "get_connection"
}
{
  "timestamp": "2025-07-11T13:47:35.588996Z",
  "level": "INFO",
  "message": "数据库操作: select",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "database_operation",
  "extra_data": {
    "operation": "select",
    "affected_rows": 1,
    "query": "\n                SELECT COUNT(*) as today_count \n                FROM publish_success_list \n                WHERE DATE(created_at) = CURDATE()\n            "
  }
}
{
  "timestamp": "2025-07-11T13:47:35.589168Z",
  "level": "INFO",
  "message": "查询执行完成，返回 1 行，耗时 4.993秒",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "query_complete",
  "extra_data": {
    "affected_rows": 1,
    "execution_time": 4.992664098739624
  }
}
{
  "timestamp": "2025-07-11T13:47:35.598577Z",
  "level": "INFO",
  "message": "数据库连接已返回池中",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "return_connection"
}
{
  "timestamp": "2025-07-11T13:47:35.603608Z",
  "level": "INFO",
  "message": "获取数据库连接",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "get_connection"
}
{
  "timestamp": "2025-07-11T13:47:37.251726Z",
  "level": "INFO",
  "message": "数据库操作: select",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "database_operation",
  "extra_data": {
    "operation": "select",
    "affected_rows": 1,
    "query": "\n                SELECT platform, COUNT(*) as count \n                FROM publish_success_list \n                GROUP BY platform \n                ORDER BY count DESC \n                LIMIT 10\n       ..."
  }
}
{
  "timestamp": "2025-07-11T13:47:37.251878Z",
  "level": "INFO",
  "message": "查询执行完成，返回 1 行，耗时 1.653秒",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "query_complete",
  "extra_data": {
    "affected_rows": 1,
    "execution_time": 1.6529605388641357
  }
}
{
  "timestamp": "2025-07-11T13:47:37.261589Z",
  "level": "INFO",
  "message": "数据库连接已返回池中",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "********-33f0-4730-9e0f-537b6481c104",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "return_connection"
}
{
  "timestamp": "2025-07-11T13:47:37.261715Z",
  "level": "INFO",
  "message": "CSV数据成功存储到数据库",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_records": 4937,
    "insert_count": 0,
    "update_count": 0,
    "error_count": 0,
    "success": true,
    "message": "处理完成：新增0条，更新0条，失败0条",
    "conflict_recovery_attempts": 0,
    "csv_records_count": 4937,
    "table_statistics": {
      "total_count": 2606920,
      "today_count": 10000,
      "platform_stats": [
        {
          "platform": "Amazon",
          "count": 2606920
        }
      ],
      "last_updated": "2025-07-11T13:47:37.261678"
    },
    "processing_time": "2025-07-11T13:47:37.261687"
  }
}
{
  "timestamp": "2025-07-11T13:47:37.266118Z",
  "level": "INFO",
  "message": "导出并存储流程完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "file_size": 2285337,
    "total_rows": "3368",
    "db_stored": true,
    "db_result": {
      "total_records": 4937,
      "insert_count": 0,
      "update_count": 0,
      "error_count": 0,
      "success": true,
      "message": "处理完成：新增0条，更新0条，失败0条",
      "conflict_recovery_attempts": 0,
      "csv_records_count": 4937,
      "table_statistics": {
        "total_count": 2606920,
        "today_count": 10000,
        "platform_stats": [
          {
            "platform": "Amazon",
            "count": 2606920
          }
        ],
        "last_updated": "2025-07-11T13:47:37.261678"
      },
      "processing_time": "2025-07-11T13:47:37.261687"
    }
  }
}
{
  "timestamp": "2025-07-11T13:47:37.266308Z",
  "level": "INFO",
  "message": "标准模式导出并存储任务执行成功",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "success": true,
    "message": "导出并存储到数据库完成",
    "export_info": {
      "id": "3258",
      "platform_code": "AMAZON",
      "task_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205",
      "data_type": "【刊登成功列表】-列表导出",
      "path": "/upload/yibai_sale_center/excel/202507/11/【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205.csv",
      "return_url": "/publish/publish_success/publish_success_export?export_type=downLoad&export_form=1&platform_code=AMAZON&export_id=3258",
      "start_time": "2025-07-11 13:44:18",
      "end_time": "2025-07-11 13:46:30",
      "status": "生成完成",
      "task_sum": "1",
      "done_task": "1",
      "rows_sum": "3368",
      "done_sum": "4937",
      "limit": "10000",
      "service_name": "end\\modules\\publish\\services\\Publish_success_service",
      "action_name": "get_export_attributes",
      "csv_header": "[\"\\u5e73\\u53f0\",\"\\u6765\\u6e90\",\"SKU\",\"SPU\",\"sellerSKU\\uff08\\u7236\\uff09\",\"sellerSKU\\uff08\\u5b50\\uff09\",\"Asin\",\"UPC\",\"EAN\",\"\\u54c1\\u724c\",\"\\u96f6\\u4ef6\\u53f7\",\"\\u53d8\\u4f53\\u7c7b\\u578b\",\"\\u53d8\\u4f53\\u540d\\u79f01\",\"\\u53d8\\u4f53\\u540d\\u79f02\",\"\\u53d8\\u4f53\\u540d\\u79f03\",\"\\u4ef7\\u683c\\uff08\\u672c\\u5e01\\uff09\",\"\\u6570\\u91cf\",\"\\u8d26\\u53f7\",\"\\u7ad9\\u70b9\",\"\\u5927\\u4ed3\",\"nodeID\",\"\\u5e73\\u53f0\\u7c7b\\u76ee\\u94fe\",\"\\u4ea7\\u54c1\\u7ebf\",\"\\u5546\\u54c1\\u7c7b\\u578b\",\"\\u4e2d\\u6587\\u6807\\u9898\",\"\\u9500\\u552e\\u5458\",\"\\u5e73\\u53f0\\u8fd4\\u56de\\u4fe1\\u606f\",\"\\u94fe\\u63a5\\u5728\\u7ebf\\u72b6\\u6001\",\"\\u521b\\u5efa\\u4eba\",\"\\u521b\\u5efa\\u65f6\\u95f4\",\"\\u6700\\u540e\\u62c9\\u53d6\\u65f6\\u95f4\",\"\\u540c\\u6b65\\u65f6\\u95f4\"]",
      "custom": "{\"platform_code\":\"AMAZON\",\"created_unix\":[1752076800,1752163199],\"export_form\":\"1\",\"jwt_token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzZXJ2aWNlUHJvdmlkZXJJZCI6MiwiYWNjb3VudE5hbWUiOiIxODI1ODExODEwMCIsInNvdXJjZSI6MSwidXNlck5hbWUiOiLmnY7li6QiLCJ1dWlkIjoiYmNjMzA3ZDYtOTQ0Yi00MjIyLWE3NzUtNTYwN2ExNjYxMTllIiwiYXV0aG9yaXR5SWQiOjE5OTI2LCJvcmdhbml6YXRpb25Db2RlIjoi5rex5Zyz5aWH54K55re76LSi5oqV6LWE5ZCI5LyZ5LyB5Lia77yI5pyJ6ZmQ5ZCI5LyZ77yJIiwibWVyY2hhbnRJZCI6IjYwNTciLCJvcmdDb2RlIjoib3JnXzAwMDAxIiwidXNlclR5cGUiOjEsInJlbGV2YW5jZUlkIjoxLCJleHAiOjE3NTIyNzAyNTEsImp0aSI6Ijg3OGU1YzgxLTA1MzAtNGZmMC04MGM1LWFmNGIzYWVlZDc1MCIsImZ4X3VzZXJfaWQiOiIxOTMxMSIsImZ4X3VzZXJfbmFtZSI6IuadjuWLpCIsImZ4X2Rpc3RyaWJ1dG9yX2lkIjoiNjA1NyJ9.NUcJunZ8ymqlBswHxetTXOLa7wLVWO_hCY-GsgbvKUU\",\"anticlimb_verify_code\":\"\",\"account_id\":[\"47680\",\"47681\",\"47682\",\"47683\",\"47684\",\"47685\",\"47686\",\"47687\",\"47688\",\"47689\",\"47690\",\"47691\",\"47692\",\"47708\",\"47709\",\"47710\",\"47711\",\"47712\",\"47713\",\"47714\",\"47715\",\"47716\",\"47717\",\"47718\",\"47719\",\"47720\",\"47721\",\"47722\",\"47723\",\"47724\",\"47725\",\"47726\",\"47727\",\"47728\",\"47729\",\"47730\",\"47731\",\"47732\",\"47733\",\"47734\",\"47735\",\"47736\",\"47737\",\"47738\",\"47739\",\"47740\",\"47741\",\"47742\",\"47743\",\"47744\",\"47745\",\"47757\",\"47758\",\"47840\",\"47841\",\"47842\",\"47843\",\"47844\",\"47845\",\"47846\",\"47847\",\"47848\",\"47849\",\"47850\",\"47851\",\"47852\",\"47853\",\"47854\",\"47855\",\"47856\",\"48091\",\"48092\",\"48093\",\"48094\",\"48095\",\"48096\",\"48097\",\"48098\",\"48099\",\"48100\",\"48101\",\"48102\",\"48103\",\"48104\",\"48105\",\"48126\",\"48127\",\"48128\",\"48129\",\"48130\",\"48131\",\"48132\",\"48133\",\"48134\",\"48135\",\"48136\",\"48137\",\"48277\",\"48279\",\"48280\",\"48281\",\"48282\",\"48283\",\"48284\",\"48292\",\"48293\",\"48294\",\"48297\",\"48298\",\"48299\",\"48300\",\"48301\",\"48302\",\"48303\",\"48304\",\"48305\",\"48306\",\"48307\",\"48308\",\"48309\",\"48310\",\"48311\",\"48312\",\"48313\",\"48314\",\"48315\",\"48316\",\"48317\",\"48318\",\"48319\",\"48320\",\"48321\",\"48322\",\"48323\",\"48324\",\"48325\",\"48326\",\"48327\",\"48328\",\"48329\",\"48330\",\"48331\",\"48332\",\"48333\",\"48334\",\"48335\",\"48336\",\"48337\",\"48338\",\"48339\",\"48340\",\"48341\",\"48342\",\"48343\",\"48344\",\"48345\",\"48346\",\"48347\",\"48348\",\"48371\",\"48372\",\"48373\",\"48374\",\"48785\",\"48786\",\"48787\",\"48796\",\"48797\",\"48834\",\"48903\",\"48931\",\"48932\",\"48933\",\"48934\",\"48935\",\"48936\",\"48937\",\"48938\",\"48939\",\"48940\",\"48941\",\"48942\",\"49033\",\"49034\",\"49035\",\"49036\",\"49360\",\"49447\",\"49601\",\"49602\",\"49603\",\"49604\",\"49605\",\"49606\",\"49607\",\"49608\",\"49609\",\"49610\",\"49910\",\"49911\",\"49912\",\"49913\",\"49914\",\"50168\",\"50444\",\"50445\",\"50446\",\"50447\",\"50449\",\"50450\",\"50451\",\"50452\",\"50453\",\"50454\",\"50455\",\"50456\",\"50457\",\"50458\",\"50459\",\"50460\",\"50461\",\"50462\",\"50463\",\"50529\",\"50530\",\"50531\",\"50678\",\"50679\",\"50680\",\"50681\",\"50682\",\"50683\",\"50684\",\"50685\",\"50686\",\"50687\",\"50688\",\"50689\",\"50690\",\"50691\",\"50878\",\"50879\",\"50880\",\"50881\",\"50882\",\"50883\",\"50884\",\"50885\",\"50886\",\"50937\",\"51658\",\"51659\",\"51660\",\"51661\",\"51662\",\"51663\",\"51664\",\"51665\",\"51666\",\"51667\",\"51668\",\"51669\",\"51670\",\"51671\",\"51672\",\"51673\",\"51674\",\"51675\",\"51676\",\"51677\",\"51678\",\"51679\",\"51680\",\"51681\",\"51682\",\"51683\",\"51684\",\"51685\",\"51686\",\"51687\",\"51688\",\"51689\",\"51690\",\"51692\",\"51693\",\"51694\",\"51695\",\"51736\",\"51737\",\"51738\",\"51739\",\"51740\",\"51741\",\"51742\",\"51743\",\"51744\",\"51745\",\"51746\",\"51747\",\"51748\",\"51749\",\"51750\",\"51751\",\"51752\",\"51753\",\"51754\",\"51755\",\"51756\",\"51757\",\"51758\",\"51869\",\"51870\",\"51871\",\"51872\",\"51873\",\"51874\",\"51875\",\"51876\",\"51877\",\"52813\",\"52814\",\"52815\",\"53033\",\"53034\",\"53035\",\"53036\",\"53037\",\"53038\",\"53039\",\"53040\",\"53041\",\"53843\",\"53844\",\"53845\",\"53846\",\"53847\",\"53848\",\"53849\",\"53850\",\"53851\",\"53852\",\"53853\",\"48788\",\"48789\",\"48790\",\"48791\",\"48792\",\"48793\",\"48794\",\"48795\",\"53854\",\"54355\",\"54356\",\"54357\",\"54358\",\"54359\",\"54360\",\"54361\",\"54362\",\"54363\",\"54898\",\"54899\",\"55888\",\"55889\",\"55890\",\"55933\",\"55934\",\"55935\",\"55956\",\"55957\",\"55958\",\"55959\",\"55960\",\"55961\",\"55962\",\"55963\",\"55964\",\"55965\",\"55966\",\"55967\",\"56027\",\"56028\",\"56029\",\"56030\",\"56031\",\"56032\",\"56033\",\"56034\",\"56035\",\"56036\",\"56037\",\"56038\",\"56039\",\"56695\",\"57496\",\"57700\",\"57701\",\"57702\",\"57703\",\"57704\",\"57705\",\"57706\",\"57707\",\"57708\",\"57709\",\"57710\",\"57711\",\"57712\",\"57713\",\"58169\",\"58170\",\"58171\",\"58172\",\"58173\",\"58174\",\"58175\",\"58176\",\"58177\",\"58178\",\"58179\",\"58180\",\"60493\",\"60494\",\"60495\",\"60496\",\"61169\",\"61170\",\"61427\",\"61428\",\"61429\",\"61430\",\"61431\",\"61460\",\"61461\",\"61462\",\"61463\",\"61464\",\"61465\",\"61466\",\"61467\",\"64277\",\"64278\",\"64279\",\"64280\",\"64355\",\"64356\",\"64357\",\"64358\",\"64359\",\"64360\",\"64361\",\"64362\",\"64363\",\"64364\",\"64365\",\"64366\",\"64367\",\"64370\",\"64371\",\"64372\",\"64374\",\"64375\",\"64376\",\"64377\",\"64383\",\"64384\",\"64385\",\"64386\",\"64387\",\"64388\",\"64389\",\"64390\",\"64391\",\"64392\",\"64633\",\"64634\",\"64635\",\"64636\",\"64955\",\"64956\",\"64957\",\"64958\",\"64959\",\"64960\",\"64961\",\"64962\",\"64963\",\"64964\",\"64965\",\"64966\",\"64967\",\"65093\",\"65094\",\"65095\",\"65096\",\"65100\",\"65101\",\"65102\",\"65103\",\"65104\",\"65105\",\"65106\",\"65107\",\"65108\",\"65109\",\"65110\",\"65111\",\"65112\",\"65158\",\"65159\",\"65160\",\"65161\",\"65810\",\"65811\",\"65812\",\"65813\",\"65885\",\"65886\",\"65887\",\"65888\",\"65889\",\"65890\",\"65891\",\"65892\",\"65893\",\"65981\",\"65982\",\"65983\",\"65984\",\"65985\",\"65986\",\"65987\",\"65988\",\"65989\",\"65990\",\"65991\",\"65992\",\"65993\",\"65994\",\"65995\",\"66391\",\"66392\",\"66393\",\"66394\",\"66395\",\"66396\",\"66397\",\"66398\",\"66399\",\"66494\",\"66495\",\"66496\",\"66497\",\"66498\",\"66499\",\"66500\",\"66501\",\"66502\",\"66503\",\"66504\",\"66505\",\"66506\",\"66507\",\"66508\",\"66509\",\"66510\",\"66511\",\"66558\",\"66559\",\"66560\",\"66561\",\"66562\",\"66563\",\"66564\",\"66565\",\"66566\",\"66569\",\"66570\",\"66571\",\"66572\",\"66573\",\"66574\",\"66575\",\"66576\",\"66578\",\"66579\",\"66580\",\"66582\",\"66583\",\"66584\",\"66585\",\"66586\",\"66587\",\"66715\",\"66716\",\"66717\",\"66718\",\"66729\",\"66730\",\"66731\",\"66732\",\"66733\",\"66734\",\"66735\",\"66736\",\"66737\",\"66738\",\"66739\",\"66740\",\"66741\",\"66742\",\"66743\",\"66744\",\"66745\",\"66746\",\"66747\",\"66748\",\"66749\",\"66750\",\"66751\",\"66752\",\"66753\",\"66754\",\"66755\",\"66756\",\"66757\",\"66758\",\"66759\",\"66760\",\"66761\",\"66762\",\"66763\",\"66764\",\"66765\",\"66766\",\"66767\",\"66768\",\"66797\",\"66798\",\"66799\",\"66800\",\"66801\",\"66802\",\"66804\",\"66805\",\"66806\",\"66807\",\"66808\",\"66811\",\"66812\",\"66813\",\"66814\",\"66815\",\"66816\",\"66817\",\"66818\",\"66819\",\"66821\",\"66822\",\"66823\",\"66824\",\"66825\",\"66826\",\"66827\",\"66828\",\"66829\",\"66830\",\"66831\",\"66832\",\"66833\",\"66834\",\"66835\",\"66836\",\"66837\",\"66838\",\"66840\",\"66841\",\"66842\",\"66843\",\"66844\",\"66845\",\"66846\",\"66847\",\"66916\",\"66917\",\"66918\",\"66919\",\"66920\",\"66921\",\"66922\",\"66923\",\"66924\",\"66925\",\"66926\",\"66927\",\"66928\",\"66929\",\"66930\",\"66931\",\"66932\",\"66933\",\"66934\",\"66935\",\"66936\",\"66937\",\"66938\",\"66939\",\"66940\",\"66941\",\"66942\",\"66946\",\"66947\",\"66948\",\"66952\",\"66953\",\"66954\",\"66955\",\"66956\",\"66957\",\"66980\",\"66981\",\"66982\",\"66983\",\"66984\",\"66985\",\"66986\",\"66987\",\"66988\",\"66989\",\"66990\",\"66991\",\"66992\",\"66993\",\"66994\",\"66995\",\"66996\",\"66997\",\"66998\",\"66999\",\"67000\",\"67001\",\"67002\",\"67003\",\"67004\",\"67005\",\"67006\",\"67007\",\"67008\",\"67009\",\"67010\",\"67011\",\"67012\",\"67013\",\"67014\",\"67015\",\"67017\",\"67037\",\"67038\",\"67039\",\"67040\",\"67041\",\"67042\",\"67043\",\"67044\",\"67045\",\"67077\",\"67078\",\"67079\",\"67081\",\"67083\",\"67084\",\"67085\",\"67086\",\"67087\",\"67208\",\"67209\",\"67210\",\"67211\",\"67212\",\"67213\",\"67214\",\"67215\",\"67216\",\"67262\",\"67263\",\"67264\",\"67265\",\"67266\",\"67267\",\"67268\",\"67269\",\"67270\",\"67352\",\"67353\",\"67354\",\"67355\",\"67356\",\"67357\",\"67358\",\"67359\",\"67360\",\"67361\",\"67362\",\"67363\",\"67364\",\"67365\",\"67366\",\"67392\",\"67393\",\"67394\",\"67395\",\"67396\",\"67397\",\"67398\",\"67399\",\"67400\",\"67559\",\"67560\",\"67561\",\"67562\",\"67563\",\"67564\",\"67565\",\"67566\",\"67585\",\"67586\",\"67587\",\"67599\",\"67600\",\"67601\",\"67602\",\"67603\",\"67604\",\"67607\",\"67608\",\"67609\",\"67610\",\"67611\",\"67612\",\"67613\",\"67614\",\"67615\",\"67567\",\"67697\",\"67698\",\"67699\",\"67705\",\"67706\",\"67707\",\"67708\",\"67709\",\"67710\"]}",
      "addition": "",
      "created_work_no": "李勤",
      "created_unix": "2025-07-11 13:44:18",
      "updated_work_no": "",
      "updated_unix": "1752212790",
      "down_num": "1"
    },
    "file_size": 2285337,
    "platform_code": "AMAZON",
    "date_range": [
      "2025-07-10 00:00:00",
      "2025-07-10 23:59:59"
    ],
    "db_result": {
      "total_records": 4937,
      "insert_count": 0,
      "update_count": 0,
      "error_count": 0,
      "success": true,
      "message": "处理完成：新增0条，更新0条，失败0条",
      "conflict_recovery_attempts": 0,
      "csv_records_count": 4937,
      "table_statistics": {
        "total_count": 2606920,
        "today_count": 10000,
        "platform_stats": [
          {
            "platform": "Amazon",
            "count": 2606920
          }
        ],
        "last_updated": "2025-07-11T13:47:37.261678"
      },
      "processing_time": "2025-07-11T13:47:37.261687"
    },
    "timestamp": "2025-07-11T13:47:37.266052"
  }
}
{
  "timestamp": "2025-07-11T13:47:37.266703Z",
  "level": "INFO",
  "message": "导出统计信息",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "total_exports": 1,
    "success_count": 1,
    "fail_count": 0,
    "success_rate": 100.0,
    "total_files_size": 2285337,
    "last_export_time": "2025-07-11T13:47:37.266052",
    "last_login_time": "2025-07-11T13:44:15.365000",
    "has_valid_tokens": true
  }
}
{
  "timestamp": "2025-07-11T13:47:37.267803Z",
  "level": "INFO",
  "message": "任务完成，已清理 1 个临时文件",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "extra_data": {
    "cleaned_items": [
      "空目录: downloads"
    ],
    "total_cleaned": 1
  }
}
{
  "timestamp": "2025-07-11T13:47:37.267913Z",
  "level": "INFO",
  "message": "步骤执行完成: main_execution",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "main_execution_end",
  "execution_time": 230.06082105636597
}
{
  "timestamp": "2025-07-11T13:47:37.268052Z",
  "level": "INFO",
  "message": "执行异步后置处理",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "async_post_processing",
  "extra_data": {
    "result_keys": [
      "success",
      "message",
      "file_size",
      "export_info",
      "db_result",
      "used_existing_export",
      "smart_mode",
      "statistics"
    ],
    "result_size": 10053
  }
}
{
  "timestamp": "2025-07-11T13:47:37.268134Z",
  "level": "INFO",
  "message": "异步后置处理完成",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "async_post_processing_complete"
}
{
  "timestamp": "2025-07-11T13:47:37.268206Z",
  "level": "INFO",
  "message": "RPA任务执行完成 - 状态: success",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "task_summary",
  "execution_time": 230.345192,
  "extra_data": {
    "status": "success",
    "total_execution_time": 230.06210255622864,
    "start_time": "2025-07-11T05:43:46.922985Z",
    "end_time": "2025-07-11T05:47:37.268177Z",
    "result": {
      "success": true,
      "message": "导出并存储到数据库完成",
      "file_size": 2285337,
      "export_info": {
        "id": "3258",
        "platform_code": "AMAZON",
        "task_name": "【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205",
        "data_type": "【刊登成功列表】-列表导出",
        "path": "/upload/yibai_sale_center/excel/202507/11/【刊登成功列表】-列表导出-2025-07-11-13-44-18-3205.csv",
        "return_url": "/publish/publish_success/publish_success_export?export_type=downLoad&export_form=1&platform_code=AMAZON&export_id=3258",
        "start_time": "2025-07-11 13:44:18",
        "end_time": "2025-07-11 13:46:30",
        "status": "生成完成",
        "task_sum": "1",
        "done_task": "1",
        "rows_sum": "3368",
        "done_sum": "4937",
        "limit": "10000",
        "service_name": "end\\modules\\publish\\services\\Publish_success_service",
        "action_name": "get_export_attributes",
        "csv_header": "[\"\\u5e73\\u53f0\",\"\\u6765\\u6e90\",\"SKU\",\"SPU\",\"sellerSKU\\uff08\\u7236\\uff09\",\"sellerSKU\\uff08\\u5b50\\uff09\",\"Asin\",\"UPC\",\"EAN\",\"\\u54c1\\u724c\",\"\\u96f6\\u4ef6\\u53f7\",\"\\u53d8\\u4f53\\u7c7b\\u578b\",\"\\u53d8\\u4f53\\u540d\\u79f01\",\"\\u53d8\\u4f53\\u540d\\u79f02\",\"\\u53d8\\u4f53\\u540d\\u79f03\",\"\\u4ef7\\u683c\\uff08\\u672c\\u5e01\\uff09\",\"\\u6570\\u91cf\",\"\\u8d26\\u53f7\",\"\\u7ad9\\u70b9\",\"\\u5927\\u4ed3\",\"nodeID\",\"\\u5e73\\u53f0\\u7c7b\\u76ee\\u94fe\",\"\\u4ea7\\u54c1\\u7ebf\",\"\\u5546\\u54c1\\u7c7b\\u578b\",\"\\u4e2d\\u6587\\u6807\\u9898\",\"\\u9500\\u552e\\u5458\",\"\\u5e73\\u53f0\\u8fd4\\u56de\\u4fe1\\u606f\",\"\\u94fe\\u63a5\\u5728\\u7ebf\\u72b6\\u6001\",\"\\u521b\\u5efa\\u4eba\",\"\\u521b\\u5efa\\u65f6\\u95f4\",\"\\u6700\\u540e\\u62c9\\u53d6\\u65f6\\u95f4\",\"\\u540c\\u6b65\\u65f6\\u95f4\"]",
        "custom": "{\"platform_code\":\"AMAZON\",\"created_unix\":[1752076800,1752163199],\"export_form\":\"1\",\"jwt_token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzZXJ2aWNlUHJvdmlkZXJJZCI6MiwiYWNjb3VudE5hbWUiOiIxODI1ODExODEwMCIsInNvdXJjZSI6MSwidXNlck5hbWUiOiLmnY7li6QiLCJ1dWlkIjoiYmNjMzA3ZDYtOTQ0Yi00MjIyLWE3NzUtNTYwN2ExNjYxMTllIiwiYXV0aG9yaXR5SWQiOjE5OTI2LCJvcmdhbml6YXRpb25Db2RlIjoi5rex5Zyz5aWH54K55re76LSi5oqV6LWE5ZCI5LyZ5LyB5Lia77yI5pyJ6ZmQ5ZCI5LyZ77yJIiwibWVyY2hhbnRJZCI6IjYwNTciLCJvcmdDb2RlIjoib3JnXzAwMDAxIiwidXNlclR5cGUiOjEsInJlbGV2YW5jZUlkIjoxLCJleHAiOjE3NTIyNzAyNTEsImp0aSI6Ijg3OGU1YzgxLTA1MzAtNGZmMC04MGM1LWFmNGIzYWVlZDc1MCIsImZ4X3VzZXJfaWQiOiIxOTMxMSIsImZ4X3VzZXJfbmFtZSI6IuadjuWLpCIsImZ4X2Rpc3RyaWJ1dG9yX2lkIjoiNjA1NyJ9.NUcJunZ8ymqlBswHxetTXOLa7wLVWO_hCY-GsgbvKUU\",\"anticlimb_verify_code\":\"\",\"account_id\":[\"47680\",\"47681\",\"47682\",\"47683\",\"47684\",\"47685\",\"47686\",\"47687\",\"47688\",\"47689\",\"47690\",\"47691\",\"47692\",\"47708\",\"47709\",\"47710\",\"47711\",\"47712\",\"47713\",\"47714\",\"47715\",\"47716\",\"47717\",\"47718\",\"47719\",\"47720\",\"47721\",\"47722\",\"47723\",\"47724\",\"47725\",\"47726\",\"47727\",\"47728\",\"47729\",\"47730\",\"47731\",\"47732\",\"47733\",\"47734\",\"47735\",\"47736\",\"47737\",\"47738\",\"47739\",\"47740\",\"47741\",\"47742\",\"47743\",\"47744\",\"47745\",\"47757\",\"47758\",\"47840\",\"47841\",\"47842\",\"47843\",\"47844\",\"47845\",\"47846\",\"47847\",\"47848\",\"47849\",\"47850\",\"47851\",\"47852\",\"47853\",\"47854\",\"47855\",\"47856\",\"48091\",\"48092\",\"48093\",\"48094\",\"48095\",\"48096\",\"48097\",\"48098\",\"48099\",\"48100\",\"48101\",\"48102\",\"48103\",\"48104\",\"48105\",\"48126\",\"48127\",\"48128\",\"48129\",\"48130\",\"48131\",\"48132\",\"48133\",\"48134\",\"48135\",\"48136\",\"48137\",\"48277\",\"48279\",\"48280\",\"48281\",\"48282\",\"48283\",\"48284\",\"48292\",\"48293\",\"48294\",\"48297\",\"48298\",\"48299\",\"48300\",\"48301\",\"48302\",\"48303\",\"48304\",\"48305\",\"48306\",\"48307\",\"48308\",\"48309\",\"48310\",\"48311\",\"48312\",\"48313\",\"48314\",\"48315\",\"48316\",\"48317\",\"48318\",\"48319\",\"48320\",\"48321\",\"48322\",\"48323\",\"48324\",\"48325\",\"48326\",\"48327\",\"48328\",\"48329\",\"48330\",\"48331\",\"48332\",\"48333\",\"48334\",\"48335\",\"48336\",\"48337\",\"48338\",\"48339\",\"48340\",\"48341\",\"48342\",\"48343\",\"48344\",\"48345\",\"48346\",\"48347\",\"48348\",\"48371\",\"48372\",\"48373\",\"48374\",\"48785\",\"48786\",\"48787\",\"48796\",\"48797\",\"48834\",\"48903\",\"48931\",\"48932\",\"48933\",\"48934\",\"48935\",\"48936\",\"48937\",\"48938\",\"48939\",\"48940\",\"48941\",\"48942\",\"49033\",\"49034\",\"49035\",\"49036\",\"49360\",\"49447\",\"49601\",\"49602\",\"49603\",\"49604\",\"49605\",\"49606\",\"49607\",\"49608\",\"49609\",\"49610\",\"49910\",\"49911\",\"49912\",\"49913\",\"49914\",\"50168\",\"50444\",\"50445\",\"50446\",\"50447\",\"50449\",\"50450\",\"50451\",\"50452\",\"50453\",\"50454\",\"50455\",\"50456\",\"50457\",\"50458\",\"50459\",\"50460\",\"50461\",\"50462\",\"50463\",\"50529\",\"50530\",\"50531\",\"50678\",\"50679\",\"50680\",\"50681\",\"50682\",\"50683\",\"50684\",\"50685\",\"50686\",\"50687\",\"50688\",\"50689\",\"50690\",\"50691\",\"50878\",\"50879\",\"50880\",\"50881\",\"50882\",\"50883\",\"50884\",\"50885\",\"50886\",\"50937\",\"51658\",\"51659\",\"51660\",\"51661\",\"51662\",\"51663\",\"51664\",\"51665\",\"51666\",\"51667\",\"51668\",\"51669\",\"51670\",\"51671\",\"51672\",\"51673\",\"51674\",\"51675\",\"51676\",\"51677\",\"51678\",\"51679\",\"51680\",\"51681\",\"51682\",\"51683\",\"51684\",\"51685\",\"51686\",\"51687\",\"51688\",\"51689\",\"51690\",\"51692\",\"51693\",\"51694\",\"51695\",\"51736\",\"51737\",\"51738\",\"51739\",\"51740\",\"51741\",\"51742\",\"51743\",\"51744\",\"51745\",\"51746\",\"51747\",\"51748\",\"51749\",\"51750\",\"51751\",\"51752\",\"51753\",\"51754\",\"51755\",\"51756\",\"51757\",\"51758\",\"51869\",\"51870\",\"51871\",\"51872\",\"51873\",\"51874\",\"51875\",\"51876\",\"51877\",\"52813\",\"52814\",\"52815\",\"53033\",\"53034\",\"53035\",\"53036\",\"53037\",\"53038\",\"53039\",\"53040\",\"53041\",\"53843\",\"53844\",\"53845\",\"53846\",\"53847\",\"53848\",\"53849\",\"53850\",\"53851\",\"53852\",\"53853\",\"48788\",\"48789\",\"48790\",\"48791\",\"48792\",\"48793\",\"48794\",\"48795\",\"53854\",\"54355\",\"54356\",\"54357\",\"54358\",\"54359\",\"54360\",\"54361\",\"54362\",\"54363\",\"54898\",\"54899\",\"55888\",\"55889\",\"55890\",\"55933\",\"55934\",\"55935\",\"55956\",\"55957\",\"55958\",\"55959\",\"55960\",\"55961\",\"55962\",\"55963\",\"55964\",\"55965\",\"55966\",\"55967\",\"56027\",\"56028\",\"56029\",\"56030\",\"56031\",\"56032\",\"56033\",\"56034\",\"56035\",\"56036\",\"56037\",\"56038\",\"56039\",\"56695\",\"57496\",\"57700\",\"57701\",\"57702\",\"57703\",\"57704\",\"57705\",\"57706\",\"57707\",\"57708\",\"57709\",\"57710\",\"57711\",\"57712\",\"57713\",\"58169\",\"58170\",\"58171\",\"58172\",\"58173\",\"58174\",\"58175\",\"58176\",\"58177\",\"58178\",\"58179\",\"58180\",\"60493\",\"60494\",\"60495\",\"60496\",\"61169\",\"61170\",\"61427\",\"61428\",\"61429\",\"61430\",\"61431\",\"61460\",\"61461\",\"61462\",\"61463\",\"61464\",\"61465\",\"61466\",\"61467\",\"64277\",\"64278\",\"64279\",\"64280\",\"64355\",\"64356\",\"64357\",\"64358\",\"64359\",\"64360\",\"64361\",\"64362\",\"64363\",\"64364\",\"64365\",\"64366\",\"64367\",\"64370\",\"64371\",\"64372\",\"64374\",\"64375\",\"64376\",\"64377\",\"64383\",\"64384\",\"64385\",\"64386\",\"64387\",\"64388\",\"64389\",\"64390\",\"64391\",\"64392\",\"64633\",\"64634\",\"64635\",\"64636\",\"64955\",\"64956\",\"64957\",\"64958\",\"64959\",\"64960\",\"64961\",\"64962\",\"64963\",\"64964\",\"64965\",\"64966\",\"64967\",\"65093\",\"65094\",\"65095\",\"65096\",\"65100\",\"65101\",\"65102\",\"65103\",\"65104\",\"65105\",\"65106\",\"65107\",\"65108\",\"65109\",\"65110\",\"65111\",\"65112\",\"65158\",\"65159\",\"65160\",\"65161\",\"65810\",\"65811\",\"65812\",\"65813\",\"65885\",\"65886\",\"65887\",\"65888\",\"65889\",\"65890\",\"65891\",\"65892\",\"65893\",\"65981\",\"65982\",\"65983\",\"65984\",\"65985\",\"65986\",\"65987\",\"65988\",\"65989\",\"65990\",\"65991\",\"65992\",\"65993\",\"65994\",\"65995\",\"66391\",\"66392\",\"66393\",\"66394\",\"66395\",\"66396\",\"66397\",\"66398\",\"66399\",\"66494\",\"66495\",\"66496\",\"66497\",\"66498\",\"66499\",\"66500\",\"66501\",\"66502\",\"66503\",\"66504\",\"66505\",\"66506\",\"66507\",\"66508\",\"66509\",\"66510\",\"66511\",\"66558\",\"66559\",\"66560\",\"66561\",\"66562\",\"66563\",\"66564\",\"66565\",\"66566\",\"66569\",\"66570\",\"66571\",\"66572\",\"66573\",\"66574\",\"66575\",\"66576\",\"66578\",\"66579\",\"66580\",\"66582\",\"66583\",\"66584\",\"66585\",\"66586\",\"66587\",\"66715\",\"66716\",\"66717\",\"66718\",\"66729\",\"66730\",\"66731\",\"66732\",\"66733\",\"66734\",\"66735\",\"66736\",\"66737\",\"66738\",\"66739\",\"66740\",\"66741\",\"66742\",\"66743\",\"66744\",\"66745\",\"66746\",\"66747\",\"66748\",\"66749\",\"66750\",\"66751\",\"66752\",\"66753\",\"66754\",\"66755\",\"66756\",\"66757\",\"66758\",\"66759\",\"66760\",\"66761\",\"66762\",\"66763\",\"66764\",\"66765\",\"66766\",\"66767\",\"66768\",\"66797\",\"66798\",\"66799\",\"66800\",\"66801\",\"66802\",\"66804\",\"66805\",\"66806\",\"66807\",\"66808\",\"66811\",\"66812\",\"66813\",\"66814\",\"66815\",\"66816\",\"66817\",\"66818\",\"66819\",\"66821\",\"66822\",\"66823\",\"66824\",\"66825\",\"66826\",\"66827\",\"66828\",\"66829\",\"66830\",\"66831\",\"66832\",\"66833\",\"66834\",\"66835\",\"66836\",\"66837\",\"66838\",\"66840\",\"66841\",\"66842\",\"66843\",\"66844\",\"66845\",\"66846\",\"66847\",\"66916\",\"66917\",\"66918\",\"66919\",\"66920\",\"66921\",\"66922\",\"66923\",\"66924\",\"66925\",\"66926\",\"66927\",\"66928\",\"66929\",\"66930\",\"66931\",\"66932\",\"66933\",\"66934\",\"66935\",\"66936\",\"66937\",\"66938\",\"66939\",\"66940\",\"66941\",\"66942\",\"66946\",\"66947\",\"66948\",\"66952\",\"66953\",\"66954\",\"66955\",\"66956\",\"66957\",\"66980\",\"66981\",\"66982\",\"66983\",\"66984\",\"66985\",\"66986\",\"66987\",\"66988\",\"66989\",\"66990\",\"66991\",\"66992\",\"66993\",\"66994\",\"66995\",\"66996\",\"66997\",\"66998\",\"66999\",\"67000\",\"67001\",\"67002\",\"67003\",\"67004\",\"67005\",\"67006\",\"67007\",\"67008\",\"67009\",\"67010\",\"67011\",\"67012\",\"67013\",\"67014\",\"67015\",\"67017\",\"67037\",\"67038\",\"67039\",\"67040\",\"67041\",\"67042\",\"67043\",\"67044\",\"67045\",\"67077\",\"67078\",\"67079\",\"67081\",\"67083\",\"67084\",\"67085\",\"67086\",\"67087\",\"67208\",\"67209\",\"67210\",\"67211\",\"67212\",\"67213\",\"67214\",\"67215\",\"67216\",\"67262\",\"67263\",\"67264\",\"67265\",\"67266\",\"67267\",\"67268\",\"67269\",\"67270\",\"67352\",\"67353\",\"67354\",\"67355\",\"67356\",\"67357\",\"67358\",\"67359\",\"67360\",\"67361\",\"67362\",\"67363\",\"67364\",\"67365\",\"67366\",\"67392\",\"67393\",\"67394\",\"67395\",\"67396\",\"67397\",\"67398\",\"67399\",\"67400\",\"67559\",\"67560\",\"67561\",\"67562\",\"67563\",\"67564\",\"67565\",\"67566\",\"67585\",\"67586\",\"67587\",\"67599\",\"67600\",\"67601\",\"67602\",\"67603\",\"67604\",\"67607\",\"67608\",\"67609\",\"67610\",\"67611\",\"67612\",\"67613\",\"67614\",\"67615\",\"67567\",\"67697\",\"67698\",\"67699\",\"67705\",\"67706\",\"67707\",\"67708\",\"67709\",\"67710\"]}",
        "addition": "",
        "created_work_no": "李勤",
        "created_unix": "2025-07-11 13:44:18",
        "updated_work_no": "",
        "updated_unix": "1752212790",
        "down_num": "1"
      },
      "db_result": {
        "total_records": 4937,
        "insert_count": 0,
        "update_count": 0,
        "error_count": 0,
        "success": true,
        "message": "处理完成：新增0条，更新0条，失败0条",
        "conflict_recovery_attempts": 0,
        "csv_records_count": 4937,
        "table_statistics": {
          "total_count": 2606920,
          "today_count": 10000,
          "platform_stats": [
            {
              "platform": "Amazon",
              "count": 2606920
            }
          ],
          "last_updated": "2025-07-11T13:47:37.261678"
        },
        "processing_time": "2025-07-11T13:47:37.261687"
      },
      "used_existing_export": false,
      "smart_mode": false,
      "statistics": {
        "total_exports": 1,
        "success_count": 1,
        "fail_count": 0,
        "success_rate": 100.0,
        "total_files_size": 2285337,
        "last_export_time": "2025-07-11T13:47:37.266052",
        "last_login_time": "2025-07-11T13:44:15.365000",
        "has_valid_tokens": true
      }
    },
    "operation_count": 0,
    "error_count": 0,
    "retry_count": 0,
    "step_times": {}
  }
}
{
  "timestamp": "2025-07-11T13:47:37.268687Z",
  "level": "INFO",
  "message": "执行异步最终清理",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "async_final_cleanup"
}
{
  "timestamp": "2025-07-11T13:47:37.364890Z",
  "level": "INFO",
  "message": "Playwright驱动已关闭",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "driver_closed",
  "extra_data": {
    "operations": 3,
    "errors": 0,
    "screenshots": 0,
    "execution_time": 227.53788566589355
  }
}
{
  "timestamp": "2025-07-11T13:47:37.365046Z",
  "level": "INFO",
  "message": "Web驱动已关闭",
  "logger": "rpa.export_publish_list.export_publish_list",
  "module": "logger",
  "function": "_log_with_context",
  "line": 80,
  "task_id": "export_publish_list_export_publish_list_20250711_134346_922",
  "business_type": "export_publish_list",
  "script_name": "export_publish_list",
  "step": "web_driver_cleanup"
}
{
  "timestamp": "2025-07-11T13:47:37.365105Z",
  "level": "INFO",
  "message": "任务执行成功",
  "logger": "__main__",
  "module": "main",
  "function": "main",
  "line": 294
}

✅ 任务执行成功
📄 消息: 导出并存储到数据库完成

💾 数据库操作成功:
   📊 总记录数: 4937
   ➕ 新增记录: 0
   🔄 更新记录: 0
   ⏭️ 跳过记录: 0
   📈 表总记录数: 2606920
   📅 今日新增: 10000
📊 文件大小: 2285337 字节
📈 成功率: 100.0%
📦 总导出次数: 1
