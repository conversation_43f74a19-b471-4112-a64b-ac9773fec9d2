@echo off
chcp 65001 > nul
echo 🔧 构建浏览器版本修复基础镜像
echo ========================================

echo 📋 修复内容：
echo   • 清理旧版本浏览器（chromium-1148）
echo   • 保留最新兼容版本（chromium-1179）
echo   • 解决浏览器版本冲突问题
echo   • 保持原有requirements.txt不变
echo.

echo 🏗️ 开始构建修复版基础镜像...
docker build -t rpa-k8s-base:latest -f Dockerfile.base.fixed .

if %errorlevel% equ 0 (
    echo.
    echo ✅ 浏览器版本修复基础镜像构建成功！
    echo.
    echo 📊 检查镜像大小对比...
    echo --- 旧版本 ---
    docker images | findstr "rpa-k8s-base:latest"
    echo --- 修复版本 ---
    docker images | findstr "rpa-k8s-base:latest"
    echo.
    echo 🧪 验证浏览器版本修复...
    docker run --rm rpa-k8s-base:latest bash -c "ls -la /home/<USER>/.cache/ms-playwright/ && echo '=== 浏览器版本检查 ===' && find /home/<USER>/.cache/ms-playwright/ -name 'chromium*' -type d"
    echo.
    echo 🎯 接下来可以：
    echo   1. 推送修复版基础镜像到镜像仓库
    echo   2. 更新业务镜像使用修复版基础镜像
    echo   3. 重新构建所有业务镜像
) else (
    echo ❌ 构建失败
    exit /b 1
)

pause 