"""
异步亿迈系统登录管理器

这是YimaiLoginManager的现代异步版本，专为Playwright驱动设计。
主要功能：
1. 智能登录状态检测 - 自动判断是否已登录，避免重复登录
2. 自动登录执行 - 支持账号密码自动填写和提交
3. 登录结果验证 - 验证登录是否成功跳转到首页
4. 异步操作支持 - 完全异步化，支持高性能并发
5. 增强错误处理 - 详细的错误信息和恢复机制
6. 配置灵活管理 - 支持环境变量和配置文件两种配置方式

配置说明：
- 支持环境变量配置（推荐）：YIMAI_USERNAME, YIMAI_PASSWORD
- 支持配置文件配置：在业务配置中设置YIMAI_CONFIG
- 配置优先级：环境变量 > 业务配置 > 默认配置

使用方式：
    login_manager = AsyncYimaiLoginManager(logger, business_type, script_name)
    async with web_driver_context() as driver:
        success = await login_manager.ensure_login(driver)
        if success:
            # 继续业务操作
"""

import asyncio
import time
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urlparse

from app.config.config import ConfigManager
from app.core.web_driver.base_driver import BaseWebDriver


class AsyncYimaiLoginManager:
    """
    异步亿迈系统登录管理器
    
    提供完整的亿迈系统登录功能，包括：
    - 智能登录状态检测
    - 自动登录执行
    - 登录结果验证
    - 错误处理和重试
    """
    
    def __init__(self, logger, business_type: str = None, script_name: str = None):
        """
        初始化异步亿迈登录管理器
        
        Args:
            logger: 日志记录器实例
            business_type: 业务类型，用于获取业务特定配置
            script_name: 脚本名称，用于获取脚本特定配置
        """
        self.logger = logger
        self.business_type = business_type
        self.script_name = script_name
        
        # 从配置管理器获取亿迈系统配置
        # 这里会按优先级获取：环境变量 > 业务配置 > 项目配置 > 默认值
        self.config = self._load_yimai_config()
        
        # 登录状态追踪
        # 用于避免重复登录和优化性能
        self._last_login_check_time = 0
        self._last_login_status = False
        self._login_cache_duration = 300  # 登录状态缓存5分钟
        
        # 用户信息缓存
        # 存储从登录接口获取的用户信息
        self._user_info = {}
        self._user_id = None
        
        # 性能统计
        self._login_attempts = 0
        self._login_successes = 0
        self._login_failures = 0
        
        self.logger.info("异步亿迈登录管理器初始化完成", step="yimai_login_init", extra_data={
            "business_type": business_type,
            "script_name": script_name,
            "has_username": bool(self.config.get('username')),
            "has_password": bool(self.config.get('password')),
            "base_url": self.config.get('base_url'),
            "cache_duration": self._login_cache_duration
        })
    
    def _load_yimai_config(self) -> Dict[str, Any]:
        """
        加载亿迈系统配置
        
        配置加载优先级（从高到低）：
        1. 环境变量：YIMAI_USERNAME, YIMAI_PASSWORD
        2. 业务配置文件：YIMAI_CONFIG 配置项
        3. 项目配置文件：default.json 中的 YIMAI_CONFIG
        4. 代码默认值
        
        Returns:
            Dict[str, Any]: 亿迈系统配置字典
        """
        # 获取默认配置
        default_config = self._get_default_config()
        
        # 从配置管理器获取项目级配置
        project_config = ConfigManager.get_json_config(
            'YIMAI_CONFIG', 
            {}, 
            self.business_type, 
            self.script_name
        )
        
        # 合并项目配置到默认配置
        config = {**default_config, **project_config}
        
        # 从环境变量获取登录凭据（优先级最高）
        # 这是推荐的生产环境配置方式，确保安全性
        username = ConfigManager.get_config('YIMAI_USERNAME', '', self.business_type, self.script_name)
        password = ConfigManager.get_config('YIMAI_PASSWORD', '', self.business_type, self.script_name)
        
        if username:
            config['username'] = username
            self.logger.debug("从环境变量获取亿迈用户名", step="config_load")
        
        if password:
            config['password'] = password
            self.logger.debug("从环境变量获取亿迈密码", step="config_load")
        
        # 从业务配置获取其他设置
        config['login_timeout'] = ConfigManager.get_int_config(
            'YIMAI_LOGIN_TIMEOUT', 
            config['login_timeout'], 
            self.business_type, 
            self.script_name
        )
        
        config['page_load_wait'] = ConfigManager.get_int_config(
            'PLAYWRIGHT_PAGE_LOAD_WAIT', 
            config['page_load_wait'], 
            self.business_type, 
            self.script_name
        )
        
        return config
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'login_url': 'https://dcmmaster.yibainetwork.com/#/login_page',
            'base_url': 'https://dcmmaster.yibainetwork.com/',
            'login_page': 'https://dcmmaster.yibainetwork.com/#/login_page',
            'home_page': 'https://dcmmaster.yibainetwork.com/#/home_page',
            'username': '***********',
            'password': 'Aa123321',
            'login_timeout': 30,  # 向后兼容
            'page_load_wait': 5,  # 向后兼容
            'element_wait': {
                'username_timeout': 10000,
                'password_timeout': 10000,
                'button_timeout': 10000
            },
            'login_wait': {
                'max_wait_time': 30,  # 最大等待时间（秒）
                'check_interval': 2,  # 检测间隔（秒）
            },
            'selectors': {
                'username_input': 'input[placeholder*="用户名"], input[name="username"], input[type="text"]',
                'password_input': 'input[placeholder*="密码"], input[name="password"], input[type="password"]',
                'login_button': 'button.el-button--primary:has-text("登录")',
                'error_message': '.el-message--error, .error-message, .alert-danger',
                'user_info': '.user-info, .user-name, .username, .header-user',
                'home_indicator': '.nav, .menu, .sidebar, .dashboard, .main-content',
                'logout_button': 'button:has-text("退出"), button:has-text("登出")',
                'loading_mask': '.loading-mask, .el-loading-mask'
            },
            'credentials': {
                'username': '***********',
                'password': 'Aa123321'
            }
        }
    
    def get_login_config(self) -> Dict[str, Any]:
        """
        获取登录配置信息（用于验证和调试）
        
        Returns:
            Dict[str, Any]: 登录配置信息，不包含敏感信息
        """
        return {
            "base_url": self.config.get('base_url'),
            "login_page": self.config.get('login_page'),
            "home_page": self.config.get('home_page'),
            "has_username": bool(self.config.get('username')),
            "has_password": bool(self.config.get('password')),
            "login_timeout": self.config.get('login_timeout'),
            "page_load_wait": self.config.get('page_load_wait'),
            "statistics": {
                "login_attempts": self._login_attempts,
                "login_successes": self._login_successes,
                "login_failures": self._login_failures,
                "success_rate": (self._login_successes / max(self._login_attempts, 1)) * 100
            }
        }
    
    def get_user_id(self) -> Optional[str]:
        """
        获取登录用户的ID
        
        Returns:
            Optional[str]: 用户ID，如果未获取到则返回None
        """
        return self._user_id
    
    def get_user_info(self) -> Dict[str, Any]:
        """
        获取完整的用户信息
        
        Returns:
            Dict[str, Any]: 用户信息字典
        """
        return self._user_info.copy()

    async def _setup_network_interception(self, driver: BaseWebDriver):
        """
        设置网络请求拦截，用于获取登录响应数据
        
        Args:
            driver: Web驱动实例
        """
        try:
            if hasattr(driver, 'page') and driver.page:
                # 设置响应拦截
                async def handle_response(response):
                    try:
                        url = response.url
                        # 拦截可能的登录接口
                        if any(keyword in url.lower() for keyword in ['login', 'user', 'auth', 'signin']):
                            if response.status == 200:
                                try:
                                    response_text = await response.text()
                                    self.logger.debug(f"拦截到登录相关响应: {url}", extra_data={
                                        'status': response.status,
                                        'url': url,
                                        'response_preview': response_text[:200] if response_text else None
                                    })
                                    
                                    # 尝试解析JSON响应
                                    if response_text:
                                        import json
                                        try:
                                            data = json.loads(response_text)
                                            await self._process_login_response(data, url)
                                        except json.JSONDecodeError:
                                            pass
                                            
                                except Exception as e:
                                    self.logger.debug(f"处理响应时异常: {e}")
                                    
                    except Exception as e:
                        self.logger.debug(f"响应拦截处理异常: {e}")
                
                # 启用响应拦截
                driver.page.on('response', handle_response)
                self.logger.debug("网络请求拦截已启用")
                
        except Exception as e:
            self.logger.warning(f"设置网络拦截时异常: {e}")

    async def _process_login_response(self, data: Dict[str, Any], url: str):
        """
        处理登录响应数据，提取用户信息
        
        Args:
            data: 响应数据
            url: 请求URL
        """
        try:
            # 检查是否包含用户信息
            user_info = None
            
            # 方式1: 直接在根级别查找用户信息
            if isinstance(data, dict):
                # 查找包含id字段的对象
                if 'id' in data and 'account' in data:
                    user_info = data
                elif 'data' in data and isinstance(data['data'], dict):
                    # 方式2: 在data字段中查找
                    if 'id' in data['data'] and 'account' in data['data']:
                        user_info = data['data']
                elif 'user' in data and isinstance(data['user'], dict):
                    # 方式3: 在user字段中查找
                    if 'id' in data['user']:
                        user_info = data['user']
            
            if user_info and 'id' in user_info:
                self._user_info = user_info
                self._user_id = str(user_info['id'])
                
                self.logger.info("成功获取用户信息", extra_data={
                    'user_id': self._user_id,
                    'account': user_info.get('account'),
                    'account_name': user_info.get('account_name'),
                    'distributor_id': user_info.get('distributor_id'),
                    'url': url
                })
                
        except Exception as e:
            self.logger.warning(f"处理登录响应时异常: {e}")

    async def ensure_login(self, driver: BaseWebDriver) -> bool:
        """
        确保用户已登录亿迈系统
        
        这是主要的登录入口方法，会智能处理登录流程：
        1. 检查当前登录状态
        2. 如果未登录，执行自动登录
        3. 验证登录结果
        4. 返回最终状态
        
        Args:
            driver: Web驱动实例
            
        Returns:
            bool: 登录是否成功
        """
        try:
            self.logger.info("开始确保亿迈系统登录状态", step="ensure_login_start")
            
            # 第一步：检查当前登录状态
            # 如果已经登录，直接返回成功，避免不必要的操作
            if await self._check_login_status(driver):
                self.logger.info("用户已登录亿迈系统", step="already_logged_in")
                return True
            
            # 第二步：执行登录操作
            # 如果未登录，尝试自动登录
            self.logger.info("用户未登录，开始执行登录操作", step="start_login")
            login_success = await self._perform_login(driver)
            
            if login_success:
                # 第三步：验证登录结果
                # 登录操作完成后，再次检查状态确保成功
                if await self._verify_login_success(driver):
                    self._login_successes += 1
                    self.logger.info("亿迈系统登录成功", step="login_success", extra_data={
                        "attempts": self._login_attempts,
                        "successes": self._login_successes
                    })
                    return True
                else:
                    self._login_failures += 1
                    self.logger.error("登录操作完成但验证失败", step="login_verification_failed")
                    return False
            else:
                self._login_failures += 1
                self.logger.error("登录操作执行失败", step="login_operation_failed")
                return False
                
        except Exception as e:
            self._login_failures += 1
            self.logger.error(f"确保登录过程中发生异常: {str(e)}", step="ensure_login_error", exception=e)
            return False
    
    async def _check_login_status(self, driver: BaseWebDriver) -> bool:
        """
        检查当前登录状态
        
        通过多种方式检测用户是否已登录：
        1. URL检查 - 是否在首页或其他认证页面
        2. 元素检查 - 是否存在登录后才有的元素
        3. 缓存检查 - 利用缓存避免频繁检查
        
        Args:
            driver: Web驱动实例
            
        Returns:
            bool: 是否已登录
        """
        try:
            # 缓存检查 - 如果最近检查过且结果为已登录，直接返回
            current_time = time.time()
            if (current_time - self._last_login_check_time < self._login_cache_duration and 
                self._last_login_status):
                self.logger.debug("使用缓存的登录状态", step="login_status_cached")
                return True
            
            self.logger.debug("检查亿迈系统登录状态", step="check_login_status")
            
            # 获取当前页面URL
            current_url = await driver.get_current_url()
            self.logger.debug(f"当前页面URL: {current_url}", step="current_url_check")
            
            # URL检查 - 如果当前在登录页面，说明未登录
            if self._is_login_page(current_url):
                self.logger.debug("当前在登录页面，用户未登录", step="on_login_page")
                self._update_login_cache(False)
                return False
            
            # URL检查 - 如果当前在首页或其他认证页面，可能已登录
            if self._is_authenticated_page(current_url):
                self.logger.debug("当前在认证页面，检查登录元素", step="on_auth_page")
                
                # 元素检查 - 查找登录后才有的元素
                selectors = self.config['selectors']
                
                # 检查用户信息元素
                user_info_exists = await driver.is_visible(selectors['user_info'], timeout=3000)
                if user_info_exists:
                    self.logger.debug("找到用户信息元素，用户已登录", step="user_info_found")
                    self._update_login_cache(True)
                    return True
                
                # 检查首页指示元素
                home_indicator_exists = await driver.is_visible(selectors['home_indicator'], timeout=3000)
                if home_indicator_exists:
                    self.logger.debug("找到首页指示元素，用户已登录", step="home_indicator_found")
                    self._update_login_cache(True)
                    return True
                
                # 检查退出按钮（登录后才会显示）
                logout_button_exists = await driver.is_visible(selectors['logout_button'], timeout=3000)
                if logout_button_exists:
                    self.logger.debug("找到退出按钮，用户已登录", step="logout_button_found")
                    self._update_login_cache(True)
                    return True
            
            # 如果所有检查都未通过，认为未登录
            self.logger.debug("登录状态检查未通过，用户未登录", step="login_check_failed")
            self._update_login_cache(False)
            return False
            
        except Exception as e:
            self.logger.warning(f"检查登录状态时发生异常: {str(e)}", step="check_login_status_error")
            # 异常情况下认为未登录，确保安全
            self._update_login_cache(False)
            return False
    
    def _is_login_page(self, url: str) -> bool:
        """
        判断是否为登录页面
        
        Args:
            url: 当前页面URL
            
        Returns:
            bool: 是否为登录页面
        """
        if not url:
            return False
        
        login_page = self.config['login_page']
        
        # 精确匹配
        if url == login_page:
            return True
        
        # 路径匹配（忽略参数）
        parsed_url = urlparse(url)
        parsed_login = urlparse(login_page)
        
        if (parsed_url.netloc == parsed_login.netloc and 
            parsed_url.path == parsed_login.path and
            'login' in parsed_url.fragment):
            return True
        
        return False
    
    def _is_authenticated_page(self, url: str) -> bool:
        """
        判断是否为需要认证的页面
        
        Args:
            url: 当前页面URL
            
        Returns:
            bool: 是否为认证页面
        """
        if not url:
            return False
        
        base_url = self.config['base_url']
        home_page = self.config['home_page']
        
        # 如果在首页，肯定是认证页面
        if url.startswith(home_page):
            return True
        
        # 如果在基础域名下且不是登录页面，可能是认证页面
        if url.startswith(base_url) and not self._is_login_page(url):
            return True
        
        return False
    
    def _update_login_cache(self, status: bool):
        """
        更新登录状态缓存
        
        Args:
            status: 登录状态
        """
        self._last_login_check_time = time.time()
        self._last_login_status = status
    
    async def _perform_login(self, driver: BaseWebDriver) -> bool:
        """
        执行登录操作
        
        完整的登录流程：
        1. 导航到登录页面
        2. 等待页面加载完成
        3. 填写用户名和密码
        4. 点击登录按钮
        5. 等待登录完成
        
        Args:
            driver: Web驱动实例
            
        Returns:
            bool: 登录操作是否成功
        """
        try:
            self._login_attempts += 1
            self.logger.info(f"开始执行登录操作 (第{self._login_attempts}次尝试)", step="perform_login_start")
            
            # 验证登录凭据
            username = self.config.get('username')
            password = self.config.get('password')
            
            if not username or not password:
                self.logger.error("登录凭据不完整", step="credentials_missing", extra_data={
                    "has_username": bool(username),
                    "has_password": bool(password)
                })
                return False
            
            # 第一步：设置网络拦截以获取用户信息
            await self._setup_network_interception(driver)
            
            # 第二步：导航到登录页面
            login_page = self.config['login_page']
            self.logger.info(f"导航到登录页面: {login_page}", step="navigate_to_login")
            await driver.navigate_to(login_page)
            
            # 等待页面加载
            await driver.wait_for_page_load()
            await asyncio.sleep(self.config['page_load_wait'])
            
            # 第三步：等待并处理加载遮罩
            await self._wait_for_page_ready(driver)
            
            # 第四步：填写登录表单
            if not await self._fill_login_form(driver, username, password):
                return False
            
            # 第五步：点击登录按钮
            if not await self._click_login_button(driver):
                return False
            
            # 第六步：等待登录完成
            if not await self._wait_for_login_completion(driver):
                return False
            
            self.logger.info("登录操作执行成功", step="perform_login_success")
            return True
            
        except Exception as e:
            self.logger.error(f"执行登录操作时发生异常: {str(e)}", step="perform_login_error", exception=e)
            return False
    
    async def _wait_for_page_ready(self, driver: BaseWebDriver):
        """
        等待页面完全加载并处理加载遮罩
        
        Args:
            driver: Web驱动实例
        """
        try:
            self.logger.debug("等待页面完全加载", step="wait_page_ready")
            
            # 等待加载遮罩消失
            loading_selector = self.config['selectors']['loading_mask']
            
            # 检查是否存在加载遮罩
            loading_exists = await driver.is_visible(loading_selector, timeout=2000)
            if loading_exists:
                self.logger.debug("检测到加载遮罩，等待消失", step="loading_mask_detected")
                
                # 等待加载遮罩消失（最多等待15秒）
                for _ in range(15):
                    await asyncio.sleep(1)
                    if not await driver.is_visible(loading_selector, timeout=1000):
                        self.logger.debug("加载遮罩已消失", step="loading_mask_gone")
                        break
                else:
                    self.logger.warning("加载遮罩等待超时，继续执行", step="loading_mask_timeout")
            
            # 额外等待确保页面稳定
            await asyncio.sleep(2)
            
        except Exception as e:
            self.logger.warning(f"等待页面就绪时发生异常: {str(e)}", step="wait_page_ready_error")
    
    async def _fill_login_form(self, driver: BaseWebDriver, username: str, password: str) -> bool:
        """
        填写登录表单
        
        Args:
            driver: Web驱动实例
            username: 用户名
            password: 密码
            
        Returns:
            bool: 是否填写成功
        """
        try:
            selectors = self.config['selectors']
            element_wait = self.config['element_wait']
            
            self.logger.info("开始填写登录表单", step="fill_login_form")
            
            # 填写用户名
            self.logger.debug("填写用户名", step="fill_username")
            username_selector = selectors['username_input']
            
            # 等待用户名输入框出现
            if not await driver.wait_for_element(username_selector, timeout=element_wait['username_timeout']):
                self.logger.error(f"用户名输入框未找到: {username_selector}", step="username_input_not_found")
                return False
            
            # 填写用户名
            if not await driver.safe_input(username_selector, username, clear_first=True):
                self.logger.error("填写用户名失败", step="fill_username_failed")
                return False
            
            # 填写密码
            self.logger.debug("填写密码", step="fill_password")
            password_selector = selectors['password_input']
            
            # 等待密码输入框出现
            if not await driver.wait_for_element(password_selector, timeout=element_wait['password_timeout']):
                self.logger.error(f"密码输入框未找到: {password_selector}", step="password_input_not_found")
                return False
            
            # 填写密码
            if not await driver.safe_input(password_selector, password, clear_first=True):
                self.logger.error("填写密码失败", step="fill_password_failed")
                return False
            
            # 等待一下确保输入完成
            await asyncio.sleep(1)
            
            self.logger.info("登录表单填写完成", step="fill_login_form_success")
            return True
            
        except Exception as e:
            self.logger.error(f"填写登录表单时发生异常: {str(e)}", step="fill_login_form_error", exception=e)
            return False
    
    async def _click_login_button(self, driver: BaseWebDriver) -> bool:
        """
        点击登录按钮（优化版 - 使用Playwright原生方法）
        
        Args:
            driver: Web驱动实例
            
        Returns:
            bool: 是否点击成功
        """
        try:
            selectors = self.config['selectors']
            element_wait = self.config['element_wait']
            
            self.logger.info("点击登录按钮", step="click_login_button")
            
            # 获取页面对象进行更精确的操作
            page = await driver.get_page()
            if not page:
                self.logger.error("无法获取页面对象", step="get_page_failed")
                return False
            
            # 多种登录按钮选择器（按优先级排序）
            login_button_selectors = [
                "button.el-button--primary:has-text('登录')",  # 主要的蓝色登录按钮
                "button.el-button--primary.el-button--small:has-text('登录')",  # 小尺寸主按钮
                "button[type='button'].el-button--primary:has-text('登录')",  # 带类型的主按钮
                "button:has-text('登录')",  # 任何包含"登录"的按钮
                "button:has-text('确认登录')",  # 确认登录按钮
                "//button[contains(@class, 'el-button--primary') and contains(text(), '登录')]",  # XPath方式
                "//button[contains(text(), '登录')]",  # 任何包含"登录"的按钮（XPath）
            ]
            
            # 尝试使用各种选择器（Playwright原生方法）
            for i, selector in enumerate(login_button_selectors):
                try:
                    self.logger.debug(f"尝试登录按钮选择器 {i+1}: {selector}", step="try_login_selector")
                    
                    # 使用Playwright的locator方法
                    button_locator = page.locator(selector)
                    
                    # 检查按钮是否存在且可见
                    if await button_locator.count() > 0:
                        # 如果有多个匹配，选择第一个可见的
                        for j in range(await button_locator.count()):
                            single_button = button_locator.nth(j)
                            
                            try:
                                # 检查按钮是否可见和可点击
                                if await single_button.is_visible(timeout=1000) and await single_button.is_enabled(timeout=1000):
                                    # 滚动到按钮位置
                                    await single_button.scroll_into_view_if_needed()
                                    
                                    # 等待按钮稳定
                                    await single_button.wait_for(state="visible", timeout=3000)
                                    
                                    # 点击按钮
                                    await single_button.click(timeout=5000)
                                    
                                    self.logger.info(f"登录按钮点击成功，使用选择器: {selector} (第{j+1}个匹配)", step="click_login_button_success")
                                    return True
                                    
                            except Exception as e:
                                self.logger.debug(f"按钮 {j+1} 点击失败: {str(e)}", step="button_click_failed")
                                continue
                    else:
                        self.logger.debug(f"选择器未找到匹配元素: {selector}", step="selector_no_match")
                        
                except Exception as e:
                    self.logger.debug(f"选择器 {selector} 失败: {str(e)}", step="selector_failed")
                    continue
            
            # 如果所有精确选择器都失败，尝试更宽泛的查找
            self.logger.info("尝试更宽泛的按钮查找", step="broad_button_search")
            
            try:
                # 查找所有按钮
                all_buttons = page.locator("button")
                button_count = await all_buttons.count()
                
                self.logger.debug(f"页面共找到 {button_count} 个按钮", step="button_count")
                
                for i in range(button_count):
                    button = all_buttons.nth(i)
                    
                    try:
                        # 检查按钮是否可见
                        if await button.is_visible(timeout=500):
                            # 获取按钮文本
                            button_text = await button.text_content()
                            
                            # 检查是否包含"登录"
                            if button_text and ('登录' in button_text or '确认登录' in button_text):
                                self.logger.debug(f"找到包含登录文本的按钮: {button_text}", step="login_text_found")
                                
                                # 滚动到按钮位置
                                await button.scroll_into_view_if_needed()
                                
                                # 点击按钮
                                await button.click(timeout=5000)
                                
                                self.logger.info(f"宽泛搜索登录按钮点击成功: {button_text}", step="broad_search_success")
                                return True
                                
                    except Exception as e:
                        self.logger.debug(f"按钮 {i+1} 处理失败: {str(e)}", step="button_process_failed")
                        continue
                        
            except Exception as e:
                self.logger.debug(f"宽泛搜索失败: {str(e)}", step="broad_search_failed")
            
            # 最后的备选方案：JavaScript（保留作为最后手段）
            self.logger.info("所有Playwright方法失败，使用JavaScript作为最后手段", step="javascript_fallback")
            
            js_result = await page.evaluate("""
                (function() {
                    // 查找所有按钮
                    const allButtons = document.querySelectorAll('button');
                    
                    for (const button of allButtons) {
                        if (button.innerText && 
                            (button.innerText.includes('登录') || button.innerText.includes('确认登录')) &&
                            button.offsetWidth > 0 && button.offsetHeight > 0) {
                            
                            try {
                                button.click();
                                return true;
                            } catch(e) {
                                console.log('JavaScript click error:', e);
                                continue;
                            }
                        }
                    }
                    return false;
                })();
            """)
            
            if js_result:
                self.logger.info("JavaScript备选方案登录按钮点击成功", step="javascript_fallback_success")
                return True
            else:
                self.logger.error("所有方法都失败，无法点击登录按钮", step="all_methods_failed")
                return False
            
        except Exception as e:
            self.logger.error(f"点击登录按钮时发生异常: {str(e)}", step="click_login_button_error")
            return False
    
    async def _wait_for_login_completion(self, driver: BaseWebDriver) -> bool:
        """
        等待登录完成（优化版 - 多层次检测机制）
        
        Args:
            driver: Web驱动实例
            
        Returns:
            bool: 是否登录成功
        """
        try:
            login_wait = self.config['login_wait']
            max_wait_time = login_wait['max_wait_time']
            check_interval = login_wait['check_interval']
            
            self.logger.info(f"等待登录完成，最大等待时间: {max_wait_time}秒", step="wait_login_completion")
            
            # 获取页面对象
            page = await driver.get_page()
            if not page:
                self.logger.error("无法获取页面对象", step="get_page_failed")
                return False
            
            # 记录初始状态
            initial_url = page.url
            self.logger.debug(f"登录前URL: {initial_url}", step="initial_url")
            
            start_time = time.time()
            last_check_time = start_time
            
            # 多层次检测机制
            while time.time() - start_time < max_wait_time:
                try:
                    current_time = time.time()
                    
                    # 每隔指定间隔进行检测
                    if current_time - last_check_time >= check_interval:
                        last_check_time = current_time
                        
                        # 检测方法1：URL变化检测（最可靠）
                        current_url = page.url
                        if current_url != initial_url:
                            # URL已改变，检查是否跳转到主页面
                            if any(pattern in current_url.lower() for pattern in [
                                'main', 'dashboard', 'home', 'index', 'my_orders', 'order'
                            ]):
                                self.logger.info(f"检测到URL变化到主页面: {current_url}", step="url_change_detected")
                                
                                # 等待页面稳定
                                await page.wait_for_load_state('networkidle', timeout=5000)
                                return True
                            else:
                                self.logger.debug(f"URL已变化但非主页面: {current_url}", step="url_changed_not_main")
                        
                        # 检测方法2：页面标题变化
                        try:
                            title = await page.title()
                            if title and not any(word in title.lower() for word in ['登录', 'login', '登陆']):
                                self.logger.info(f"检测到页面标题变化: {title}", step="title_change_detected")
                                return True
                        except Exception as e:
                            self.logger.debug(f"标题检测失败: {str(e)}", step="title_check_failed")
                        
                        # 检测方法3：特征元素出现检测
                        success_indicators = [
                            # 主页面特征元素
                            "nav, .nav, .navbar, .navigation",  # 导航栏
                            ".el-menu, .menu, .sidebar",  # 菜单
                            ".user-info, .user-name, .username",  # 用户信息
                            ".dashboard, .main-content, .content",  # 主要内容区
                            "button:has-text('退出'), button:has-text('登出')",  # 退出按钮
                            ".order-list, .my-orders, [href*='order']",  # 订单相关
                            ".header, .top-bar",  # 头部区域
                        ]
                        
                        for indicator in success_indicators:
                            try:
                                elements = page.locator(indicator)
                                if await elements.count() > 0:
                                    # 检查元素是否可见
                                    first_element = elements.first
                                    if await first_element.is_visible(timeout=1000):
                                        self.logger.info(f"检测到登录成功特征元素: {indicator}", step="success_element_detected")
                                        return True
                            except Exception as e:
                                self.logger.debug(f"特征元素检测失败 {indicator}: {str(e)}", step="element_check_failed")
                                continue
                        
                        # 检测方法4：登录表单消失检测
                        try:
                            login_form_selectors = [
                                "form:has(input[type='password'])",  # 包含密码输入的表单
                                ".login-form, .login-container",  # 登录表单容器
                                "input[type='password']",  # 密码输入框
                                "button:has-text('登录')",  # 登录按钮
                            ]
                            
                            form_disappeared = True
                            for selector in login_form_selectors:
                                try:
                                    elements = page.locator(selector)
                                    if await elements.count() > 0:
                                        first_element = elements.first
                                        if await first_element.is_visible(timeout=500):
                                            form_disappeared = False
                                            break
                                except:
                                    continue
                            
                            if form_disappeared:
                                self.logger.info("检测到登录表单已消失", step="login_form_disappeared")
                                return True
                                
                        except Exception as e:
                            self.logger.debug(f"登录表单检测失败: {str(e)}", step="form_check_failed")
                        
                        # 检测方法5：JavaScript页面状态检测
                        try:
                            js_login_status = await page.evaluate("""
                                (function() {
                                    // 检查是否有登录相关的错误信息
                                    const errorElements = document.querySelectorAll('.error, .alert-danger, .el-message--error');
                                    for (const elem of errorElements) {
                                        if (elem.innerText && 
                                            (elem.innerText.includes('用户名') || 
                                             elem.innerText.includes('密码') || 
                                             elem.innerText.includes('登录失败'))) {
                                            return 'login_error';
                                        }
                                    }
                                    
                                    // 检查页面是否有主要内容区域
                                    const mainContent = document.querySelector('.main, .content, .dashboard, nav, .nav');
                                    if (mainContent && mainContent.offsetWidth > 0) {
                                        return 'main_content_found';
                                    }
                                    
                                    // 检查是否还在登录页面
                                    const loginElements = document.querySelectorAll('input[type="password"], .login-form, button[type="submit"]');
                                    if (loginElements.length === 0) {
                                        return 'login_page_left';
                                    }
                                    
                                    return 'still_loading';
                                })();
                            """)
                            
                            if js_login_status == 'login_error':
                                self.logger.error("检测到登录错误信息", step="login_error_detected")
                                return False
                            elif js_login_status in ['main_content_found', 'login_page_left']:
                                self.logger.info(f"JavaScript检测登录成功: {js_login_status}", step="js_login_success")
                                return True
                                
                        except Exception as e:
                            self.logger.debug(f"JavaScript状态检测失败: {str(e)}", step="js_status_check_failed")
                        
                        # 显示进度
                        elapsed = current_time - start_time
                        remaining = max_wait_time - elapsed
                        self.logger.debug(f"登录检测进行中... 已等待: {elapsed:.1f}s, 剩余: {remaining:.1f}s", step="login_check_progress")
                    
                    # 短暂等待后继续检测
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    self.logger.debug(f"登录检测循环中发生异常: {str(e)}", step="login_check_loop_error")
                    await asyncio.sleep(1)
                    continue
            
            # 超时前的最后检查
            self.logger.info("登录等待即将超时，进行最后检查", step="final_login_check")
            
            try:
                # 等待网络稳定
                await page.wait_for_load_state('networkidle', timeout=3000)
                
                # 最终URL检查
                final_url = page.url
                if final_url != initial_url:
                    self.logger.info(f"最终检查：URL已变化 {initial_url} -> {final_url}", step="final_url_check")
                    return True
                
                # 最终页面内容检查
                js_final_check = await page.evaluate("""
                    (function() {
                        // 检查是否有主要导航或内容
                        const indicators = [
                            'nav', '.nav', '.navbar', '.menu', '.el-menu',
                            '.main', '.content', '.dashboard', '.user-info',
                            '.header', '.top-bar'
                        ];
                        
                        for (const selector of indicators) {
                            const elements = document.querySelectorAll(selector);
                            for (const elem of elements) {
                                if (elem.offsetWidth > 0 && elem.offsetHeight > 0) {
                                    return true;
                                }
                            }
                        }
                        
                        // 检查登录表单是否还存在
                        const loginForms = document.querySelectorAll('input[type="password"], .login-form');
                        return loginForms.length === 0;
                    })();
                """)
                
                if js_final_check:
                    self.logger.info("最终检查：检测到登录成功", step="final_check_success")
                    return True
                    
            except Exception as e:
                self.logger.debug(f"最终检查失败: {str(e)}", step="final_check_failed")
            
            self.logger.error(f"登录完成检测超时，等待了 {max_wait_time} 秒", step="login_wait_timeout")
            return False
            
        except Exception as e:
            self.logger.error(f"等待登录完成时发生异常: {str(e)}", step="wait_login_completion_error")
            return False
    
    async def _verify_login_success(self, driver: BaseWebDriver) -> bool:
        """
        验证登录是否成功（优化版 - 智能验证）
        
        登录操作完成后的最终验证：
        1. 首先检查页面标题是否表明登录成功
        2. 检查当前URL是否为首页或认证页面
        3. 检查是否存在登录后的特征元素
        4. 确保不在登录页面
        
        Args:
            driver: Web驱动实例
            
        Returns:
            bool: 登录是否成功
        """
        try:
            self.logger.info("验证登录结果", step="verify_login_success")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 获取当前URL和页面标题
            current_url = await driver.get_current_url()
            page = await driver.get_page()
            page_title = await page.title() if page else ""
            
            self.logger.debug(f"登录后当前URL: {current_url}", step="post_login_url")
            self.logger.debug(f"登录后页面标题: {page_title}", step="post_login_title")
            
            # 方法1：页面标题检查（最直接的方法）
            if page_title:
                # 如果标题包含系统名称或不包含"登录"相关字样，认为登录成功
                success_title_indicators = ['亿迈', '商户系统', 'dashboard', 'home', '首页', '主页']
                login_title_indicators = ['登录', 'login', '登陆', '验证']
                
                has_success_indicator = any(indicator in page_title for indicator in success_title_indicators)
                has_login_indicator = any(indicator in page_title.lower() for indicator in login_title_indicators)
                
                if has_success_indicator and not has_login_indicator:
                    self.logger.info(f"页面标题验证登录成功: {page_title}", step="title_verification_success")
                    return True
                elif not has_login_indicator and len(page_title.strip()) > 0:
                    # 如果标题不为空且不包含登录相关字样，也认为可能成功
                    self.logger.info(f"页面标题暗示登录成功: {page_title}", step="title_suggests_success")
                    # 继续其他检查以确认
            
            # 方法2：确保不在登录页面
            if self._is_login_page(current_url):
                self.logger.error("登录后仍在登录页面，登录失败", step="still_on_login_page")
                return False
            
            # 方法3：检查是否在认证页面
            is_auth_page = self._is_authenticated_page(current_url)
            if is_auth_page:
                self.logger.info("当前在认证页面，登录可能成功", step="on_auth_page")
                
                # 如果在认证页面且标题表明成功，直接返回成功
                if page_title and any(indicator in page_title for indicator in ['亿迈', '商户系统', 'dashboard']):
                    self.logger.info("认证页面+成功标题，确认登录成功", step="auth_page_title_success")
                    return True
            
            # 方法4：检查登录后的特征元素
            selectors = self.config['selectors']
            
            # 检查用户信息元素
            user_info_exists = await driver.is_visible(selectors['user_info'], timeout=3000)
            if user_info_exists:
                self.logger.info("登录验证成功：找到用户信息元素", step="login_verified_user_info")
                return True
            
            # 检查首页指示元素
            home_indicator_exists = await driver.is_visible(selectors['home_indicator'], timeout=3000)
            if home_indicator_exists:
                self.logger.info("登录验证成功：找到首页指示元素", step="login_verified_home_indicator")
                return True
            
            # 检查退出按钮
            logout_button_exists = await driver.is_visible(selectors['logout_button'], timeout=3000)
            if logout_button_exists:
                self.logger.info("登录验证成功：找到退出按钮", step="login_verified_logout_button")
                return True
            
            # 方法5：JavaScript检查页面状态
            try:
                js_verification = await page.evaluate("""
                    (function() {
                        // 检查是否有登录表单（如果没有，说明已经登录）
                        const loginForms = document.querySelectorAll('input[type="password"], .login-form, form[action*="login"]');
                        const hasLoginForm = loginForms.length > 0;
                        
                        // 检查是否有主要内容区域
                        const mainContent = document.querySelector('.main, .content, .dashboard, nav, .nav, .header, .el-menu');
                        const hasMainContent = mainContent && mainContent.offsetWidth > 0;
                        
                        // 检查页面是否有错误信息
                        const errorElements = document.querySelectorAll('.error, .alert-danger, .el-message--error');
                        let hasError = false;
                        for (const elem of errorElements) {
                            if (elem.innerText && elem.innerText.includes('登录')) {
                                hasError = true;
                                break;
                            }
                        }
                        
                        return {
                            hasLoginForm: hasLoginForm,
                            hasMainContent: hasMainContent,
                            hasError: hasError,
                            bodyClass: document.body.className,
                            title: document.title
                        };
                    })();
                """)
                
                # 如果没有登录表单且有主要内容，认为登录成功
                if not js_verification.get('hasLoginForm') and js_verification.get('hasMainContent'):
                    self.logger.info("JavaScript验证登录成功：无登录表单且有主要内容", step="js_verification_success")
                    return True
                
                # 如果没有登录表单且没有错误，也可能成功
                if not js_verification.get('hasLoginForm') and not js_verification.get('hasError'):
                    self.logger.info("JavaScript验证登录成功：无登录表单且无错误", step="js_verification_no_form_no_error")
                    return True
                    
            except Exception as e:
                self.logger.debug(f"JavaScript验证失败: {str(e)}", step="js_verification_failed")
            
            # 方法6：如果在认证页面但找不到特征元素，给页面更多时间加载
            if is_auth_page:
                self.logger.info("在认证页面但未找到特征元素，等待页面完全加载", step="auth_page_wait_loading")
                
                # 等待页面网络空闲
                try:
                    await page.wait_for_load_state('networkidle', timeout=5000)
                except:
                    pass
                
                # 再次检查用户信息元素
                await asyncio.sleep(2)
                user_info_exists = await driver.is_visible(selectors['user_info'], timeout=2000)
                if user_info_exists:
                    self.logger.info("延迟验证成功：找到用户信息元素", step="delayed_verification_success")
                    return True
                
                # 如果页面标题表明成功，即使找不到特定元素也认为成功
                if page_title and any(indicator in page_title for indicator in ['亿迈', '商户系统']):
                    self.logger.info("基于页面标题的最终验证成功", step="final_title_verification_success")
                    return True
            
            # 最终检查：如果URL已改变且不在登录页面，可能是成功的
            if not self._is_login_page(current_url) and current_url != self.config['login_page']:
                self.logger.warning("URL已改变且不在登录页面，可能登录成功但页面结构不符合预期", step="url_changed_possible_success")
                
                # 如果标题不为空且不包含登录字样，认为可能成功
                if page_title and not any(word in page_title.lower() for word in ['login', '登录', '登陆']):
                    self.logger.info("基于URL和标题的兼容性验证成功", step="compatibility_verification_success")
                    return True
            
            self.logger.error("登录验证失败：所有验证方法都未通过", step="all_verification_failed")
            return False
            
        except Exception as e:
            self.logger.error(f"验证登录结果时发生异常: {str(e)}", step="verify_login_success_error", exception=e)
            return False
    
    async def logout(self, driver: BaseWebDriver) -> bool:
        """
        退出登录（可选功能）
        
        Args:
            driver: Web驱动实例
            
        Returns:
            bool: 是否退出成功
        """
        try:
            self.logger.info("开始退出登录", step="logout_start")
            
            selectors = self.config['selectors']
            logout_selector = selectors['logout_button']
            
            # 检查退出按钮是否存在
            if not await driver.is_visible(logout_selector, timeout=5000):
                self.logger.warning("未找到退出按钮，可能已经退出", step="logout_button_not_found")
                return True
            
            # 点击退出按钮
            if await driver.safe_click(logout_selector):
                # 等待退出完成
                await asyncio.sleep(3)
                
                # 检查是否返回登录页面
                current_url = await driver.get_current_url()
                if self._is_login_page(current_url):
                    self.logger.info("退出登录成功", step="logout_success")
                    self._update_login_cache(False)
                    return True
                else:
                    self.logger.warning("点击退出按钮后未返回登录页面", step="logout_incomplete")
                    return False
            else:
                self.logger.error("点击退出按钮失败", step="logout_click_failed")
                return False
                
        except Exception as e:
            self.logger.error(f"退出登录时发生异常: {str(e)}", step="logout_error", exception=e)
            return False
    
    def clear_login_cache(self):
        """清除登录状态缓存"""
        self._last_login_check_time = 0
        self._last_login_status = False
        self.logger.debug("登录状态缓存已清除", step="cache_cleared")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取登录管理器的统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "login_attempts": self._login_attempts,
            "login_successes": self._login_successes,
            "login_failures": self._login_failures,
            "success_rate": (self._login_successes / max(self._login_attempts, 1)) * 100,
            "last_login_check": self._last_login_check_time,
            "last_login_status": self._last_login_status,
            "cache_duration": self._login_cache_duration
        } 