"""
RPA日志工具类

提供专门用于RPA任务的日志记录功能：
- 任务追踪信息
- 步骤记录
- 执行时间统计
- 结构化日志输出
"""

import uuid
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from contextlib import contextmanager

from app.config.logging import get_logger


class RPALogger:
    """RPA专用日志记录器"""
    
    def __init__(self, business_type: str, script_name: str, task_id: Optional[str] = None):
        """
        初始化RPA日志记录器
        
        Args:
            business_type: 业务类型
            script_name: 脚本名称  
            task_id: 任务ID，如果不提供则自动生成
        """
        self.business_type = business_type
        self.script_name = script_name
        self.task_id = task_id or str(uuid.uuid4())
        self.logger = get_logger(f'rpa.{business_type}.{script_name}')
        self.start_time = datetime.utcnow()
        
        # 记录任务开始
        self.info("RPA任务开始", step="task_start")
    
    def _log_with_context(self, level: str, message: str, step: Optional[str] = None, 
                         extra_data: Optional[Dict[str, Any]] = None, 
                         execution_time: Optional[float] = None, exc_info: bool = False):
        """
        记录带上下文信息的日志
        
        Args:
            level: 日志级别
            message: 日志消息
            step: 执行步骤
            extra_data: 额外数据
            execution_time: 执行时间
            exc_info: 是否记录异常信息
        """
        # 创建日志记录，添加RPA特定字段
        record_kwargs = {
            'msg': message,
            'extra': {
                'task_id': self.task_id,
                'business_type': self.business_type,
                'script_name': self.script_name,
            }
        }
        
        if step:
            record_kwargs['extra']['step'] = step
            
        if extra_data:
            record_kwargs['extra']['extra_data'] = extra_data
            
        if execution_time is not None:
            record_kwargs['extra']['execution_time'] = execution_time
            
        if exc_info:
            record_kwargs['exc_info'] = True
        
        # 根据级别记录日志
        log_method = getattr(self.logger, level.lower())
        log_method(record_kwargs['msg'], extra=record_kwargs['extra'], exc_info=exc_info)
    
    def debug(self, message: str, step: Optional[str] = None, extra_data: Optional[Dict[str, Any]] = None):
        """记录DEBUG级别日志"""
        self._log_with_context('DEBUG', message, step, extra_data)
    
    def info(self, message: str, step: Optional[str] = None, extra_data: Optional[Dict[str, Any]] = None):
        """记录INFO级别日志"""
        self._log_with_context('INFO', message, step, extra_data)
    
    def warning(self, message: str, step: Optional[str] = None, extra_data: Optional[Dict[str, Any]] = None):
        """记录WARNING级别日志"""
        self._log_with_context('WARNING', message, step, extra_data)
    
    def error(self, message: str, step: Optional[str] = None, extra_data: Optional[Dict[str, Any]] = None, 
              exception: Optional[Exception] = None):
        """记录ERROR级别日志"""
        self._log_with_context('ERROR', message, step, extra_data, exc_info=exception is not None)
    
    def critical(self, message: str, step: Optional[str] = None, extra_data: Optional[Dict[str, Any]] = None, 
                 exception: Optional[Exception] = None):
        """记录CRITICAL级别日志"""
        self._log_with_context('CRITICAL', message, step, extra_data, exc_info=exception is not None)
    
    @contextmanager
    def step_timer(self, step_name: str, message: Optional[str] = None):
        """
        步骤计时上下文管理器
        
        Args:
            step_name: 步骤名称
            message: 自定义消息
            
        Usage:
            with logger.step_timer("data_scraping", "开始抓取数据"):
                # 执行数据抓取逻辑
                pass
        """
        start_time = time.time()
        start_msg = message or f"开始执行步骤: {step_name}"
        
        self.info(start_msg, step=f"{step_name}_start")
        
        try:
            yield
            end_time = time.time()
            execution_time = end_time - start_time
            end_msg = f"步骤执行完成: {step_name}"
            self._log_with_context('INFO', end_msg, f"{step_name}_end", 
                                 execution_time=execution_time)
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            error_msg = f"步骤执行失败: {step_name} - {str(e)}"
            self._log_with_context('ERROR', error_msg, f"{step_name}_error", 
                                 execution_time=execution_time, exc_info=True)
            raise
    
    def execution_summary(self, status: str, result_data: Optional[Dict[str, Any]] = None):
        """
        记录任务执行总结
        
        Args:
            status: 执行状态 (success/failure/timeout)
            result_data: 结果数据
        """
        end_time = datetime.utcnow()
        total_execution_time = (end_time - self.start_time).total_seconds()
        
        summary_data = {
            'status': status,
            'total_execution_time': total_execution_time,
            'start_time': self.start_time.isoformat() + 'Z',
            'end_time': end_time.isoformat() + 'Z',
        }
        
        if result_data:
            summary_data.update(result_data)
        
        level = 'INFO' if status == 'success' else 'ERROR'
        message = f"RPA任务执行完成 - 状态: {status}"
        
        self._log_with_context(level, message, "task_summary", 
                             extra_data=summary_data, 
                             execution_time=total_execution_time)
    
    def log_selenium_action(self, action: str, element: str = None, value: str = None, 
                           screenshot_path: str = None):
        """
        记录Selenium操作日志
        
        Args:
            action: 操作类型 (click, input, navigate等)
            element: 元素定位信息
            value: 输入值
            screenshot_path: 截图路径
        """
        extra_data = {'action': action}
        
        if element:
            extra_data['element'] = element
        if value:
            extra_data['value'] = value
        if screenshot_path:
            extra_data['screenshot_path'] = screenshot_path
            
        self.info(f"Selenium操作: {action}", step="selenium_action", extra_data=extra_data)
    
    def log_database_operation(self, operation: str, table: str = None, 
                              affected_rows: int = None, query: str = None):
        """
        记录数据库操作日志
        
        Args:
            operation: 操作类型 (select, insert, update, delete)
            table: 表名
            affected_rows: 影响行数
            query: SQL查询语句
        """
        extra_data = {'operation': operation}
        
        if table:
            extra_data['table'] = table
        if affected_rows is not None:
            extra_data['affected_rows'] = affected_rows
        if query:
            # 记录SQL但不记录敏感信息
            safe_query = query[:200] + '...' if len(query) > 200 else query
            extra_data['query'] = safe_query
            
        self.info(f"数据库操作: {operation}", step="database_operation", extra_data=extra_data)


def get_rpa_logger(business_type: str, script_name: str, task_id: Optional[str] = None) -> RPALogger:
    """
    获取RPA日志记录器实例
    
    Args:
        business_type: 业务类型
        script_name: 脚本名称
        task_id: 任务ID
        
    Returns:
        RPALogger实例
    """
    return RPALogger(business_type, script_name, task_id) 