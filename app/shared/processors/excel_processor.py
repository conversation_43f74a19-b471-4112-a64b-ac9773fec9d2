"""
通用Excel处理器

提供Excel文件处理的通用功能：
1. 读取Excel文件（支持多Sheet）
2. 处理合并单元格
3. 数据提取和转换
4. 按条件筛选数据
5. 支持多种数据源（文件路径、字节流）

重构说明：
- 基于app/template/excel_tamplate_util.py重构
- 增加了通用性和灵活性
- 支持内存处理和条件筛选
"""

import os
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Callable
from pathlib import Path
import logging
import tempfile
import shutil
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
import io


class ExcelProcessor:
    """
    通用Excel处理器
    
    提供完整的Excel文件处理功能：
    - 读取和解析Excel文件
    - 多Sheet处理
    - 合并单元格处理
    - 数据筛选和提取
    - 支持文件和字节流输入
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化Excel处理器
        
        Args:
            logger: 日志记录器，如果未提供则创建默认logger
        """
        self.logger = logger or self._create_default_logger()
        self.temp_files = []  # 跟踪临时文件
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def read_excel_from_bytes(self, 
                             excel_bytes: bytes,
                             handle_merged_cells: bool = True) -> Dict[str, pd.DataFrame]:
        """
        从字节流读取Excel文件
        
        Args:
            excel_bytes: Excel文件的字节内容
            handle_merged_cells: 是否处理合并单元格，默认True
            
        Returns:
            Dict[str, pd.DataFrame]: 以Sheet名为键，DataFrame为值的字典
        """
        try:
            self.logger.info(f"开始从字节流读取Excel文件，大小: {len(excel_bytes)} 字节")
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            # 写入字节内容
            with open(temp_path, 'wb') as f:
                f.write(excel_bytes)
            
            # 记录临时文件
            self.temp_files.append(temp_path)
            
            # 读取Excel文件
            sheets_dict = self.read_excel_sheets(temp_path, handle_merged_cells)
            
            self.logger.info(f"成功从字节流读取Excel文件，共{len(sheets_dict)}个Sheet")
            return sheets_dict
            
        except Exception as e:
            self.logger.error(f"从字节流读取Excel文件失败: {e}")
            raise
    
    def read_excel_sheets(self, 
                         excel_path: Union[str, Path], 
                         handle_merged_cells: bool = True) -> Dict[str, pd.DataFrame]:
        """
        读取Excel文件的所有Sheet
        
        Args:
            excel_path: Excel文件路径
            handle_merged_cells: 是否处理合并单元格，默认True
            
        Returns:
            Dict[str, pd.DataFrame]: 以Sheet名为键，DataFrame为值的字典
        """
        excel_path = Path(excel_path)
        
        if not excel_path.exists():
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")
        
        if excel_path.suffix.lower() not in ['.xlsx', '.xls']:
            raise ValueError(f"不支持的文件格式: {excel_path.suffix}")
        
        try:
            self.logger.info(f"开始读取Excel文件: {excel_path}")
            
            # 如果需要处理合并单元格，先处理后再读取
            if handle_merged_cells:
                processed_file = self._process_merged_cells(excel_path)
                try:
                    # 读取处理后的文件
                    sheets_dict = pd.read_excel(processed_file, sheet_name=None, engine='openpyxl')
                finally:
                    # 清理临时文件（确保不是原文件）
                    if processed_file != str(excel_path) and os.path.exists(processed_file):
                        try:
                            os.remove(processed_file)
                            self.logger.debug(f"已清理临时文件: {processed_file}")
                        except PermissionError:
                            self.logger.warning(f"无法删除临时文件: {processed_file}")
            else:
                # 直接读取原文件
                sheets_dict = pd.read_excel(excel_path, sheet_name=None, engine='openpyxl')
            
            self.logger.info(f"成功读取Excel文件，共{len(sheets_dict)}个Sheet: {list(sheets_dict.keys())}")
            return sheets_dict
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def filter_sheet_data(self,
                         df: pd.DataFrame,
                         column_conditions: Dict[str, Any] = None,
                         custom_filter: Callable[[pd.DataFrame], pd.DataFrame] = None) -> pd.DataFrame:
        """
        按条件筛选Sheet数据
        
        Args:
            df: 原始DataFrame
            column_conditions: 列条件字典，格式: {列名: 值} 或 {列名: [值1, 值2]}
            custom_filter: 自定义筛选函数
            
        Returns:
            pd.DataFrame: 筛选后的DataFrame
        """
        try:
            filtered_df = df.copy()
            
            # 应用列条件筛选
            if column_conditions:
                for column, condition in column_conditions.items():
                    if column not in filtered_df.columns:
                        self.logger.warning(f"列 '{column}' 不存在，跳过筛选")
                        continue
                    
                    if isinstance(condition, list):
                        # 多值筛选
                        filtered_df = filtered_df[filtered_df[column].isin(condition)]
                        self.logger.debug(f"按列 '{column}' 多值筛选: {condition}")
                    else:
                        # 单值筛选
                        filtered_df = filtered_df[filtered_df[column] == condition]
                        self.logger.debug(f"按列 '{column}' 筛选: {condition}")
            
            # 应用自定义筛选
            if custom_filter:
                filtered_df = custom_filter(filtered_df)
                self.logger.debug("应用自定义筛选函数")
            
            self.logger.info(f"数据筛选完成，原{len(df)}行 -> 筛选后{len(filtered_df)}行")
            return filtered_df
            
        except Exception as e:
            self.logger.error(f"数据筛选失败: {e}")
            raise
    
    def extract_column_values(self,
                             df: pd.DataFrame,
                             column_name: str,
                             unique_only: bool = True,
                             exclude_empty: bool = True) -> List[Any]:
        """
        提取指定列的值
        
        Args:
            df: DataFrame
            column_name: 列名
            unique_only: 是否只返回唯一值
            exclude_empty: 是否排除空值
            
        Returns:
            List[Any]: 列值列表
        """
        try:
            if column_name not in df.columns:
                raise ValueError(f"列 '{column_name}' 不存在")
            
            values = df[column_name]
            
            # 排除空值
            if exclude_empty:
                values = values.dropna()
                # 排除空字符串
                values = values[values != '']
            
            # 获取唯一值
            if unique_only:
                values = values.unique()
            
            result = values.tolist()
            
            self.logger.info(f"提取列 '{column_name}' 的值，共{len(result)}个")
            return result
            
        except Exception as e:
            self.logger.error(f"提取列值失败: {e}")
            raise
    
    def find_sheets_by_name_pattern(self,
                                   sheets_dict: Dict[str, pd.DataFrame],
                                   patterns: List[str]) -> Dict[str, pd.DataFrame]:
        """
        根据名称模式查找Sheet
        
        Args:
            sheets_dict: Sheet字典
            patterns: 名称模式列表（支持部分匹配）
            
        Returns:
            Dict[str, pd.DataFrame]: 匹配的Sheet字典
        """
        try:
            matched_sheets = {}
            
            for sheet_name, df in sheets_dict.items():
                for pattern in patterns:
                    if pattern in sheet_name:
                        matched_sheets[sheet_name] = df
                        self.logger.debug(f"Sheet '{sheet_name}' 匹配模式 '{pattern}'")
                        break
            
            self.logger.info(f"根据模式找到{len(matched_sheets)}个匹配的Sheet")
            return matched_sheets
            
        except Exception as e:
            self.logger.error(f"查找Sheet失败: {e}")
            raise
    
    async def process_business_data(self,
                                   excel_data: Union[bytes, str, Path],
                                   sheet_configs: Dict[str, Dict[str, Any]]) -> Dict[str, List[Any]]:
        """
        处理业务数据 - 核心业务逻辑方法
        
        Args:
            excel_data: Excel数据（字节流、文件路径等）
            sheet_configs: Sheet配置字典
            
        Returns:
            Dict[str, List[Any]]: 处理结果字典
        """
        # TODO: 实现具体的业务数据处理逻辑
        self.logger.info("Excel业务数据处理 - 待实现")
        return {}
    
    def _process_merged_cells(self, excel_path: Path) -> str:
        """
        处理Excel文件中的合并单元格
        
        Args:
            excel_path: 原始Excel文件路径
            
        Returns:
            str: 处理后的文件路径（可能是临时文件）
        """
        try:
            # 检查是否有合并单元格
            if not self._has_merged_cells(excel_path):
                self.logger.info("未检测到合并单元格，无需处理")
                return str(excel_path)
            
            self.logger.info("检测到合并单元格，开始处理...")
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            # 复制原文件到临时文件
            shutil.copy2(excel_path, temp_path)
            
            # 处理合并单元格
            self._unmerge_and_fill_cells(temp_path)
            
            self.logger.info(f"合并单元格处理完成，临时文件: {temp_path}")
            return temp_path
            
        except Exception as e:
            self.logger.error(f"处理合并单元格失败: {e}")
            # 如果处理失败，返回原文件路径
            return str(excel_path)
    
    def _has_merged_cells(self, excel_path: Path) -> bool:
        """检查Excel文件是否包含合并单元格"""
        try:
            workbook = load_workbook(excel_path, read_only=False)
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                if worksheet.merged_cells.ranges:
                    workbook.close()
                    return True
            
            workbook.close()
            return False
            
        except Exception as e:
            self.logger.warning(f"检查合并单元格时出错: {e}")
            return False
    
    def _unmerge_and_fill_cells(self, excel_path: str) -> None:
        """拆分合并单元格并填充数据"""
        workbook = None
        try:
            workbook = load_workbook(excel_path)
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # 获取所有合并单元格区域
                merged_ranges = list(worksheet.merged_cells.ranges)
                
                if not merged_ranges:
                    continue
                
                self.logger.info(f"Sheet '{sheet_name}' 发现 {len(merged_ranges)} 个合并单元格区域")
                
                # 处理每个合并单元格区域
                for merged_range in merged_ranges:
                    # 获取合并区域的边界
                    min_col, min_row, max_col, max_row = merged_range.bounds
                    
                    # 获取左上角单元格的值
                    top_left_cell = worksheet.cell(row=min_row, column=min_col)
                    merged_value = top_left_cell.value
                    
                    # 先取消合并
                    worksheet.unmerge_cells(str(merged_range))
                    
                    # 用原值填充所有单元格
                    for row in range(min_row, max_row + 1):
                        for col in range(min_col, max_col + 1):
                            cell = worksheet.cell(row=row, column=col)
                            cell.value = merged_value
            
            # 保存处理后的文件
            workbook.save(excel_path)
            self.logger.info("所有合并单元格处理完成")
            
        except Exception as e:
            self.logger.error(f"拆分合并单元格失败: {e}")
            raise
        finally:
            # 确保关闭workbook
            if workbook:
                workbook.close()
    
    def get_sheet_info(self, 
                      excel_data: Union[bytes, str, Path], 
                      handle_merged_cells: bool = True) -> Dict[str, Dict]:
        """
        获取Excel文件中所有Sheet的基本信息
        
        Args:
            excel_data: Excel数据（字节流、文件路径等）
            handle_merged_cells: 是否处理合并单元格，默认True
            
        Returns:
            Dict[str, Dict]: Sheet信息字典，包含行数、列数等
        """
        # 读取Excel数据
        if isinstance(excel_data, bytes):
            sheets_dict = self.read_excel_from_bytes(excel_data, handle_merged_cells)
        else:
            sheets_dict = self.read_excel_sheets(excel_data, handle_merged_cells)
        
        sheet_info = {}
        for sheet_name, df in sheets_dict.items():
            sheet_info[sheet_name] = {
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': list(df.columns),
                'has_data': not df.empty,
                'memory_usage': df.memory_usage(deep=True).sum()
            }
        
        return sheet_info
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        # TODO: 实现临时文件清理逻辑
        self.logger.debug("临时文件清理 - 待实现")
    
    def __del__(self):
        """析构函数，确保清理临时文件"""
        self.cleanup_temp_files() 