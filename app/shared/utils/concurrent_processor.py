"""
并发处理器

提供带频率限制的并发处理功能，适用于API调用场景。
主要功能：
1. 并发数控制 - 通过Semaphore限制同时执行的任务数
2. 频率限制 - 控制API调用间隔，避免过于频繁的请求
3. 异常处理 - 单个任务失败不影响其他任务
4. 灵活配置 - 支持动态调整并发数和请求间隔

使用方式：
    processor = ConcurrentOrderProcessor(max_concurrent=2, request_interval=1.0)
    results = await processor.process_orders_concurrent(order_list, tokens, user_info)
"""

import asyncio
import time
import logging
from asyncio import Semaphore
from typing import List, Dict, Any, Callable, Optional
from datetime import datetime


class ConcurrentOrderProcessor:
    """
    并发订单处理器
    
    支持带频率限制的并发处理，适用于API调用场景
    """
    
    def __init__(self, 
                 max_concurrent: int = 2, 
                 request_interval: float = 1.0,
                 logger: Optional[logging.Logger] = None):
        """
        初始化并发处理器
        
        Args:
            max_concurrent: 最大并发数
            request_interval: 请求间隔（秒）
            logger: 日志记录器
        """
        self.semaphore = Semaphore(max_concurrent)
        self.request_interval = request_interval
        self.last_request_time = 0
        self.request_lock = asyncio.Lock()  # 用于保护last_request_time
        self.logger = logger or logging.getLogger(__name__)
        
        # 统计信息
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.start_time = None
        
        self.logger.info(f"✅ 并发处理器初始化完成 - 最大并发: {max_concurrent}, 请求间隔: {request_interval}秒")
    
    async def process_orders_concurrent(self, 
                                       order_list: List[Dict], 
                                       api_call_func: Callable,
                                       *args, **kwargs) -> List[Any]:
        """
        并发处理订单列表，带频率限制
        
        Args:
            order_list: 订单列表
            api_call_func: API调用函数
            *args, **kwargs: 传递给API调用函数的参数
            
        Returns:
            List[Any]: 处理结果列表，包含成功结果和异常
        """
        if not order_list:
            self.logger.warning("订单列表为空，跳过并发处理")
            return []
        
        self.start_time = time.time()
        self.total_requests = len(order_list)
        
        self.logger.info(f"🚀 开始并发处理订单 - 总数: {len(order_list)}, 最大并发: {self.semaphore._value}, 请求间隔: {self.request_interval}秒")
        
        # 创建并发任务
        tasks = []
        for i, order in enumerate(order_list):
            task = self._process_single_order_with_limit(
                order, api_call_func, i, *args, **kwargs
            )
            tasks.append(task)
        
        # 批量执行，自动管理并发数和频率
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        self._log_processing_summary(results)
        
        return results
    
    async def _process_single_order_with_limit(self, 
                                              order: Dict, 
                                              api_call_func: Callable,
                                              order_index: int,
                                              *args, **kwargs) -> Any:
        """
        单个订单处理，带并发和频率限制
        
        Args:
            order: 订单数据
            api_call_func: API调用函数
            order_index: 订单索引
            *args, **kwargs: 传递给API调用函数的参数
            
        Returns:
            Any: 处理结果或异常
        """
        async with self.semaphore:  # 控制并发数
            try:
                # 频率限制
                await self._apply_rate_limit()
                
                # 记录开始处理
                order_id = order.get('order_id', f'order_{order_index}')
                self.logger.debug(f"🔄 开始处理订单: {order_id}")
                
                # 执行实际的API调用
                start_time = time.time()
                result = await api_call_func(order, *args, **kwargs)
                
                # 记录成功
                processing_time = time.time() - start_time
                self.successful_requests += 1
                
                self.logger.debug(f"✅ 订单处理成功: {order_id} - 耗时: {processing_time:.2f}s, 索引: {order_index}")
                
                return result
                
            except Exception as e:
                # 记录失败
                self.failed_requests += 1
                order_id = order.get('order_id', f'order_{order_index}')
                
                self.logger.warning(f"❌ 订单处理失败: {order_id} - 错误: {str(e)}, 索引: {order_index}, 异常类型: {type(e).__name__}")
                
                # 返回异常而不是抛出，这样不会影响其他任务
                return e
    
    async def _apply_rate_limit(self):
        """
        应用频率限制，确保请求间隔
        """
        async with self.request_lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < self.request_interval:
                sleep_time = self.request_interval - time_since_last
                self.logger.debug(f"⏱️ 频率限制等待: {sleep_time:.2f}秒")
                await asyncio.sleep(sleep_time)
            
            self.last_request_time = time.time()
    
    def _log_processing_summary(self, results: List[Any]):
        """
        记录处理结果摘要
        
        Args:
            results: 处理结果列表
        """
        total_time = time.time() - self.start_time if self.start_time else 0
        
        # 统计异常类型
        exception_types = {}
        for result in results:
            if isinstance(result, Exception):
                exc_type = type(result).__name__
                exception_types[exc_type] = exception_types.get(exc_type, 0) + 1
        
        success_rate = f"{(self.successful_requests / self.total_requests * 100):.1f}%" if self.total_requests > 0 else "0%"
        avg_time = f"{(total_time / self.total_requests):.2f}秒/订单" if self.total_requests > 0 else "0秒"

        self.logger.info(f"🎯 并发处理完成 - 总订单数: {self.total_requests}, 成功数: {self.successful_requests}, "
                        f"失败数: {self.failed_requests}, 成功率: {success_rate}, 总耗时: {total_time:.2f}秒, "
                        f"平均耗时: {avg_time}, 异常类型: {exception_types if exception_types else '无异常'}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        total_time = time.time() - self.start_time if self.start_time else 0
        
        return {
            'total_requests': self.total_requests,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'success_rate': (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0,
            'total_time': total_time,
            'average_time_per_request': (total_time / self.total_requests) if self.total_requests > 0 else 0,
            'requests_per_second': (self.total_requests / total_time) if total_time > 0 else 0
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_requests = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.start_time = None
        self.logger.debug("📊 统计信息已重置")


class BatchProcessor:
    """
    批处理器
    
    将大量任务分批处理，避免一次性创建过多并发任务
    """
    
    def __init__(self, 
                 batch_size: int = 10,
                 batch_interval: float = 0.5,
                 logger: Optional[logging.Logger] = None):
        """
        初始化批处理器
        
        Args:
            batch_size: 批处理大小
            batch_interval: 批次间隔（秒）
            logger: 日志记录器
        """
        self.batch_size = batch_size
        self.batch_interval = batch_interval
        self.logger = logger or logging.getLogger(__name__)
        
        self.logger.info(f"✅ 批处理器初始化完成 - 批大小: {batch_size}, 批间隔: {batch_interval}秒")
    
    async def process_in_batches(self, 
                                items: List[Any],
                                processor: ConcurrentOrderProcessor,
                                api_call_func: Callable,
                                *args, **kwargs) -> List[Any]:
        """
        分批处理项目列表
        
        Args:
            items: 要处理的项目列表
            processor: 并发处理器
            api_call_func: API调用函数
            *args, **kwargs: 传递给API调用函数的参数
            
        Returns:
            List[Any]: 所有批次的处理结果
        """
        if not items:
            return []
        
        total_batches = (len(items) + self.batch_size - 1) // self.batch_size
        
        self.logger.info(f"📦 开始分批处理 - 总项目: {len(items)}, 批大小: {self.batch_size}, 总批数: {total_batches}")
        
        all_results = []
        
        for batch_index in range(total_batches):
            start_idx = batch_index * self.batch_size
            end_idx = min(start_idx + self.batch_size, len(items))
            batch_items = items[start_idx:end_idx]
            
            self.logger.info(f"🔄 处理批次 {batch_index + 1}/{total_batches} - 项目数: {len(batch_items)}, 范围: {start_idx}-{end_idx-1}")
            
            # 处理当前批次
            batch_results = await processor.process_orders_concurrent(
                batch_items, api_call_func, *args, **kwargs
            )
            
            all_results.extend(batch_results)
            
            # 批次间隔（除了最后一批）
            if batch_index < total_batches - 1:
                self.logger.debug(f"⏱️ 批次间隔等待: {self.batch_interval}秒")
                await asyncio.sleep(self.batch_interval)
        
        self.logger.info(f"🎯 分批处理完成 - 总批数: {total_batches}, 总结果: {len(all_results)}")
        
        return all_results
