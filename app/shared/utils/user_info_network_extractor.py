"""
用户信息网络提取工具

通用的用户信息网络提取工具，集成网络拦截和多渠道用户信息提取功能。
主要功能：
1. 网络拦截设置 - 自动拦截登录相关的网络请求
2. 多渠道用户信息提取 - 支持从响应数据、网络历史、JWT Token等多种方式提取
3. 支持distributor_id字段提取 - 专门增强对distributor_id的提取支持
4. 通用接口 - 可被多个业务模块复用

使用方式：
    extractor = UserInfoNetworkExtractor(logger, business_type, script_name)
    async with web_driver_context() as driver:
        tokens, user_info = await extractor.extract_with_network_interception(driver)
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional, Tuple
from playwright.async_api import Page, Request, Response

from app.shared.clients.user_info_extractor import UserInfoExtractor
from app.shared.clients.token_extractor import TokenExtractor
from app.utils.yimai_login_async import AsyncYimaiLoginManager
from app.core.web_driver.base_driver import BaseWebDriver


class UserInfoNetworkExtractor:
    """
    用户信息网络提取工具
    
    集成网络拦截和多渠道用户信息提取的通用工具
    """
    
    def __init__(self, logger: logging.Logger = None, business_type: str = None, script_name: str = None):
        """
        初始化用户信息网络提取工具
        
        Args:
            logger: 日志记录器
            business_type: 业务类型
            script_name: 脚本名称
        """
        self.logger = logger or logging.getLogger(__name__)
        self.business_type = business_type
        self.script_name = script_name
        
        # 初始化组件
        self.yimai_login_manager = AsyncYimaiLoginManager(
            self.logger, self.business_type, self.script_name
        )
        self.token_extractor = TokenExtractor(self.logger)
        self.user_info_extractor = UserInfoExtractor(self.logger)
        
        # 网络拦截状态
        self._network_intercepted = False
        self._login_response_data = None
        
        self.logger.info("✅ 用户信息网络提取工具初始化完成", extra_data={
            'business_type': self.business_type,
            'script_name': self.script_name
        })
    
    async def extract_with_network_interception(self, driver: BaseWebDriver) -> Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]:
        """
        使用网络拦截提取用户信息和Token
        
        Args:
            driver: Web驱动实例
            
        Returns:
            Tuple[Optional[Dict[str, str]], Optional[Dict[str, Any]]]: Token字典和用户信息字典
        """
        try:
            self.logger.info("开始使用网络拦截提取用户信息和Token")
            
            # 设置网络拦截
            await self._setup_network_interception(driver)
            
            # 确保登录状态
            self.logger.info("步骤1: 确保登录状态")
            login_success = await self.yimai_login_manager.ensure_login(driver)
            if not login_success:
                self.logger.error("登录失败，无法继续执行")
                return None, None
            
            # 提取Token信息
            self.logger.info("步骤2: 提取Token信息")
            tokens = await self.token_extractor.extract_tokens_from_page(driver.page)
            if not tokens or not tokens.get('jwt_token'):
                self.logger.error("Token提取失败，无法继续执行")
                return None, None
            
            # 提取用户信息
            self.logger.info("步骤3: 提取用户信息")
            user_info = await self._extract_user_info_multi_channel(driver, tokens)
            if not user_info or not user_info.get('uid'):
                self.logger.error("用户信息提取失败，无法继续执行")
                return None, None
            
            self.logger.info(f"✅ 用户信息和Token提取成功", extra_data={
                'user_id': user_info.get('uid'),
                'distributor_id': user_info.get('distributor_id'),
                'account': user_info.get('account'),
                'has_jwt_token': bool(tokens.get('jwt_token'))
            })
            
            return tokens, user_info
            
        except Exception as e:
            self.logger.error(f"❌ 用户信息网络提取异常: {str(e)}")
            return None, None
    
    async def _setup_network_interception(self, driver: BaseWebDriver):
        """
        设置网络请求拦截，用于获取登录响应数据
        
        Args:
            driver: Web驱动实例
        """
        try:
            if not hasattr(driver, 'page') or not driver.page:
                self.logger.warning("页面对象不可用，跳过网络拦截设置")
                return
            
            self.logger.info("设置网络请求拦截")
            
            def handle_response(response):
                """处理响应的内部函数"""
                try:
                    url = response.url
                    # 检查是否是登录相关的响应
                    if any(login_path in url for login_path in ['login/accountLogin', 'login/login', 'user/getUserInfo']):
                        asyncio.create_task(self._process_login_response(response, url))
                except Exception as e:
                    self.logger.warning(f"处理响应时异常: {e}")
            
            # 启用响应拦截
            driver.page.on('response', handle_response)
            self.logger.debug("网络请求拦截已启用")
            self._network_intercepted = True
            
        except Exception as e:
            self.logger.warning(f"设置网络拦截时异常: {e}")
    
    async def _process_login_response(self, response: Response, url: str):
        """
        处理登录响应数据，提取用户信息
        
        Args:
            response: 响应对象
            url: 请求URL
        """
        try:
            if response.status == 200:
                response_text = await response.text()
                if response_text:
                    try:
                        data = json.loads(response_text)
                        if isinstance(data, dict) and data.get('code') == 200:
                            self._login_response_data = data
                            self.logger.info(f"✅ 已保存登录响应数据: {url}")
                    except json.JSONDecodeError:
                        self.logger.warning(f"登录响应不是有效的JSON: {url}")
        except Exception as e:
            self.logger.warning(f"处理登录响应异常: {e}")
    
    async def _extract_user_info_multi_channel(self, driver: BaseWebDriver, tokens: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        多渠道提取用户信息
        
        Args:
            driver: Web驱动实例
            tokens: Token字典
            
        Returns:
            Optional[Dict[str, Any]]: 用户信息字典
        """
        try:
            user_info = None
            
            # 渠道1: 从已保存的登录响应数据中提取
            if self._login_response_data:
                self.logger.info("尝试从已保存的登录响应中提取用户信息")
                user_info_obj = await self.user_info_extractor.extract_user_info_from_response_data(self._login_response_data)
                if user_info_obj and user_info_obj.user_id:
                    user_info = self._convert_user_info_to_dict(user_info_obj)
                    self.logger.info("✅ 从登录响应数据中提取用户信息成功")
            
            # 渠道2: 从网络历史记录中提取
            if not user_info or not user_info.get('uid'):
                self.logger.info("尝试从网络历史记录中提取用户信息")
                user_info_obj = await self.user_info_extractor.extract_user_info_from_network_history(driver.page)
                if user_info_obj and user_info_obj.user_id:
                    user_info = self._convert_user_info_to_dict(user_info_obj)
                    self.logger.info("✅ 从网络历史记录中提取用户信息成功")
            
            # 渠道3: 从JWT Token中提取
            if (not user_info or not user_info.get('uid')) and tokens.get('jwt_token'):
                self.logger.info("尝试从JWT Token中提取用户信息")
                user_info_obj = await self.user_info_extractor.extract_user_info_from_jwt_token(tokens['jwt_token'])
                if user_info_obj and user_info_obj.user_id:
                    user_info = self._convert_user_info_to_dict(user_info_obj)
                    self.logger.info("✅ 从JWT Token中提取用户信息成功")
            
            # 渠道4: 从页面请求中提取
            if not user_info or not user_info.get('uid'):
                self.logger.info("尝试从页面请求中提取用户信息")
                user_info_obj = await self.user_info_extractor.extract_user_info_from_page(driver.page)
                if user_info_obj and user_info_obj.user_id:
                    user_info = self._convert_user_info_to_dict(user_info_obj)
                    self.logger.info("✅ 从页面请求中提取用户信息成功")
            
            if user_info and user_info.get('uid'):
                # 确保distributor_id字段存在
                if not user_info.get('distributor_id') and self._login_response_data:
                    # 尝试从登录响应的account_data中提取distributor_id
                    account_data = self._login_response_data.get('data', {}).get('account_data', {})
                    if account_data.get('distributor_id'):
                        user_info['distributor_id'] = account_data['distributor_id']
                        self.logger.info(f"✅ 从account_data中补充distributor_id: {user_info['distributor_id']}")
                
                self.logger.info("✅ 多渠道用户信息提取成功", extra_data={
                    'uid': user_info.get('uid'),
                    'distributor_id': user_info.get('distributor_id'),
                    'account': user_info.get('account'),
                    'account_name': user_info.get('account_name')
                })
                
                return user_info
            else:
                self.logger.warning("❌ 所有渠道都未能提取到有效的用户信息")
                return None
                
        except Exception as e:
            self.logger.error(f"多渠道用户信息提取异常: {str(e)}")
            return None
    
    def _convert_user_info_to_dict(self, user_info_obj) -> Dict[str, Any]:
        """
        将UserInfo对象转换为字典格式
        
        Args:
            user_info_obj: UserInfo对象
            
        Returns:
            Dict[str, Any]: 用户信息字典
        """
        return {
            'uid': user_info_obj.user_id,
            'distributor_id': user_info_obj.distributor_id,
            'account': user_info_obj.account,
            'account_name': user_info_obj.account_name,
            'authority_id': getattr(user_info_obj, 'authority_id', None),
            'distributor_code': getattr(user_info_obj, 'distributor_code', None),
            'is_admin': getattr(user_info_obj, 'is_admin', None),
            'is_master': getattr(user_info_obj, 'is_master', None),
            'level_number': getattr(user_info_obj, 'level_number', None),
            'level_name': getattr(user_info_obj, 'level_name', None),
            'session_id': getattr(user_info_obj, 'session_id', None),
            'service_provider_id': getattr(user_info_obj, 'service_provider_id', None)
        }
    
    def is_network_intercepted(self) -> bool:
        """
        检查网络拦截是否已设置
        
        Returns:
            bool: 是否已设置网络拦截
        """
        return self._network_intercepted
    
    def get_login_response_data(self) -> Optional[Dict[str, Any]]:
        """
        获取保存的登录响应数据
        
        Returns:
            Optional[Dict[str, Any]]: 登录响应数据
        """
        return self._login_response_data
