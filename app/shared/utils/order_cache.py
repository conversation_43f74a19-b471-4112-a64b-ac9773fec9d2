"""
订单详情缓存

提供高效的内存缓存功能，用于缓存API响应数据。
主要功能：
1. 内存缓存 - 快速的内存级别缓存
2. 过期管理 - 自动清理过期的缓存条目
3. LRU淘汰 - 当缓存满时使用LRU策略淘汰旧数据
4. 统计信息 - 提供缓存命中率等统计数据

使用方式：
    cache = OrderDetailCache(cache_duration_minutes=30, max_cache_size=1000)
    
    # 获取缓存
    cached_data = cache.get(order_id, user_id)
    
    # 设置缓存
    cache.set(order_id, user_id, order_data)
"""

import hashlib
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from collections import OrderedDict


class OrderDetailCache:
    """
    订单详情内存缓存
    
    支持过期时间和LRU淘汰策略的内存缓存
    """
    
    def __init__(self, 
                 cache_duration_minutes: int = 30,
                 max_cache_size: int = 1000,
                 logger: Optional[logging.Logger] = None):
        """
        初始化订单详情缓存
        
        Args:
            cache_duration_minutes: 缓存持续时间（分钟）
            max_cache_size: 最大缓存条目数
            logger: 日志记录器
        """
        self.cache_duration = timedelta(minutes=cache_duration_minutes)
        self.max_cache_size = max_cache_size
        self.logger = logger or logging.getLogger(__name__)
        
        # 使用OrderedDict实现LRU
        self.cache: OrderedDict[str, Tuple[Any, datetime]] = OrderedDict()
        
        # 统计信息
        self.hit_count = 0
        self.miss_count = 0
        self.set_count = 0
        self.eviction_count = 0
        
        self.logger.info(f"✅ 订单详情缓存初始化完成 - 缓存时长: {cache_duration_minutes}分钟, 最大大小: {max_cache_size}")
    
    def _generate_cache_key(self, order_id: str, user_id: str) -> str:
        """
        生成缓存键
        
        Args:
            order_id: 订单ID
            user_id: 用户ID
            
        Returns:
            str: 缓存键
        """
        # 使用MD5哈希生成固定长度的键
        key_string = f"{order_id}_{user_id}"
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def get(self, order_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的订单详情
        
        Args:
            order_id: 订单ID
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 缓存的数据，如果不存在或过期则返回None
        """
        cache_key = self._generate_cache_key(order_id, user_id)
        
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            
            # 检查是否过期
            if datetime.now() - timestamp < self.cache_duration:
                # 命中缓存，移动到末尾（LRU更新）
                self.cache.move_to_end(cache_key)
                self.hit_count += 1
                
                age_seconds = (datetime.now() - timestamp).total_seconds()
                self.logger.debug(f"✅ 缓存命中: {order_id} - 键: {cache_key}, 年龄: {age_seconds:.1f}秒")
                
                return cached_data
            else:
                # 缓存过期，删除
                del self.cache[cache_key]
                self.logger.debug(f"⏰ 缓存过期已删除: {order_id}")
        
        # 缓存未命中
        self.miss_count += 1
        self.logger.debug(f"❌ 缓存未命中: {order_id}")
        
        return None
    
    def set(self, order_id: str, user_id: str, data: Dict[str, Any]):
        """
        设置缓存
        
        Args:
            order_id: 订单ID
            user_id: 用户ID
            data: 要缓存的数据
        """
        cache_key = self._generate_cache_key(order_id, user_id)
        
        # 检查缓存大小，如果满了则删除最旧的条目
        if len(self.cache) >= self.max_cache_size and cache_key not in self.cache:
            # 删除最旧的条目（FIFO）
            oldest_key, _ = self.cache.popitem(last=False)
            self.eviction_count += 1
            self.logger.debug(f"🗑️ 缓存满，淘汰最旧条目: {oldest_key}")
        
        # 设置缓存
        self.cache[cache_key] = (data, datetime.now())
        self.set_count += 1
        
        self.logger.debug(f"💾 缓存已设置: {order_id} - 键: {cache_key}, 缓存大小: {len(self.cache)}")
    
    def clear_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            int: 清理的条目数
        """
        current_time = datetime.now()
        expired_keys = []
        
        for cache_key, (_, timestamp) in self.cache.items():
            if current_time - timestamp >= self.cache_duration:
                expired_keys.append(cache_key)
        
        # 删除过期条目
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self.logger.info(f"🧹 清理过期缓存: {len(expired_keys)}个条目")
        
        return len(expired_keys)
    
    def clear_all(self):
        """清空所有缓存"""
        cache_size = len(self.cache)
        self.cache.clear()
        
        # 重置统计信息
        self.hit_count = 0
        self.miss_count = 0
        self.set_count = 0
        self.eviction_count = 0
        
        self.logger.info(f"🗑️ 已清空所有缓存: {cache_size}个条目")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            Dict[str, Any]: 统计信息字典
        """
        total_requests = self.hit_count + self.miss_count
        hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_size': len(self.cache),
            'max_cache_size': self.max_cache_size,
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'total_requests': total_requests,
            'hit_rate': hit_rate,
            'set_count': self.set_count,
            'eviction_count': self.eviction_count,
            'cache_duration_minutes': self.cache_duration.total_seconds() / 60
        }
    
    def log_statistics(self):
        """记录缓存统计信息到日志"""
        stats = self.get_statistics()
        
        self.logger.info(f"📊 缓存统计信息 - 大小: {stats['cache_size']}/{stats['max_cache_size']}, "
                         f"命中: {stats['hit_count']}, 未命中: {stats['miss_count']}, "
                         f"命中率: {stats['hit_rate']:.1f}%, 设置: {stats['set_count']}, 淘汰: {stats['eviction_count']}")
    
    def contains(self, order_id: str, user_id: str) -> bool:
        """
        检查缓存中是否包含指定的订单
        
        Args:
            order_id: 订单ID
            user_id: 用户ID
            
        Returns:
            bool: 是否包含且未过期
        """
        cache_key = self._generate_cache_key(order_id, user_id)
        
        if cache_key in self.cache:
            _, timestamp = self.cache[cache_key]
            return datetime.now() - timestamp < self.cache_duration
        
        return False
    
    def get_cache_info(self, order_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存条目的详细信息（不影响LRU顺序）
        
        Args:
            order_id: 订单ID
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 缓存信息，包含数据和元数据
        """
        cache_key = self._generate_cache_key(order_id, user_id)
        
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            age = datetime.now() - timestamp
            
            return {
                'data': cached_data,
                'timestamp': timestamp,
                'age_seconds': age.total_seconds(),
                'is_expired': age >= self.cache_duration,
                'cache_key': cache_key
            }
        
        return None


class CacheManager:
    """
    缓存管理器
    
    提供缓存的高级管理功能，包括自动清理、统计报告等
    """
    
    def __init__(self, cache: OrderDetailCache, auto_cleanup_interval: int = 300):
        """
        初始化缓存管理器
        
        Args:
            cache: 订单详情缓存实例
            auto_cleanup_interval: 自动清理间隔（秒）
        """
        self.cache = cache
        self.auto_cleanup_interval = auto_cleanup_interval
        self.last_cleanup_time = time.time()
        
        self.cache.logger.info(f"✅ 缓存管理器初始化完成 - 自动清理间隔: {auto_cleanup_interval}秒")
    
    async def get_with_auto_cleanup(self, order_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存数据，并自动执行清理
        
        Args:
            order_id: 订单ID
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 缓存的数据
        """
        # 检查是否需要自动清理
        current_time = time.time()
        if current_time - self.last_cleanup_time > self.auto_cleanup_interval:
            expired_count = self.cache.clear_expired()
            self.last_cleanup_time = current_time
            
            if expired_count > 0:
                self.cache.logger.debug(f"🧹 自动清理完成: {expired_count}个过期条目")
        
        return self.cache.get(order_id, user_id)
    
    def generate_cache_report(self) -> str:
        """
        生成缓存报告
        
        Returns:
            str: 格式化的缓存报告
        """
        stats = self.cache.get_statistics()
        
        report = f"""
📊 缓存性能报告
{'='*50}
缓存大小: {stats['cache_size']}/{stats['max_cache_size']} ({stats['cache_size']/stats['max_cache_size']*100:.1f}%)
命中统计: {stats['hit_count']} 命中 / {stats['miss_count']} 未命中
命中率: {stats['hit_rate']:.1f}%
操作统计: {stats['set_count']} 设置 / {stats['eviction_count']} 淘汰
缓存时长: {stats['cache_duration_minutes']:.0f} 分钟
"""
        return report.strip()
