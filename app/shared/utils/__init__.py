"""
共享工具模块

提供各种通用工具和实用程序：
- UniversalAuthManager: 通用认证管理器（基于block_manager成熟实现）
- ConcurrentOrderProcessor: 并发订单处理器
- BatchProcessor: 批处理器
- OrderDetailCache: 订单详情缓存
- CacheManager: 缓存管理器
"""

from .concurrent_processor import ConcurrentOrderProcessor, BatchProcessor
from .order_cache import OrderDetailCache, CacheManager
from .auth_manager import UniversalAuthManager

__all__ = [
    'UniversalAuthManager',
    'ConcurrentOrderProcessor',
    'BatchProcessor',
    'OrderDetailCache',
    'CacheManager'
]
