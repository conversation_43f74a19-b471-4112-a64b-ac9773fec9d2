"""
RPA应用主入口

功能：
- 动态加载业务脚本
- 解析命令行参数和环境变量
- 执行RPA任务
"""

import os
import sys
import json
import argparse
import importlib
import uuid
from typing import Dict, Any
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from app.config.config import ConfigManager
from app.utils.logger import get_rpa_logger


def main():
    """应用程序入口点"""
    # 解析命令行参数，支持环境变量作为默认值
    parser = argparse.ArgumentParser(description='RPA自动化脚本执行器')
    parser.add_argument('--business-type', '-b', 
                       default=os.getenv('BUSINESS_TYPE'), 
                       help='业务类型 (可通过BUSINESS_TYPE环境变量设置)')
    parser.add_argument('--script-name', '-s', 
                       default=os.getenv('SCRIPT_NAME'), 
                       help='脚本名称 (可通过SCRIPT_NAME环境变量设置)')
    parser.add_argument('--task-params', '-p', 
                       type=str, 
                       default=os.getenv('TASK_PARAMS'), 
                       help='任务参数(JSON格式)')
    parser.add_argument('--task-id', '-t', 
                       type=str, 
                       default=os.getenv('TASK_ID'), 
                       help='任务ID')
    
    args = parser.parse_args()
    
    # 验证必需参数
    if not args.business_type:
        print("错误: 业务类型未指定。请通过 --business-type 参数或 BUSINESS_TYPE 环境变量设置。")
        return 1
    
    if not args.script_name:
        print("错误: 脚本名称未指定。请通过 --script-name 参数或 SCRIPT_NAME 环境变量设置。")
        return 1
    
    # 生成任务ID
    task_id = args.task_id or str(uuid.uuid4())
    
    # 设置日志记录器
    logger = get_rpa_logger(args.business_type, args.script_name, task_id)
    
    try:
        logger.info(f"启动RPA应用程序", step="app_start", extra_data={
            "business_type": args.business_type,
            "script_name": args.script_name,
            "task_id": task_id
        })
        
        # 解析任务参数
        task_params = {}
        if args.task_params:
            try:
                task_params = json.loads(args.task_params)
            except json.JSONDecodeError as e:
                logger.error(f"任务参数JSON格式错误: {str(e)}", step="params_error")
                return 1
        
        # 动态加载RPA脚本
        module_path = f"app.business.{args.business_type}.{args.script_name}"
        module = importlib.import_module(module_path)
        
        # 查找RPA类（支持同步和异步版本）
        rpa_class = None
        rpa_candidates = []
        
        # 收集所有候选的RPA类
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if (isinstance(attr, type) and
                hasattr(attr, '__bases__') and
                any('BaseRPA' in base.__name__ or 'AsyncBaseRPA' in base.__name__ for base in attr.__mro__)):
                rpa_candidates.append((attr_name, attr))
        
        # 调试信息：打印所有找到的候选类
        logger.info(f"找到候选RPA类: {[name for name, _ in rpa_candidates]}", step="candidates_found")
        
        # 优先选择非基类的业务实现类
        for attr_name, attr in rpa_candidates:
            logger.debug(f"检查候选类: {attr_name}")
            # 排除基类本身（AsyncBaseRPA, BaseRPA等）
            if attr_name in ['AsyncBaseRPA', 'BaseRPA', 'AsyncRPA', 'RPA']:
                logger.debug(f"跳过基类: {attr_name}")
                continue
            # 排除以Base开头的类（通常是基类）
            if attr_name.startswith('Base') or attr_name.startswith('Abstract'):
                logger.debug(f"跳过Base/Abstract类: {attr_name}")
                continue
            # 选择第一个符合条件的业务类
                rpa_class = attr
            logger.info(f"找到RPA业务类: {attr_name}", step="class_found", extra_data={
                "class_name": attr_name,
                "module_path": module_path,
                "is_async": any('AsyncBaseRPA' in base.__name__ for base in attr.__mro__)
            })
                break
        
        # 如果没有找到业务类，使用第一个候选类（向后兼容）
        if rpa_class is None and rpa_candidates:
            attr_name, attr = rpa_candidates[0]
            rpa_class = attr
            logger.warning(f"未找到明确的业务类，使用候选类: {attr_name}", step="class_fallback")
        
        # 如果仍然没有类，记录详细错误信息
        if rpa_class is None:
            logger.error(f"模块 {module_path} 中没有找到任何RPA类", step="no_class_found", extra_data={
                "module_attributes": [name for name in dir(module) if not name.startswith('_')],
                "candidates": len(rpa_candidates)
            })
        
        if rpa_class is None:
            logger.error(f"未找到RPA类: {module_path}", step="class_not_found")
            return 1
        
        # 检查是否为异步RPA类
        is_async_rpa = any('AsyncBaseRPA' in base.__name__ for base in rpa_class.__mro__)

        # 创建RPA实例（根据类型使用不同的构造参数）
        if is_async_rpa:
            # 异步RPA类只需要task_id参数
            rpa_instance = rpa_class(task_id=task_id)
        else:
            # 同步RPA类需要完整的参数
            rpa_instance = rpa_class(
                business_type=args.business_type,
                script_name=args.script_name,
                task_params=task_params,
                task_id=task_id
            )

        # 执行RPA任务
        if is_async_rpa:
            # 异步执行
            import asyncio
            result = asyncio.run(rpa_instance.execute())
        else:
            # 同步执行
            result = rpa_instance.execute()
        
        # 输出结果
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        return 0 if result["status"] == "success" else 1
        
    except Exception as e:
        logger.error(f"应用程序执行失败: {str(e)}", step="app_error", exception=e)
        return 1


if __name__ == "__main__":
    sys.exit(main()) 