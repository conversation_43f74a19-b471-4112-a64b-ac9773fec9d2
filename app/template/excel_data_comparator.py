#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据比较工具
功能：比较两个Excel文件中特定列的数据差异
作者：RPA-K8S项目
"""

import os
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import json
from datetime import datetime


class ExcelDataComparator:
    """Excel数据比较器"""
    
    def __init__(self, excel1_path: str, excel2_path: str):
        """
        初始化Excel数据比较器
        
        Args:
            excel1_path: 第一个Excel文件路径
            excel2_path: 第二个Excel文件路径
        """
        self.excel1_path = excel1_path
        self.excel2_path = excel2_path
        self.excel1_data = None
        self.excel2_data = None
        self.comparison_results = []
        
    def load_excel_files(self, sheet_name1: str = 0, sheet_name2: str = 0) -> bool:
        """
        加载两个Excel文件
        
        Args:
            sheet_name1: Excel1的sheet名称或索引（默认第一个sheet）
            sheet_name2: Excel2的sheet名称或索引（默认第一个sheet）
            
        Returns:
            是否加载成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(self.excel1_path):
                print(f"❌ Excel1文件不存在: {self.excel1_path}")
                return False
                
            if not os.path.exists(self.excel2_path):
                print(f"❌ Excel2文件不存在: {self.excel2_path}")
                return False
            
            # 加载Excel文件
            self.excel1_data = pd.read_excel(self.excel1_path, sheet_name=sheet_name1)
            self.excel2_data = pd.read_excel(self.excel2_path, sheet_name=sheet_name2)
            
            print(f"✅ 成功加载Excel1: {os.path.basename(self.excel1_path)} (行数: {len(self.excel1_data)})")
            print(f"✅ 成功加载Excel2: {os.path.basename(self.excel2_path)} (行数: {len(self.excel2_data)})")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载Excel文件失败: {e}")
            return False
    
    def get_available_columns(self) -> Tuple[List[str], List[str]]:
        """
        获取两个Excel文件的可用列名
        
        Returns:
            (excel1_columns, excel2_columns) 元组
        """
        if self.excel1_data is None or self.excel2_data is None:
            return [], []
            
        return self.excel1_data.columns.tolist(), self.excel2_data.columns.tolist()
    
    def compare_data(self, 
                    key_column: str, 
                    compare_column: str, 
                    excel1_sheet: str = 0, 
                    excel2_sheet: str = 0) -> List[Dict]:
        """
        比较两个Excel文件中的数据差异
        
        Args:
            key_column: 用于匹配的关键列名（列名1）
            compare_column: 需要比较的列名（列名2）
            excel1_sheet: Excel1的sheet名称或索引
            excel2_sheet: Excel2的sheet名称或索引
            
        Returns:
            包含差异数据的列表
        """
        # 加载Excel文件
        if not self.load_excel_files(excel1_sheet, excel2_sheet):
            return []
        
        # 检查列名是否存在
        excel1_cols, excel2_cols = self.get_available_columns()
        
        if key_column not in excel1_cols:
            print(f"❌ Excel1中不存在列名: {key_column}")
            print(f"可用列名: {excel1_cols}")
            return []
            
        if compare_column not in excel1_cols:
            print(f"❌ Excel1中不存在列名: {compare_column}")
            print(f"可用列名: {excel1_cols}")
            return []
            
        if key_column not in excel2_cols:
            print(f"❌ Excel2中不存在列名: {key_column}")
            print(f"可用列名: {excel2_cols}")
            return []
            
        if compare_column not in excel2_cols:
            print(f"❌ Excel2中不存在列名: {compare_column}")
            print(f"可用列名: {excel2_cols}")
            return []
        
        print(f"\n🔍 开始比较数据...")
        print(f"关键列: {key_column}")
        print(f"比较列: {compare_column}")
        print(f"Excel1行数: {len(self.excel1_data)}")
        print(f"Excel2行数: {len(self.excel2_data)}")
        
        # 创建Excel2的查找字典，提高查找效率
        excel2_dict = {}
        for _, row in self.excel2_data.iterrows():
            key_value = row[key_column]
            compare_value = row[compare_column]
            excel2_dict[str(key_value)] = compare_value
        
        print(f"Excel2字典构建完成，包含 {len(excel2_dict)} 个键值对")
        
        # 比较数据
        differences = []
        processed_count = 0
        found_differences = 0
        
        for index, row in self.excel1_data.iterrows():
            processed_count += 1
            
            # 获取当前行的关键列值和比较列值
            excel1_key = row[key_column]
            excel1_compare_value = row[compare_column]
            
            # 检查Excel1的比较列是否有值（不为空、NaN等）
            if pd.isna(excel1_compare_value) or excel1_compare_value == "" or excel1_compare_value is None:
                continue
            
            # 在Excel2中查找匹配的key
            excel1_key_str = str(excel1_key)
            if excel1_key_str in excel2_dict:
                excel2_compare_value = excel2_dict[excel1_key_str]
                
                # 比较两个值是否不一致
                if str(excel1_compare_value) != str(excel2_compare_value):
                    found_differences += 1
                    
                    # 记录差异数据
                    difference_record = {
                        "序号": index + 1,
                        "Excel1行号": index + 2,  # Excel行号从2开始（包含表头）
                        "关键列名": key_column,
                        "关键列值": excel1_key,
                        "比较列名": compare_column,
                        "Excel1_比较列值": excel1_compare_value,
                        "Excel2_比较列值": excel2_compare_value,
                        "Excel1完整行数据": row.to_dict(),
                        "差异类型": "值不一致"
                    }
                    
                    differences.append(difference_record)
                    
                    if found_differences <= 5:  # 只显示前5个差异的详细信息
                        print(f"  🔸 发现差异 #{found_differences}: {key_column}='{excel1_key}' | "
                              f"Excel1[{compare_column}]='{excel1_compare_value}' | "
                              f"Excel2[{compare_column}]='{excel2_compare_value}'")
            else:
                # 在Excel2中未找到匹配的key
                difference_record = {
                    "序号": index + 1,
                    "Excel1行号": index + 2,
                    "关键列名": key_column,
                    "关键列值": excel1_key,
                    "比较列名": compare_column,
                    "Excel1_比较列值": excel1_compare_value,
                    "Excel2_比较列值": "❌ 未找到匹配项",
                    "Excel1完整行数据": row.to_dict(),
                    "差异类型": "Excel2中无匹配项"
                }
                
                differences.append(difference_record)
                found_differences += 1
        
        self.comparison_results = differences
        
        print(f"\n📊 比较完成!")
        print(f"处理行数: {processed_count}")
        print(f"发现差异: {found_differences}")
        
        return differences
    
    def format_comparison_results(self, output_format: str = "pretty") -> str:
        """
        格式化比较结果
        
        Args:
            output_format: 输出格式 ('pretty', 'json', 'summary', 'detailed')
            
        Returns:
            格式化后的结果字符串
        """
        if not self.comparison_results:
            return "❌ 没有比较结果，请先执行 compare_data() 方法"
        
        if output_format == "json":
            return json.dumps(self.comparison_results, ensure_ascii=False, indent=2, default=str)
        
        elif output_format == "summary":
            return self._format_summary_output()
        
        elif output_format == "detailed":
            return self._format_detailed_output()
        
        else:  # pretty format
            return self._format_pretty_output()
    
    def _format_pretty_output(self) -> str:
        """格式化为美观的输出"""
        output = []
        output.append("=" * 100)
        output.append(f"📊 Excel数据比较结果")
        output.append("=" * 100)
        output.append(f"Excel1文件: {os.path.basename(self.excel1_path)}")
        output.append(f"Excel2文件: {os.path.basename(self.excel2_path)}")
        output.append(f"发现差异数量: {len(self.comparison_results)}")
        output.append(f"比较时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        output.append("")
        
        if not self.comparison_results:
            output.append("✅ 未发现数据差异")
            return "\n".join(output)
        
        for i, diff in enumerate(self.comparison_results, 1):
            output.append("-" * 80)
            output.append(f"🔸 差异 #{i}")
            output.append("-" * 80)
            output.append(f"位置: Excel1第{diff['Excel1行号']}行")
            output.append(f"关键字段: {diff['关键列名']} = '{diff['关键列值']}'")
            output.append(f"比较字段: {diff['比较列名']}")
            output.append(f"  ├─ Excel1值: '{diff['Excel1_比较列值']}'")
            output.append(f"  └─ Excel2值: '{diff['Excel2_比较列值']}'")
            output.append(f"差异类型: {diff['差异类型']}")
            output.append("")
        
        output.append("=" * 100)
        return "\n".join(output)
    
    def _format_summary_output(self) -> str:
        """格式化为摘要输出"""
        output = []
        output.append(f"📋 数据比较摘要")
        output.append(f"总差异数: {len(self.comparison_results)}")
        
        # 按差异类型分组统计
        type_counts = {}
        for diff in self.comparison_results:
            diff_type = diff['差异类型']
            type_counts[diff_type] = type_counts.get(diff_type, 0) + 1
        
        output.append("\n差异类型统计:")
        for diff_type, count in type_counts.items():
            output.append(f"  • {diff_type}: {count}个")
        
        return "\n".join(output)
    
    def _format_detailed_output(self) -> str:
        """格式化为详细输出"""
        output = []
        output.append("📋 详细差异报告\n")
        
        for i, diff in enumerate(self.comparison_results, 1):
            output.append(f"【差异 #{i}】")
            output.append(f"Excel1行号: {diff['Excel1行号']}")
            output.append(f"关键字段: {diff['关键列名']} = {diff['关键列值']}")
            output.append(f"比较字段: {diff['比较列名']}")
            output.append(f"Excel1值: {diff['Excel1_比较列值']}")
            output.append(f"Excel2值: {diff['Excel2_比较列值']}")
            output.append(f"差异类型: {diff['差异类型']}")
            
            # 显示完整行数据（前5个字段）
            row_data = diff['Excel1完整行数据']
            output.append("Excel1完整行数据:")
            for j, (col, val) in enumerate(row_data.items()):
                if j < 8:  # 只显示前8个字段
                    output.append(f"  {col}: {val}")
                elif j == 8:
                    output.append(f"  ... (共{len(row_data)}个字段)")
                    break
            
            output.append("-" * 60)
        
        return "\n".join(output)
    
    def save_results_to_excel(self, output_file: str) -> bool:
        """
        保存比较结果到Excel文件
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            是否保存成功
        """
        try:
            if not self.comparison_results:
                print("❌ 没有比较结果可保存")
                return False
            
            # 准备数据
            save_data = []
            for diff in self.comparison_results:
                record = {
                    "序号": diff["序号"],
                    "Excel1行号": diff["Excel1行号"],
                    "关键列名": diff["关键列名"],
                    "关键列值": diff["关键列值"],
                    "比较列名": diff["比较列名"],
                    "Excel1_比较列值": diff["Excel1_比较列值"],
                    "Excel2_比较列值": diff["Excel2_比较列值"],
                    "差异类型": diff["差异类型"]
                }
                
                # 添加Excel1的其他列数据
                row_data = diff["Excel1完整行数据"]
                for col, val in row_data.items():
                    if col not in [diff["关键列名"], diff["比较列名"]]:
                        record[f"Excel1_{col}"] = val
                
                save_data.append(record)
            
            # 保存到Excel
            df = pd.DataFrame(save_data)
            df.to_excel(output_file, index=False, engine='openpyxl')
            
            print(f"✅ 比较结果已保存到: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存Excel文件失败: {e}")
            return False
    
    def save_results_to_json(self, output_file: str) -> bool:
        """
        保存比较结果到JSON文件
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            是否保存成功
        """
        try:
            if not self.comparison_results:
                print("❌ 没有比较结果可保存")
                return False
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.comparison_results, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 比较结果已保存到: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存JSON文件失败: {e}")
            return False


def compare_excel_files(excel1_path: str, 
                       excel2_path: str, 
                       key_column: str, 
                       compare_column: str,
                       output_format: str = "pretty",
                       save_to_excel: Optional[str] = None,
                       save_to_json: Optional[str] = None) -> str:
    """
    比较两个Excel文件的便捷函数
    
    Args:
        excel1_path: Excel1文件路径
        excel2_path: Excel2文件路径
        key_column: 关键列名（用于匹配）
        compare_column: 比较列名（用于比较差异）
        output_format: 输出格式
        save_to_excel: 保存Excel文件路径（可选）
        save_to_json: 保存JSON文件路径（可选）
        
    Returns:
        格式化的比较结果
    """
    comparator = ExcelDataComparator(excel1_path, excel2_path)
    
    # 执行比较
    differences = comparator.compare_data(key_column, compare_column)
    
    # 格式化结果
    result = comparator.format_comparison_results(output_format)
    
    # 保存文件（如果指定）
    if save_to_excel:
        comparator.save_results_to_excel(save_to_excel)
    
    if save_to_json:
        comparator.save_results_to_json(save_to_json)
    
    return result


# 示例使用
if __name__ == "__main__":
    print("🚀 Excel数据比较工具")
    print("=" * 60)
    
    # 示例文件路径（请替换为实际路径）
    excel1 = r'C:\Users\<USER>\Desktop\拆分excel\账户明细-原.xlsx'
    excel2 = r'C:\Users\<USER>\Desktop\拆分excel\trade_detail_更新后.xlsx'
    
    if os.path.exists(excel1) and os.path.exists(excel2):
        # 创建比较器
        comparator = ExcelDataComparator(excel1, excel2)
        
        # # 查看可用列名
        # comparator.load_excel_files()
        # cols1, cols2 = comparator.get_available_columns()
        # print(f"Excel1列名: {cols1[:5]}...")  # 显示前5个列名
        # print(f"Excel2列名: {cols2[:5]}...")
        
        # 执行比较（示例）
        differences = comparator.compare_data("交易流水号", "店铺")
        result = comparator.format_comparison_results("pretty")
        print(result)
        
    else:
        print("❌ 示例文件不存在")
        print("\n使用方法:")
        print("1. 设置正确的Excel文件路径")
        print("2. 调用 compare_excel_files() 函数")
        print("3. 指定关键列名和比较列名")
        print("\n示例代码:")
        print("result = compare_excel_files('file1.xlsx', 'file2.xlsx', 'ID', 'Name')")
        print("print(result)") 