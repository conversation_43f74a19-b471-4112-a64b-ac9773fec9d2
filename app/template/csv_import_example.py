"""
CSV导入工具使用示例

演示如何使用CSVToDBImporter类导入CSV文件到数据库。
"""

import os
from pathlib import Path
from app.template.csv_to_db_importer import CSVToDBImporter


def main():
    """主函数：演示CSV导入工具的使用"""
    
    print("=== CSV导入工具使用示例 ===\n")
    
    # 创建导入器实例
    importer = CSVToDBImporter(
        business_type="csv_template_example",
        script_name="import_demo"
    )
    
    # 示例1：检查数据库统计信息
    print("1. 获取数据库统计信息：")
    stats = importer.get_database_statistics()
    if 'error' not in stats:
        print(f"   总记录数: {stats['total_count']}")
        print(f"   今日新增: {stats['today_count']}")
        if stats['platform_stats']:
            print("   按平台统计:")
            for platform_stat in stats['platform_stats'][:5]:
                print(f"     {platform_stat['platform']}: {platform_stat['count']}条")
    else:
        print(f"   获取统计信息失败: {stats['error']}")
    
    print()

    # 示例2：验证CSV文件（如果有的话）
    print("2. CSV文件验证示例：")

    # # 查找项目中可能存在的CSV文件
    # project_root = Path(__file__).parent.parent.parent
    # sample_csv_paths = [
    #     project_root / "sample_data.csv",
    #     project_root / "test_data.csv",
    #     project_root / "app" / "business" / "export_publish_list" / "downloads" / "*.csv"
    # ]
    #
    # # 检查downloads目录下是否有CSV文件
    # downloads_dir = project_root / "app" / "business" / "export_publish_list" / "downloads"
    # csv_files = []
    # if downloads_dir.exists():
    #     csv_files = list(downloads_dir.glob("*.csv"))
    #
    # if csv_files:
    #     sample_csv = csv_files[0]
    #     print(f"   发现CSV文件: {sample_csv.name}")
    #
    #     # 验证文件
    #     validation_result = importer.validate_csv_file(str(sample_csv))
    #     print(f"   验证结果: {validation_result['message']}")
    #     print(f"   记录数量: {validation_result['records_count']}")
    #
    #     if validation_result['sample_records']:
    #         print("   前3条记录字段示例:")
    #         for i, record in enumerate(validation_result['sample_records'][:3], 1):
    #             print(f"     记录{i}: seller_sku_child={record.get('seller_sku_child', 'N/A')}, "
    #                   f"account={record.get('account', 'N/A')}, "
    #                   f"platform={record.get('platform', 'N/A')}")
    #
    #     print()
        
        # 示例3：实际导入（谨慎执行）
    print("3. 导入示例（可选）：")
    user_input = input("   是否要执行实际的CSV导入？这将向数据库插入/更新数据 (y/N): ").strip().lower()

    if user_input == 'y':
        print("   开始导入...")
        custom_db_config = {
            'host': 'rm-bp151ouf41d67hk3v4o.mysql.rds.aliyuncs.com',  # 数据库主机地址
            'port': '3306',  # 端口号
            'username': 'lingyi888',  # 用户名
            'password': '2019hngkzyP',  # 密码
            'database': 'lingyi'  # 数据库名
        }

        # 创建使用自定义数据库的导入器
        custom_importer = CSVToDBImporter(
            business_type="custom_import",
            script_name="custom_script",
            database_config=custom_db_config  # 传入自定义配置
        )

        # 使用自定义数据库执行导入
        result = custom_importer.import_csv_to_db(r'C:\Users\<USER>\Downloads\【刊登成功列表】-列表导出-2025-06-30-17-00-39-9821merge1751276608.csv')

        print(f"   导入结果: {result['message']}")
        print(f"   处理时间: {result['process_time']}秒")
        print(f"   总记录数: {result['total_records']}")
        print(f"   新增记录: {result['insert_count']}")
        print(f"   更新记录: {result['update_count']}")
        print(f"   跳过记录: {result['skip_count']}")
        print(f"   错误记录: {result['error_count']}")

        if result['file_info']:
            print(f"   文件大小: {result['file_info']['file_size_mb']}MB")
    else:
        print("   跳过实际导入")

    
    print()
    
    # 示例4：代码使用示例
    print("4. 代码使用示例：")
    print("""
# 基本使用（默认数据库配置）
from app.template.csv_to_db_importer import CSVToDBImporter

# 创建导入器
importer = CSVToDBImporter()

# 导入CSV文件
result = importer.import_csv_to_db('path/to/your/file.csv')

# 检查结果
if result['success']:
    print(f"导入成功：新增 {result['insert_count']} 条，更新 {result['update_count']} 条")
else:
    print(f"导入失败：{result['message']}")

# 强制更新模式（更新所有已存在记录）
result = importer.import_csv_to_db('path/to/your/file.csv', force_update=True)

# 验证文件而不导入
validation = importer.validate_csv_file('path/to/your/file.csv')
if validation['valid']:
    print(f"文件有效，包含 {validation['records_count']} 条记录")
""")
    
    # 示例5：自定义数据库配置
    print("5. 自定义数据库配置示例：")
    print("""
# 自定义数据库配置
custom_db_config = {
    'host': 'your-db-host.com',      # 数据库主机地址
    'port': '3306',                  # 端口号
    'username': 'your_username',     # 用户名
    'password': 'your_password',     # 密码
    'database': 'your_database'      # 数据库名
}

# 创建使用自定义数据库的导入器
custom_importer = CSVToDBImporter(
    business_type="custom_import",
    script_name="custom_script",
    database_config=custom_db_config  # 传入自定义配置
)

# 使用自定义数据库执行导入
result = custom_importer.import_csv_to_db('path/to/your/file.csv')

# 多环境配置示例
environments = {
    'production': {
        'host': 'prod-db.example.com',
        'port': '3306',
        'username': 'prod_user',
        'password': 'prod_password',
        'database': 'prod_database'
    },
    'testing': {
        'host': 'test-db.example.com',
        'port': '3306',
        'username': 'test_user',
        'password': 'test_password',
        'database': 'test_database'
    },
    'development': {
        'host': 'localhost',
        'port': '3306',
        'username': 'dev_user',
        'password': 'dev_password',
        'database': 'dev_database'
    }
}

# 根据环境选择配置
env = 'development'  # 或从环境变量获取
importer = CSVToDBImporter(database_config=environments[env])

# 使用配置好的导入器
result = importer.import_csv_to_db('data.csv')
""")
    
    print("\n=== 示例完成 ===")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n执行出错: {str(e)}")
        import traceback
        traceback.print_exc() 