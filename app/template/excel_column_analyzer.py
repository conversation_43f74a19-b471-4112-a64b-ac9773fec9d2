#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel列信息解析工具
功能：解析Excel文档中每个sheet的列名和列号信息
作者：RPA-K8S项目
"""

import os
import pandas as pd
from openpyxl import load_workbook
from typing import Dict, List, Tuple, Optional
import json


class ExcelColumnAnalyzer:
    """Excel列信息分析器"""
    
    def __init__(self, file_path: str):
        """
        初始化Excel分析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.workbook = None
        self.sheets_info = {}
        
    def _number_to_excel_column(self, col_num: int) -> str:
        """
        将数字列号转换为Excel列标（A、B、C...AA、AB等）
        
        Args:
            col_num: 列号（从1开始）
            
        Returns:
            Excel列标字符串
        """
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result
    
    def load_excel(self) -> bool:
        """
        加载Excel文件
        
        Returns:
            是否加载成功
        """
        try:
            if not os.path.exists(self.file_path):
                print(f"❌ 文件不存在: {self.file_path}")
                return False
                
            self.workbook = load_workbook(self.file_path, read_only=True)
            print(f"✅ 成功加载Excel文件: {self.file_path}")
            return True
            
        except Exception as e:
            print(f"❌ 加载Excel文件失败: {e}")
            return False
    
    def analyze_sheet(self, sheet_name: str) -> Dict:
        """
        分析单个sheet的列信息
        
        Args:
            sheet_name: sheet名称
            
        Returns:
            包含列信息的字典
        """
        try:
            # 使用pandas读取sheet数据
            df = pd.read_excel(self.file_path, sheet_name=sheet_name, nrows=0)
            
            # 获取列名
            columns = df.columns.tolist()
            
            # 构建列信息
            column_info = []
            for idx, col_name in enumerate(columns, 1):
                excel_col = self._number_to_excel_column(idx)
                column_info.append({
                    "列号": idx,
                    "Excel列标": excel_col,
                    "列名": str(col_name),
                    "列名类型": type(col_name).__name__
                })
            
            sheet_info = {
                "sheet名称": sheet_name,
                "总列数": len(columns),
                "列信息": column_info
            }
            
            return sheet_info
            
        except Exception as e:
            print(f"❌ 分析sheet '{sheet_name}' 时出错: {e}")
            return {
                "sheet名称": sheet_name,
                "错误": str(e),
                "总列数": 0,
                "列信息": []
            }
    
    def analyze_all_sheets(self) -> Dict:
        """
        分析所有sheet的列信息
        
        Returns:
            包含所有sheet信息的字典
        """
        if not self.workbook:
            if not self.load_excel():
                return {}
        
        all_sheets_info = {
            "文件路径": self.file_path,
            "文件名": os.path.basename(self.file_path),
            "总sheet数": len(self.workbook.sheetnames),
            "sheet列表": self.workbook.sheetnames,
            "详细信息": {}
        }
        
        print(f"\n📊 开始分析 {len(self.workbook.sheetnames)} 个sheet...")
        
        for sheet_name in self.workbook.sheetnames:
            print(f"   🔍 正在分析: {sheet_name}")
            sheet_info = self.analyze_sheet(sheet_name)
            all_sheets_info["详细信息"][sheet_name] = sheet_info
        
        self.sheets_info = all_sheets_info
        return all_sheets_info
    
    def format_output(self, output_format: str = "pretty") -> str:
        """
        格式化输出结果
        
        Args:
            output_format: 输出格式 ('pretty', 'json', 'table')
            
        Returns:
            格式化后的字符串
        """
        if not self.sheets_info:
            return "❌ 没有可用的分析结果，请先调用 analyze_all_sheets()"
        
        if output_format == "json":
            return json.dumps(self.sheets_info, ensure_ascii=False, indent=2)
        
        elif output_format == "table":
            return self._format_table_output()
        
        else:  # pretty format
            return self._format_pretty_output()
    
    def _format_pretty_output(self) -> str:
        """格式化为美观的输出"""
        output = []
        output.append("=" * 80)
        output.append(f"📄 Excel文件分析结果")
        output.append("=" * 80)
        output.append(f"文件路径: {self.sheets_info['文件路径']}")
        output.append(f"文件名: {self.sheets_info['文件名']}")
        output.append(f"总sheet数: {self.sheets_info['总sheet数']}")
        output.append(f"sheet列表: {', '.join(self.sheets_info['sheet列表'])}")
        output.append("")
        
        for sheet_name, sheet_info in self.sheets_info["详细信息"].items():
            output.append("-" * 60)
            output.append(f"📋 Sheet: {sheet_name}")
            output.append("-" * 60)
            
            if "错误" in sheet_info:
                output.append(f"❌ 错误: {sheet_info['错误']}")
                output.append("")
                continue
            
            output.append(f"总列数: {sheet_info['总列数']}")
            output.append("")
            output.append("列信息:")
            
            if sheet_info['列信息']:
                for col_info in sheet_info['列信息']:
                    output.append(f"  • {col_info['Excel列标']}列 (第{col_info['列号']}列): {col_info['列名']}")
            else:
                output.append("  (无列信息)")
            
            output.append("")
        
        output.append("=" * 80)
        return "\n".join(output)
    
    def _format_table_output(self) -> str:
        """格式化为表格输出"""
        output = []
        
        for sheet_name, sheet_info in self.sheets_info["详细信息"].items():
            output.append(f"\n📋 {sheet_name}")
            output.append("-" * 50)
            
            if "错误" in sheet_info:
                output.append(f"错误: {sheet_info['错误']}")
                continue
            
            # 表头
            output.append(f"{'列号':<6} {'Excel列标':<10} {'列名':<30}")
            output.append("-" * 50)
            
            # 数据行
            for col_info in sheet_info['列信息']:
                col_name = col_info['列名'][:28] + "..." if len(col_info['列名']) > 30 else col_info['列名']
                output.append(f"{col_info['列号']:<6} {col_info['Excel列标']:<10} {col_name:<30}")
        
        return "\n".join(output)
    
    def save_to_file(self, output_file: str, output_format: str = "json") -> bool:
        """
        保存分析结果到文件
        
        Args:
            output_file: 输出文件路径
            output_format: 输出格式
            
        Returns:
            是否保存成功
        """
        try:
            content = self.format_output(output_format)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 分析结果已保存到: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False


def analyze_excel_file(file_path: str, output_format: str = "pretty", save_to: Optional[str] = None) -> str:
    """
    分析Excel文件的便捷函数
    
    Args:
        file_path: Excel文件路径
        output_format: 输出格式 ('pretty', 'json', 'table')
        save_to: 保存文件路径（可选）
        
    Returns:
        格式化的分析结果字符串
    """
    analyzer = ExcelColumnAnalyzer(file_path)
    
    # 分析所有sheet
    analyzer.analyze_all_sheets()
    
    # 格式化输出
    result = analyzer.format_output(output_format)
    
    # 保存到文件（如果指定）
    if save_to:
        analyzer.save_to_file(save_to, output_format)
    
    return result


# 示例使用
if __name__ == "__main__":
    # 使用示例
    print("🚀 Excel列信息分析工具")
    print("=" * 50)
    
    # 请将下面的文件路径替换为您的Excel文件路径
    excel_file = "example.xlsx"  # 请替换为实际文件路径
    
    if os.path.exists(excel_file):
        # 分析Excel文件
        result = analyze_excel_file(excel_file, output_format="pretty")
        print(result)
        
        # 也可以保存为JSON格式
        analyze_excel_file(excel_file, output_format="json", save_to="excel_analysis.json")
        
    else:
        print(f"❌ 示例文件 '{excel_file}' 不存在")
        print("\n使用方法:")
        print("1. 将您的Excel文件路径替换到 excel_file 变量")
        print("2. 或者直接调用 analyze_excel_file('您的文件路径.xlsx')")
        print("\n支持的输出格式:")
        print("- 'pretty': 美观的格式化输出")
        print("- 'json': JSON格式")
        print("- 'table': 表格格式")
