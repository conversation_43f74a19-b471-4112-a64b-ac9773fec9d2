"""
Excel模板工具类
用于处理Excel文件的读取、拆分和导出功能
"""

import os
import pandas as pd
from typing import Dict, List, Optional, Union, Any
from pathlib import Path
import logging
import tempfile
import shutil
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter


class ExcelTemplateUtil:
    """
    Excel模板处理工具类
    
    功能：
    - 读取Excel文件
    - 按Sheet拆分为多个表格
    - 导出到目标目录
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化Excel模板工具
        
        Args:
            logger: 日志记录器，如果未提供则创建默认logger
        """
        self.logger = logger or self._create_default_logger()
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def read_excel_sheets(
        self, 
        excel_path: Union[str, Path], 
        handle_merged_cells: bool = True
    ) -> Dict[str, pd.DataFrame]:
        """
        读取Excel文件的所有Sheet
        
        Args:
            excel_path: Excel文件路径
            handle_merged_cells: 是否处理合并单元格，默认True
            
        Returns:
            Dict[str, pd.DataFrame]: 以Sheet名为键，DataFrame为值的字典
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式不支持
        """
        excel_path = Path(excel_path)
        
        if not excel_path.exists():
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")
        
        if excel_path.suffix.lower() not in ['.xlsx', '.xls']:
            raise ValueError(f"不支持的文件格式: {excel_path.suffix}")
        
        try:
            self.logger.info(f"开始读取Excel文件: {excel_path}")
            
            # 如果需要处理合并单元格，先处理后再读取
            if handle_merged_cells:
                processed_file = self._process_merged_cells(excel_path)
                try:
                    # 读取处理后的文件
                    sheets_dict = pd.read_excel(processed_file, sheet_name=None, engine='openpyxl')
                finally:
                    # 清理临时文件（确保不是原文件）
                    if processed_file != str(excel_path) and os.path.exists(processed_file):
                        try:
                            os.remove(processed_file)
                            self.logger.debug(f"已清理临时文件: {processed_file}")
                        except PermissionError:
                            self.logger.warning(f"无法删除临时文件（文件可能被占用）: {processed_file}")
            else:
                # 直接读取原文件
                sheets_dict = pd.read_excel(excel_path, sheet_name=None, engine='openpyxl')
            
            self.logger.info(f"成功读取Excel文件，共{len(sheets_dict)}个Sheet: {list(sheets_dict.keys())}")
            
            return sheets_dict
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def split_sheets_to_files(
        self, 
        excel_path: Union[str, Path], 
        target_dir: Union[str, Path],
        file_format: str = 'xlsx',
        include_sheets: Optional[List[str]] = None,
        exclude_sheets: Optional[List[str]] = None,
        handle_merged_cells: bool = True
    ) -> Dict[str, str]:
        """
        将Excel文件的每个Sheet拆分为独立的文件
        
        Args:
            excel_path: 源Excel文件路径
            target_dir: 目标目录路径
            file_format: 输出文件格式 ('xlsx', 'csv', 'json')
            include_sheets: 包含的Sheet名列表，None表示包含所有
            exclude_sheets: 排除的Sheet名列表
            handle_merged_cells: 是否处理合并单元格，默认True
            
        Returns:
            Dict[str, str]: Sheet名到输出文件路径的映射
            
        Raises:
            ValueError: 参数错误
            OSError: 目录创建失败
        """
        target_dir = Path(target_dir)
        
        # 创建目标目录
        try:
            target_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"目标目录已准备: {target_dir}")
        except OSError as e:
            self.logger.error(f"创建目标目录失败: {e}")
            raise
        
        # 读取所有Sheet（包含合并单元格处理）
        sheets_dict = self.read_excel_sheets(excel_path, handle_merged_cells)
        
        # 过滤Sheet
        filtered_sheets = self._filter_sheets(
            sheets_dict, include_sheets, exclude_sheets
        )
        
        # 导出文件
        output_files = {}
        
        for sheet_name, df in filtered_sheets.items():
            try:
                # 清理Sheet名，确保可以作为文件名
                safe_sheet_name = self._sanitize_filename(sheet_name)
                
                # 生成输出文件路径
                output_file = target_dir / f"{safe_sheet_name}.{file_format}"
                
                # 根据格式导出
                self._export_dataframe(df, output_file, file_format)
                
                output_files[sheet_name] = str(output_file)
                self.logger.info(f"Sheet '{sheet_name}' 已导出到: {output_file}")
                
            except Exception as e:
                self.logger.error(f"导出Sheet '{sheet_name}' 失败: {e}")
                continue
        
        self.logger.info(f"拆分完成，共导出{len(output_files)}个文件")
        return output_files
    
    def _filter_sheets(
        self, 
        sheets_dict: Dict[str, pd.DataFrame],
        include_sheets: Optional[List[str]] = None,
        exclude_sheets: Optional[List[str]] = None
    ) -> Dict[str, pd.DataFrame]:
        """
        过滤Sheet
        
        Args:
            sheets_dict: 原始Sheet字典
            include_sheets: 包含的Sheet名列表
            exclude_sheets: 排除的Sheet名列表
            
        Returns:
            Dict[str, pd.DataFrame]: 过滤后的Sheet字典
        """
        filtered_sheets = sheets_dict.copy()
        
        # 应用包含过滤器
        if include_sheets:
            filtered_sheets = {
                name: df for name, df in filtered_sheets.items() 
                if name in include_sheets
            }
            self.logger.info(f"应用包含过滤器，保留Sheet: {list(filtered_sheets.keys())}")
        
        # 应用排除过滤器
        if exclude_sheets:
            filtered_sheets = {
                name: df for name, df in filtered_sheets.items() 
                if name not in exclude_sheets
            }
            self.logger.info(f"应用排除过滤器，剩余Sheet: {list(filtered_sheets.keys())}")
        
        return filtered_sheets
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除不安全字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的安全文件名
        """
        # 移除或替换不安全字符
        unsafe_chars = '<>:"/\\|?*'
        safe_filename = filename
        
        for char in unsafe_chars:
            safe_filename = safe_filename.replace(char, '_')
        
        # 移除前后空格
        safe_filename = safe_filename.strip()
        
        # 如果文件名为空，使用默认名称
        if not safe_filename:
            safe_filename = 'unnamed_sheet'
        
        return safe_filename
    
    def _export_dataframe(
        self, 
        df: pd.DataFrame, 
        output_path: Path, 
        file_format: str
    ) -> None:
        """
        导出DataFrame到指定格式文件
        
        Args:
            df: 要导出的DataFrame
            output_path: 输出文件路径
            file_format: 文件格式
            
        Raises:
            ValueError: 不支持的文件格式
        """
        if file_format.lower() == 'xlsx':
            df.to_excel(output_path, index=False, engine='openpyxl')
        elif file_format.lower() == 'csv':
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
        elif file_format.lower() == 'json':
            df.to_json(output_path, orient='records', force_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的文件格式: {file_format}")
    
    def get_sheet_info(self, excel_path: Union[str, Path], handle_merged_cells: bool = True) -> Dict[str, Dict]:
        """
        获取Excel文件中所有Sheet的基本信息
        
        Args:
            excel_path: Excel文件路径
            handle_merged_cells: 是否处理合并单元格，默认True
            
        Returns:
            Dict[str, Dict]: Sheet信息字典，包含行数、列数等
        """
        sheets_dict = self.read_excel_sheets(excel_path, handle_merged_cells)
        
        sheet_info = {}
        for sheet_name, df in sheets_dict.items():
            sheet_info[sheet_name] = {
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': list(df.columns),
                'has_data': not df.empty,
                'memory_usage': df.memory_usage(deep=True).sum()
            }
        
        return sheet_info
    
    def _process_merged_cells(self, excel_path: Path) -> str:
        """
        处理Excel文件中的合并单元格
        
        Args:
            excel_path: 原始Excel文件路径
            
        Returns:
            str: 处理后的文件路径（可能是临时文件）
        """
        try:
            # 检查是否有合并单元格
            if not self._has_merged_cells(excel_path):
                self.logger.info("未检测到合并单元格，无需处理")
                return str(excel_path)
            
            self.logger.info("检测到合并单元格，开始处理...")
            
            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            # 复制原文件到临时文件
            shutil.copy2(excel_path, temp_path)
            
            # 处理合并单元格
            self._unmerge_and_fill_cells(temp_path)
            
            self.logger.info(f"合并单元格处理完成，临时文件: {temp_path}")
            return temp_path
            
        except Exception as e:
            self.logger.error(f"处理合并单元格失败: {e}")
            # 如果处理失败，返回原文件路径
            return str(excel_path)
    
    def _has_merged_cells(self, excel_path: Path) -> bool:
        """
        检查Excel文件是否包含合并单元格
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            bool: 是否包含合并单元格
        """
        try:
            # 不使用read_only模式，以便访问merged_cells属性
            workbook = load_workbook(excel_path, read_only=False)
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                if worksheet.merged_cells.ranges:
                    workbook.close()
                    return True
            
            workbook.close()
            return False
            
        except Exception as e:
            self.logger.warning(f"检查合并单元格时出错: {e}")
            return False
    
    def _unmerge_and_fill_cells(self, excel_path: str) -> None:
        """
        拆分合并单元格并填充数据
        
        Args:
            excel_path: Excel文件路径
        """
        workbook = None
        try:
            workbook = load_workbook(excel_path)
            
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                # 获取所有合并单元格区域
                merged_ranges = list(worksheet.merged_cells.ranges)
                
                if not merged_ranges:
                    continue
                
                self.logger.info(f"Sheet '{sheet_name}' 发现 {len(merged_ranges)} 个合并单元格区域")
                
                # 处理每个合并单元格区域
                for merged_range in merged_ranges:
                    # 获取合并区域的边界
                    min_col, min_row, max_col, max_row = merged_range.bounds
                    
                    # 获取左上角单元格的值（合并单元格的值通常存储在这里）
                    top_left_cell = worksheet.cell(row=min_row, column=min_col)
                    merged_value = top_left_cell.value
                    
                    # 先取消合并
                    worksheet.unmerge_cells(str(merged_range))
                    
                    # 用原值填充所有单元格
                    for row in range(min_row, max_row + 1):
                        for col in range(min_col, max_col + 1):
                            cell = worksheet.cell(row=row, column=col)
                            cell.value = merged_value
                    
                    self.logger.debug(
                        f"已处理合并单元格 {merged_range}: "
                        f"范围({min_row},{min_col})-({max_row},{max_col}), "
                        f"值='{merged_value}'"
                    )
            
            # 保存处理后的文件
            workbook.save(excel_path)
            self.logger.info("所有合并单元格处理完成")
            
        except Exception as e:
            self.logger.error(f"拆分合并单元格失败: {e}")
            raise
        finally:
            # 确保关闭workbook
            if workbook:
                workbook.close()


# 便捷函数
def split_excel_by_sheets(
    excel_path: Union[str, Path],
    target_dir: Union[str, Path],
    file_format: str = 'xlsx',
    include_sheets: Optional[List[str]] = None,
    exclude_sheets: Optional[List[str]] = None,
    handle_merged_cells: bool = True
) -> Dict[str, str]:
    """
    便捷函数：将Excel文件按Sheet拆分为多个文件
    
    Args:
        excel_path: 源Excel文件路径
        target_dir: 目标目录路径
        file_format: 输出文件格式 ('xlsx', 'csv', 'json')
        include_sheets: 包含的Sheet名列表
        exclude_sheets: 排除的Sheet名列表
        handle_merged_cells: 是否处理合并单元格，默认True
        
    Returns:
        Dict[str, str]: Sheet名到输出文件路径的映射
    """
    util = ExcelTemplateUtil()
    return util.split_sheets_to_files(
        excel_path=excel_path,
        target_dir=target_dir,
        file_format=file_format,
        include_sheets=include_sheets,
        exclude_sheets=exclude_sheets,
        handle_merged_cells=handle_merged_cells
    )


def get_excel_info(excel_path: Union[str, Path], handle_merged_cells: bool = True) -> Dict[str, Dict]:
    """
    便捷函数：获取Excel文件信息
    
    Args:
        excel_path: Excel文件路径
        handle_merged_cells: 是否处理合并单元格，默认True
        
    Returns:
        Dict[str, Dict]: Sheet信息字典
    """
    util = ExcelTemplateUtil()
    return util.get_sheet_info(excel_path, handle_merged_cells)


def process_excel_file(
    source_excel: Union[str, Path],
    target_dir: Union[str, Path],
    output_format: str = 'xlsx',
    handle_merged_cells: bool = True
) -> Dict[str, Any]:
    """
    Excel文件处理入口方法
    
    Args:
        source_excel: 源Excel文件路径
        target_dir: 目标保存文件夹路径
        output_format: 文件保存后缀格式 ('xlsx', 'csv', 'json')
        handle_merged_cells: 是否处理合并单元格，默认True
        
    Returns:
        Dict[str, Any]: 处理结果信息
        {
            'success': bool,
            'source_file': str,
            'target_dir': str,
            'output_format': str,
            'processed_sheets': Dict[str, str],  # Sheet名到输出文件路径的映射
            'sheet_count': int,
            'total_rows': int,
            'error': str  # 如果有错误
        }
    """
    try:
        # 转换路径对象
        source_path = Path(source_excel)
        target_path = Path(target_dir)
        
        # 验证源文件存在
        if not source_path.exists():
            return {
                'success': False,
                'source_file': str(source_path),
                'target_dir': str(target_path),
                'output_format': output_format,
                'processed_sheets': {},
                'sheet_count': 0,
                'total_rows': 0,
                'error': f'源文件不存在: {source_path}'
            }
        
        # 创建目标目录
        target_path.mkdir(parents=True, exist_ok=True)
        
        # 获取Excel文件信息
        excel_info = get_excel_info(source_path, handle_merged_cells)
        
        # 按Sheet拆分文件
        processed_sheets = split_excel_by_sheets(
            excel_path=source_path,
            target_dir=target_path,
            file_format=output_format,
            handle_merged_cells=handle_merged_cells
        )
        
        # 统计总行数
        total_rows = sum(
            sheet_info.get('row_count', 0) 
            for sheet_info in excel_info.values()
        )
        
        return {
            'success': True,
            'source_file': str(source_path),
            'target_dir': str(target_path),
            'output_format': output_format,
            'processed_sheets': processed_sheets,
            'sheet_count': len(processed_sheets),
            'total_rows': total_rows,
            'error': None
        }
        
    except Exception as e:
        return {
            'success': False,
            'source_file': str(source_excel),
            'target_dir': str(target_dir),
            'output_format': output_format,
            'processed_sheets': {},
            'sheet_count': 0,
            'total_rows': 0,
            'error': f'处理Excel文件时发生错误: {str(e)}'
        }

if __name__ == '__main__':
    excel_path = r'C:\Users\<USER>\Desktop\账户明细1.xlsx'
    target_dir = r'C:\Users\<USER>\Desktop\拆分excel'
    result = process_excel_file(excel_path, target_dir)
    print(result)
