"""
Excel重复数据检测工具类
用于检测Excel文件中基于指定列组合的重复数据
"""

import os
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from pathlib import Path
import logging


class ExcelDuplicateDetector:
    """
    Excel重复数据检测工具类
    
    功能：
    - 读取Excel文件（支持多Sheet）
    - 基于指定列组合检测重复数据
    - 返回所有重复行的完整数据
    - 支持多种输出格式
    - 提供重复数据统计信息
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化Excel重复数据检测器
        
        Args:
            logger: 日志记录器，如果未提供则创建默认logger
        """
        self.logger = logger or self._create_default_logger()
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def find_duplicates(
        self,
        excel_path: Union[str, Path],
        columns: List[str],
        sheet_name: Optional[str] = None,
        case_sensitive: bool = True,
        keep_first: bool = True
    ) -> Dict[str, Any]:
        """
        查找Excel文件中基于指定列组合的重复数据
        
        Args:
            excel_path: Excel文件路径
            columns: 用于检测重复的列名列表
            sheet_name: 工作表名称，None表示使用第一个Sheet
            case_sensitive: 是否区分大小写，默认True
            keep_first: 是否保留首次出现的重复行，默认True
            
        Returns:
            Dict[str, Any]: 包含重复数据和统计信息的字典
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 参数错误
            KeyError: 指定的列不存在
        """
        excel_path = Path(excel_path)
        
        # 验证文件存在性
        if not excel_path.exists():
            raise FileNotFoundError(f"Excel文件不存在: {excel_path}")
        
        if excel_path.suffix.lower() not in ['.xlsx', '.xls']:
            raise ValueError(f"不支持的文件格式: {excel_path.suffix}")
        
        # 验证列名参数
        if not columns or not isinstance(columns, list):
            raise ValueError("columns参数必须是非空的列名列表")
        
        try:
            self.logger.info(f"开始检测重复数据: {excel_path}")
            self.logger.info(f"检测列: {columns}")
            
            # 读取Excel数据
            df = self._read_excel_data(excel_path, sheet_name)
            
            # 验证列是否存在
            missing_columns = [col for col in columns if col not in df.columns]
            if missing_columns:
                raise KeyError(f"以下列不存在于Excel文件中: {missing_columns}")
            
            # 预处理数据（处理大小写敏感性）
            df_processed = self._preprocess_data(df, columns, case_sensitive)
            
            # 检测重复数据
            duplicate_result = self._detect_duplicates(
                df_processed, columns, keep_first
            )
            
            # 构建返回结果
            result = {
                'duplicate_data': duplicate_result['duplicate_data'],
                'duplicate_count': duplicate_result['duplicate_count'],
                'duplicate_groups': duplicate_result['duplicate_groups'],
                'total_rows': len(df),
                'duplicate_rate': duplicate_result['duplicate_count'] / len(df) if len(df) > 0 else 0,
                'columns_used': columns,
                'sheet_name': duplicate_result['sheet_name'],
                'source_file': str(excel_path)
            }
            
            self.logger.info(f"重复检测完成: 发现{result['duplicate_count']}行重复数据")
            self.logger.info(f"重复率: {result['duplicate_rate']:.2%}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"检测重复数据失败: {e}")
            raise
    
    def _read_excel_data(
        self, 
        excel_path: Path, 
        sheet_name: Optional[str] = None
    ) -> pd.DataFrame:
        """读取Excel数据"""
        try:
            if sheet_name:
                df = pd.read_excel(excel_path, sheet_name=sheet_name, engine='openpyxl')
                self.logger.info(f"读取Sheet '{sheet_name}': {len(df)}行 x {len(df.columns)}列")
            else:
                # 读取第一个Sheet
                df = pd.read_excel(excel_path, engine='openpyxl')
                self.logger.info(f"读取默认Sheet: {len(df)}行 x {len(df.columns)}列")
            
            return df
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def _preprocess_data(
        self, 
        df: pd.DataFrame, 
        columns: List[str], 
        case_sensitive: bool
    ) -> pd.DataFrame:
        """预处理数据"""
        df_processed = df.copy()
        
        if not case_sensitive:
            # 对字符串列进行小写转换
            for col in columns:
                if df_processed[col].dtype == 'object':
                    df_processed[col] = df_processed[col].astype(str).str.lower()
                    self.logger.debug(f"列 '{col}' 已转换为小写")
        
        return df_processed
    
    def _detect_duplicates(
        self, 
        df: pd.DataFrame, 
        columns: List[str], 
        keep_first: bool
    ) -> Dict[str, Any]:
        """检测重复数据"""
        # 标记重复行（keep=False表示所有重复行都标记为True）
        duplicate_mask = df.duplicated(subset=columns, keep=False)
        
        # 获取所有重复行
        duplicate_data = df[duplicate_mask].copy()
        
        if len(duplicate_data) == 0:
            self.logger.info("未发现重复数据")
            return {
                'duplicate_data': pd.DataFrame(),
                'duplicate_count': 0,
                'duplicate_groups': 0,
                'sheet_name': 'Unknown'
            }
        
        # 计算重复组数
        duplicate_groups = df[duplicate_mask].groupby(columns).ngroups
        
        # 如果不保留首次出现的行，则只返回除首次外的重复行
        if not keep_first:
            duplicate_mask_no_first = df.duplicated(subset=columns, keep='first')
            duplicate_data = df[duplicate_mask_no_first].copy()
        
        # 为重复数据添加组标识
        if len(duplicate_data) > 0:
            # 创建组ID
            group_ids = df[duplicate_mask].groupby(columns).ngroup()
            duplicate_data = duplicate_data.copy()
            duplicate_data['重复组ID'] = group_ids[duplicate_mask].values
            duplicate_data['重复组ID'] = duplicate_data['重复组ID'] + 1  # 从1开始编号
        
        return {
            'duplicate_data': duplicate_data,
            'duplicate_count': len(duplicate_data),
            'duplicate_groups': duplicate_groups,
            'sheet_name': 'Sheet1'
        }
    
    def export_duplicates(
        self,
        duplicate_result: Dict[str, Any],
        output_path: Union[str, Path],
        format_type: str = 'xlsx',
        highlight_columns: bool = True
    ) -> str:
        """导出重复数据到文件"""
        output_path = Path(output_path)
        duplicate_data = duplicate_result['duplicate_data']
        
        if duplicate_data.empty:
            self.logger.warning("没有重复数据可导出")
            return str(output_path)
        
        try:
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format_type.lower() == 'xlsx':
                duplicate_data.to_excel(output_path, index=False, engine='openpyxl')
            elif format_type.lower() == 'csv':
                duplicate_data.to_csv(output_path, index=False, encoding='utf-8-sig')
            else:
                raise ValueError(f"不支持的输出格式: {format_type}")
            
            self.logger.info(f"重复数据已导出到: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"导出重复数据失败: {e}")
            raise


def find_excel_duplicates(
    excel_path: Union[str, Path],
    columns: List[str],
    sheet_name: Optional[str] = None,
    case_sensitive: bool = True,
    export_path: Optional[Union[str, Path]] = None,
    export_format: str = 'xlsx'
) -> Dict[str, Any]:
    """
    便捷函数：查找Excel文件中的重复数据
    
    Args:
        excel_path: Excel文件路径
        columns: 用于检测重复的列名列表
        sheet_name: 工作表名称，None表示使用第一个Sheet
        case_sensitive: 是否区分大小写，默认True
        export_path: 导出路径，None表示不导出
        export_format: 导出格式 ('xlsx', 'csv')
        
    Returns:
        Dict[str, Any]: 重复数据检测结果
    """
    detector = ExcelDuplicateDetector()
    
    # 检测重复数据
    result = detector.find_duplicates(
        excel_path=excel_path,
        columns=columns,
        sheet_name=sheet_name,
        case_sensitive=case_sensitive
    )
    
    # 如果指定了导出路径，则导出数据
    if export_path and not result['duplicate_data'].empty:
        export_file = detector.export_duplicates(
            duplicate_result=result,
            output_path=export_path,
            format_type=export_format
        )
        result['export_file'] = export_file
    
    return result


def get_duplicate_summary(duplicate_result: Dict[str, Any]) -> str:
    """
    生成重复数据检测摘要报告
    
    Args:
        duplicate_result: find_duplicates方法的返回结果
        
    Returns:
        str: 格式化的摘要报告
    """
    summary = f"""
=== Excel重复数据检测报告 ===
源文件: {duplicate_result['source_file']}
工作表: {duplicate_result['sheet_name']}
检测列: {', '.join(duplicate_result['columns_used'])}

数据统计:
- 总行数: {duplicate_result['total_rows']}
- 重复行数: {duplicate_result['duplicate_count']}
- 重复组数: {duplicate_result['duplicate_groups']}
- 重复率: {duplicate_result['duplicate_rate']:.2%}

检测结果: {'发现重复数据' if duplicate_result['duplicate_count'] > 0 else '未发现重复数据'}
"""
    return summary.strip()


def simple_excel_duplicate_filter(
    excel_path: Union[str, Path],
    output_path: Union[str, Path], 
    columns: List[str]
) -> Dict[str, Any]:
    """
    超级简化的Excel重复数据筛选函数
    只需传入三个参数即可完成重复数据筛选和导出
    
    Args:
        excel_path: Excel文件路径
        output_path: 导出重复数据的路径  
        columns: 用于检测重复的列名列表
        
    Returns:
        Dict[str, Any]: 筛选结果统计
        {
            'success': bool,           # 是否成功
            'duplicate_count': int,    # 重复行数
            'duplicate_groups': int,   # 重复组数  
            'duplicate_rate': float,   # 重复率
            'export_file': str,        # 导出文件路径
            'message': str             # 结果消息
        }
        
    Example:
        # 一行代码完成重复筛选
        result = simple_excel_duplicate_filter(
            "客户数据.xlsx", 
            "重复客户.xlsx", 
            ["姓名", "电话"]
        )
        print(result['message'])
    """
    try:
        # 检测重复数据
        duplicate_result = find_excel_duplicates(
            excel_path=excel_path,
            columns=columns,
            export_path=output_path,
            export_format='xlsx'
        )
        
        # 构建简化结果
        if duplicate_result['duplicate_count'] > 0:
            message = f"✅ 筛选完成！发现{duplicate_result['duplicate_count']}行重复数据，已导出到: {output_path}"
        else:
            message = "✅ 未发现重复数据"
            
        return {
            'success': True,
            'duplicate_count': duplicate_result['duplicate_count'],
            'duplicate_groups': duplicate_result['duplicate_groups'],
            'duplicate_rate': duplicate_result['duplicate_rate'],
            'export_file': str(output_path) if duplicate_result['duplicate_count'] > 0 else None,
            'message': message
        }
        
    except Exception as e:
        return {
            'success': False,
            'duplicate_count': 0,
            'duplicate_groups': 0,
            'duplicate_rate': 0.0,
            'export_file': None,
            'message': f"❌ 筛选失败: {str(e)}"
        }


if __name__ == '__main__':
    # 超级简化使用示例
    result = simple_excel_duplicate_filter(
        excel_path=r'C:\Users\<USER>\Desktop\拆分excel\结算中心-结算明细.xlsx',
        output_path=r'C:\Users\<USER>\Desktop\拆分excel\结算中心-结算明细重复行.xlsx',
        columns=["结算编号", "订单号","店铺","国家","报告类型","配送方式","来源","MSKU","交易类型","结算时间","币种","金额","数量","结算状态","转账状态","Settlement ID","SKU","品名","FNSKU"]
    )
    print(result['message']) 