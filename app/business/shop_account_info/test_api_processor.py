"""
店铺账户信息API处理器测试脚本

用于测试新的API版本处理器的基本功能：
1. 初始化测试
2. 配置验证测试
3. 批量任务验证测试
4. API客户端测试

使用方式：
    python test_api_processor.py
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.business.shop_account_info.shop_account_processor_api import AsyncShopAccountInfoAPIProcessor
from app.business.shop_account_info.shop_account_api_client import ShopAccountApiClient
from app.utils.logger import get_rpa_logger


async def test_processor_initialization():
    """测试处理器初始化"""
    print("🧪 测试1: 处理器初始化")
    
    try:
        processor = AsyncShopAccountInfoAPIProcessor(task_id="test_001")
        print("✅ 处理器初始化成功")
        
        # 测试基本属性
        assert processor.business_type == "shop_account_info"
        assert processor.script_name == "shop_account_processor_api"
        assert hasattr(processor, 'api_client')
        assert isinstance(processor.api_client, ShopAccountApiClient)
        
        print("✅ 基本属性验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 处理器初始化失败: {str(e)}")
        return False


async def test_config_methods():
    """测试配置方法"""
    print("\n🧪 测试2: 配置方法")
    
    try:
        processor = AsyncShopAccountInfoAPIProcessor(task_id="test_002")
        
        # 测试配置获取
        debug_mode = processor.get_bool_config('debugMode', False)
        max_batch_size = processor.get_int_config('maxBatchSize', 50)
        
        print(f"✅ 配置获取成功 - debugMode: {debug_mode}, maxBatchSize: {max_batch_size}")
        
        # 测试JSON配置
        batch_tasks = processor.get_json_config('detailConditionList', [])
        print(f"✅ JSON配置获取成功 - 批量任务数量: {len(batch_tasks)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置方法测试失败: {str(e)}")
        return False


async def test_batch_tasks():
    """测试批量任务获取"""
    print("\n🧪 测试3: 批量任务获取")
    
    try:
        processor = AsyncShopAccountInfoAPIProcessor(task_id="test_003")
        
        # 测试获取批量任务
        batch_tasks = processor._get_batch_tasks()
        print(f"✅ 批量任务获取成功 - 任务数量: {len(batch_tasks)}")
        
        if batch_tasks:
            # 验证任务结构
            first_task = batch_tasks[0]
            required_fields = ['systemSku', 'creationTime', 'sourceOrderNo', 'accountTradeDetailId']
            
            for field in required_fields:
                if field not in first_task:
                    print(f"⚠️ 任务缺少必需字段: {field}")
                else:
                    print(f"✅ 任务包含字段: {field} = {first_task[field]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量任务测试失败: {str(e)}")
        return False


async def test_api_client():
    """测试API客户端"""
    print("\n🧪 测试4: API客户端")
    
    try:
        # 使用项目的RPA日志记录器
        test_logger = get_rpa_logger("shop_account_info", "test_api_client")

        api_client = ShopAccountApiClient(
            logger=test_logger,
            business_type="shop_account_info",
            script_name="test"
        )
        
        print("✅ API客户端初始化成功")
        
        # 测试基本属性
        assert hasattr(api_client, 'user_info_network_extractor')
        assert hasattr(api_client, 'auth_info')
        assert api_client.config['base_url'] == 'https://dcmmaster.yibainetwork.com'
        
        print("✅ API客户端属性验证通过")
        
        # 测试认证状态
        is_authenticated = api_client.is_authenticated()
        print(f"✅ 认证状态检查: {is_authenticated}")
        
        return True
        
    except Exception as e:
        print(f"❌ API客户端测试失败: {str(e)}")
        return False


async def test_validation_methods():
    """测试验证方法"""
    print("\n🧪 测试5: 验证方法")
    
    try:
        processor = AsyncShopAccountInfoAPIProcessor(task_id="test_005")
        
        # 创建测试任务
        test_tasks = [
            {
                "systemSku": "*************",
                "creationTime": "2025-05-25 10:00:00",
                "sourceOrderNo": "FXPK250524509609",
                "accountTradeDetailId": "test_001"
            },
            {
                "systemSku": "*************",
                "creationTime": "invalid_time",  # 无效时间格式
                "sourceOrderNo": "FXPK250524509610",
                "accountTradeDetailId": "test_002"
            },
            {
                "systemSku": "",  # 缺少必需字段
                "creationTime": "2025-05-25 11:00:00",
                "sourceOrderNo": "FXPK250524509611"
                # 缺少accountTradeDetailId
            }
        ]
        
        # 测试任务验证
        valid_tasks = await processor._validate_batch_tasks(test_tasks)
        print(f"✅ 任务验证完成 - 有效任务: {len(valid_tasks)}, 总任务: {len(test_tasks)}")
        
        # 检查失败任务
        print(f"✅ 失败任务数量: {len(processor.failed_orders)}")
        print(f"✅ 失败任务详情数量: {len(processor.failed_orders_detail)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证方法测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始店铺账户信息API处理器测试")
    print("=" * 60)
    
    # 设置测试环境变量
    os.environ['debugMode'] = 'true'
    os.environ['batchProcessingEnabled'] = 'true'
    
    test_results = []
    
    # 执行测试
    test_results.append(await test_processor_initialization())
    test_results.append(await test_config_methods())
    test_results.append(await test_batch_tasks())
    test_results.append(await test_api_client())
    test_results.append(await test_validation_methods())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 60)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！API处理器基本功能正常")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
