# 店铺账户信息API增强功能 - 调试测试指南

## 概述

本目录包含了用于测试店铺账户信息API增强功能的完整测试套件。新增强的功能包括：

1. **正确解析订单详情API响应结构** - 按照 `data.datas.data_list.operate_log_list.operateLogList` 路径解析
2. **在操作日志content中搜索sourceOrderNo** - 精确匹配包裹号信息
3. **检测登录失效并自动重新认证** - 检测 `status=0, errorCode="E4002", http_status_code=401` 等条件
4. **增强的错误处理和日志记录** - 提供详细的调试信息和备用搜索机制

## 测试工具说明

### 🚀 一键测试启动器
```bash
python run_debug_tests.py
```

**功能**: 运行完整的测试套件，包括数据生成、基础测试、场景测试和报告生成

**选项**:
- `--all` - 运行所有测试（默认）
- `--basic` - 只运行基础功能测试
- `--full` - 只运行完整场景测试
- `--data` - 只生成测试数据

### 🧪 基础功能测试器
```bash
python debug_test_runner.py
```

**功能**: 测试核心增强功能的基本实现
- 订单详情解析逻辑验证
- 登录失效检测机制验证
- 自动重新认证机制验证
- 错误处理机制验证

### 🎭 完整场景测试器
```bash
python local_test_runner.py
```

**功能**: 模拟真实使用场景的端到端测试
- 场景1: 正常流程 - 找到匹配的sourceOrderNo
- 场景2: 登录失效和自动重新认证
- 场景3: 未找到匹配的sourceOrderNo
- 场景4: 异常响应结构处理

### 📋 测试数据生成器
```bash
python mock_api_data_generator.py
```

**功能**: 生成各种测试用的模拟API响应数据
- 订单列表API响应
- 订单详情API响应（包含操作日志）
- 登录失效响应
- 边界情况响应

## 快速开始

### 1. 运行完整测试套件
```bash
cd app/business/shop_account_info
python run_debug_tests.py --all
```

### 2. 只运行基础功能测试
```bash
python run_debug_tests.py --basic
```

### 3. 只运行场景测试
```bash
python run_debug_tests.py --full
```

### 4. 只生成测试数据
```bash
python run_debug_tests.py --data
```

## 测试结果文件

测试完成后，会在 `debug_data/` 目录下生成以下文件：

### 📊 测试结果报告
- `test_results.json` - 基础功能测试结果
- `local_test_results.json` - 场景测试结果
- `comprehensive_test_report.json` - 综合测试报告

### 📋 模拟测试数据
- `mock_responses/` - 模拟API响应数据目录
  - `order_list_normal.json` - 正常订单列表响应
  - `order_detail_with_match.json` - 包含匹配的订单详情
  - `order_detail_without_match.json` - 不包含匹配的订单详情
  - `auth_failure_e4002.json` - E4002登录失效响应
  - `auth_failure_401.json` - 401状态码响应
  - 其他边界情况响应...

### ⚙️ 配置文件
- `test_config.json` - 测试配置
- `database_test_data.sql` - 数据库测试数据

## 测试验证清单

运行测试后，以下功能将被验证：

### ✅ 订单详情解析增强
- [x] 正确解析 `data.datas.data_list.operate_log_list.operateLogList` 路径
- [x] 在每个操作日志项的 `content` 字段中搜索sourceOrderNo
- [x] 安全的字典访问，避免KeyError
- [x] 备用搜索机制处理异常结构

### ✅ 登录失效检测
- [x] 检测 `status: 0, errorCode: "E4002"` 条件
- [x] 检测 `http_status_code: 401` 条件
- [x] 检测错误信息中的关键词
- [x] 抛出明确的AUTH_FAILED异常

### ✅ 自动重新认证机制
- [x] 识别认证错误类型
- [x] 自动重新登录和token更新
- [x] 重试失败的API请求
- [x] 限制重试次数避免无限循环

### ✅ 增强的错误处理
- [x] 详细的日志记录和调试信息
- [x] 异常响应结构的容错处理
- [x] 备用搜索机制
- [x] 向后兼容性保持

## 测试数据说明

### 测试用的sourceOrderNo
- `FXPK250524509609` - 主要测试包裹号，在多个测试场景中使用
- `NOTFOUND123` - 用于测试未找到匹配的情况

### 测试用的accountTradeDetailId
- `debug_001` - 第一个测试记录
- `debug_002` - 第二个测试记录
- `debug_003` - 第三个测试记录（无匹配）

### 测试用的systemSku
- `*************` - 主要测试SKU
- `*************` - 第二个测试SKU
- `*************` - 第三个测试SKU

## 故障排除

### 常见问题

1. **ImportError: No module named 'app'**
   ```bash
   # 确保在项目根目录运行，或设置PYTHONPATH
   export PYTHONPATH=/path/to/project:$PYTHONPATH
   ```

2. **测试数据目录不存在**
   ```bash
   # 运行数据生成器
   python mock_api_data_generator.py
   ```

3. **权限错误**
   ```bash
   # 确保有写入权限
   chmod +w debug_data/
   ```

### 调试模式

设置环境变量启用详细日志：
```bash
export debugMode=true
export batchProcessingEnabled=true
python run_debug_tests.py
```

## 开发者说明

### 添加新测试用例

1. 在 `debug_test_runner.py` 中添加基础功能测试
2. 在 `local_test_runner.py` 中添加场景测试
3. 在 `mock_api_data_generator.py` 中添加相应的测试数据

### 修改测试配置

编辑 `test_config.json` 文件来调整测试参数：
```json
{
  "debugMode": true,
  "maxBatchSize": 5,
  "test_data": {
    "systemSku": "your_test_sku",
    "sourceOrderNo": "your_test_order_no"
  }
}
```

## 联系信息

如有问题或建议，请联系开发团队或查看项目文档。

---

**最后更新**: 2025-01-25
**版本**: 1.0.0
**状态**: ✅ 已完成并验证
