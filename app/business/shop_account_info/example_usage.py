"""
店铺账户信息API处理器使用示例

演示如何使用新的API版本处理器来替代RPA版本。
"""

import asyncio
import logging
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.business.shop_account_info.shop_account_processor_api import AsyncShopAccountInfoAPIProcessor


async def example_usage():
    """API处理器使用示例"""
    
    print("🚀 店铺账户信息API处理器使用示例")
    print("=" * 60)
    
    # 设置环境变量（实际使用时应该在环境中配置）
    os.environ['debugMode'] = 'true'  # 启用调试模式使用内置测试数据
    os.environ['batchProcessingEnabled'] = 'true'
    
    try:
        # 1. 创建处理器实例
        print("📝 步骤1: 创建API处理器实例")
        processor = AsyncShopAccountInfoAPIProcessor(task_id="example_001")
        print("✅ 处理器创建成功")
        
        # 2. 执行API处理
        print("\n🔄 步骤2: 执行API处理")
        print("注意：这将执行完整的API流程，包括登录、API调用等")
        
        # 在实际环境中，这里会：
        # - 使用RPA登录获取Token和用户信息
        # - 调用订单列表API
        # - 调用订单详情API
        # - 查找匹配的sourceOrderNo
        # - 保存到数据库
        
        result = await processor.execute()
        
        # 3. 处理结果
        print("\n📊 步骤3: 处理结果分析")
        execution_summary = result.get("execution_summary", {})
        
        print(f"总任务数: {execution_summary.get('total_tasks', 0)}")
        print(f"成功数: {execution_summary.get('success_count', 0)}")
        print(f"失败数: {execution_summary.get('failed_count', 0)}")
        print(f"执行时间: {execution_summary.get('execution_time_seconds', 0):.2f}秒")
        print(f"SKU组数: {execution_summary.get('sku_groups_count', 0)}")
        
        # 4. 成功订单详情
        success_orders = result.get("success_orders", [])
        if success_orders:
            print(f"\n✅ 成功处理的订单 ({len(success_orders)}个):")
            for order in success_orders:
                print(f"  - 订单ID: {order.get('order_id')}")
                print(f"    店铺账号: {order.get('shop_account')}")
                print(f"    SKU: {order.get('sku')}")
                print(f"    源订单号: {order.get('source_order_no')}")
        
        # 5. 失败订单详情
        failed_orders = result.get("failed_orders", [])
        if failed_orders:
            print(f"\n❌ 失败的订单 ({len(failed_orders)}个):")
            failed_details = result.get("failed_orders_detail", {})
            for failed_id in failed_orders:
                detail = failed_details.get(failed_id, {})
                print(f"  - ID: {failed_id}")
                print(f"    原因: {detail.get('error_reason', '未知')}")
                print(f"    上下文: {detail.get('context', '未知')}")
        
        print("\n🎯 API处理器执行完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 执行异常: {str(e)}")
        return False


async def compare_with_rpa():
    """与RPA版本的对比说明"""
    
    print("\n" + "=" * 60)
    print("📋 API版本 vs RPA版本对比")
    print("=" * 60)
    
    print("🔄 RPA版本特点:")
    print("  - 使用Playwright模拟浏览器操作")
    print("  - 需要页面元素定位和点击操作")
    print("  - 受页面变化影响较大")
    print("  - 执行速度相对较慢")
    print("  - 资源消耗较高")
    
    print("\n🚀 API版本优势:")
    print("  - 直接调用HTTP API接口")
    print("  - 不依赖页面元素，稳定性更高")
    print("  - 执行速度更快")
    print("  - 资源消耗更低")
    print("  - 更容易维护和调试")
    
    print("\n🔧 共同保留的功能:")
    print("  - 登录验证和Token获取")
    print("  - 用户信息提取")
    print("  - 批量任务处理")
    print("  - SKU分组优化")
    print("  - 数据库前置过滤")
    print("  - 详细的日志记录")
    print("  - 错误处理和重试机制")


def main():
    """主函数"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🎯 这是一个使用示例，演示API版本的基本用法")
    print("⚠️  注意：实际执行需要配置正确的环境变量和数据库连接")
    
    # 运行示例
    try:
        success = asyncio.run(example_usage())
        asyncio.run(compare_with_rpa())
        
        if success:
            print("\n✅ 示例执行成功！")
        else:
            print("\n❌ 示例执行失败，请检查配置")
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断执行")
    except Exception as e:
        print(f"\n💥 示例执行异常: {str(e)}")


if __name__ == "__main__":
    main()
