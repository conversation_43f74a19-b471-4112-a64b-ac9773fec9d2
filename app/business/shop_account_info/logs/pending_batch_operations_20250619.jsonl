{"timestamp": "2025-06-19T16:03:52.251118", "operation": "batch_update_failed_orders", "table": "account_trade_detail", "sql": "\n            UPDATE account_trade_detail \n            SET flow_status = 1 \n            WHERE id IN ('debug_001','debug_002') \n            AND flow_status != 2\n            ", "affected_ids": ["debug_001", "debug_002"], "target_flow_status": 1, "condition": "flow_status != 2", "status": "pending"}
{"timestamp": "2025-06-19T16:06:06.045799", "operation": "batch_update_failed_orders", "table": "account_trade_detail", "sql": "\n            UPDATE account_trade_detail \n            SET flow_status = 1 \n            WHERE id IN ('debug_002','debug_001') \n            AND flow_status != 2\n            ", "affected_ids": ["debug_002", "debug_001"], "target_flow_status": 1, "condition": "flow_status != 2", "status": "pending"}
{"timestamp": "2025-06-19T16:37:00.906823", "operation": "batch_update_failed_orders", "table": "account_trade_detail", "sql": "\n            UPDATE account_trade_detail \n            SET flow_status = 1 \n            WHERE (CAST(id AS CHAR) IN ('debug_001')) \n            AND flow_status != 2\n            ", "affected_ids": ["debug_001"], "target_flow_status": 1, "condition": "flow_status != 2", "status": "pending"}
{"timestamp": "2025-06-19T16:44:30.937875", "operation": "batch_update_failed_orders", "table": "account_trade_detail", "sql": "\n            UPDATE account_trade_detail \n            SET flow_status = 1 \n            WHERE (CAST(id AS CHAR) IN ('debug_001')) \n            AND flow_status != 2\n            ", "affected_ids": ["debug_001"], "target_flow_status": 1, "condition": "flow_status != 2", "status": "pending"}
