{"timestamp": "2025-06-19T16:36:35.929518", "operation": "update", "table": "account_trade_detail", "sql": "\n                UPDATE account_trade_detail \n                SET order_no = 'FXAM250524015033', shop = 'ICE CATCA(ICE CATCA)', flow_status = 2\n                WHERE id = debug_001\n                ", "data": {"account_trade_detail_id": "debug_001", "order_number": "FXAM250524015033", "shop_account": "ICE CATCA(ICE CATCA)", "flow_status": 2}, "status": "pending"}
{"timestamp": "2025-06-19T16:44:04.490720", "operation": "update", "table": "account_trade_detail", "sql": "\n                UPDATE account_trade_detail \n                SET order_no = 'FXAM250524015033', shop = 'ICE CATCA(ICE CATCA)', flow_status = 2\n                WHERE id = debug_001\n                ", "data": {"account_trade_detail_id": "debug_001", "order_number": "FXAM250524015033", "shop_account": "ICE CATCA(ICE CATCA)", "flow_status": 2}, "status": "pending"}
{"timestamp": "2025-06-19T17:14:28.586746", "operation": "update", "table": "account_trade_detail", "sql": "\n                UPDATE account_trade_detail \n                SET order_no = 'FXAM250524015033', shop = 'ICE CATCA(ICE CATCA)', flow_status = 2\n                WHERE id = batch_001\n                ", "data": {"account_trade_detail_id": "batch_001", "order_number": "FXAM250524015033", "shop_account": "ICE CATCA(ICE CATCA)", "flow_status": 2}, "status": "pending"}
