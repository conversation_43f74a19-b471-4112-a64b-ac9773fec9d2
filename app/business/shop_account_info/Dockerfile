# RPA-K8s Dockerfile - shop_account_info业务专用 (优化版)
# 支持shop_account_info业务脚本的超快速构建（预计30-60秒）

# 使用预构建的基础镜像（包含所有依赖）
FROM crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-k8s-base:latest

# 设置构建参数
ARG BUSINESS_TYPE=shop_account_info
ARG SCRIPT_NAME=shop_account_processor_async
ARG BUILD_DATE
ARG GIT_COMMIT

# 设置标签
LABEL maintainer="RPA-K8s Team"
LABEL description="RPA automation business - shop_account_info - speed optimized"
LABEL business_type="${BUSINESS_TYPE}"
LABEL script_name="${SCRIPT_NAME}"
LABEL build_date="${BUILD_DATE}"
LABEL git_commit="${GIT_COMMIT}"
LABEL version="3.0-speed-optimized"

# 设置业务环境变量
ENV BUSINESS_TYPE=${BUSINESS_TYPE} \
    SCRIPT_NAME=${SCRIPT_NAME} \
    RPA_EXECUTION_MODE=docker \
    PLAYWRIGHT_HEADLESS=true \
    PLAYWRIGHT_TIMEOUT=45 \
    PLAYWRIGHT_PAGE_LOAD_WAIT=15 \
    TZ=Asia/Shanghai

# 切换到root用户进行文件操作
USER root

# 工作目录
WORKDIR /app

# 🚀 核心优化：只复制必要的业务代码（最小化传输）
# 分层复制，优先复制不常变的文件
COPY app/core/ ./app/core/
COPY app/config/ ./app/config/
COPY app/utils/ ./app/utils/
COPY app/shared/ ./app/shared/

# 最后复制经常变化的业务代码
COPY app/business/shop_account_info/ ./app/business/shop_account_info/

# 确保浏览器权限正确（基础镜像中已包含浏览器缓存）
RUN chown -R rpauser:rpauser /home/<USER>

# 一次性完成所有文件操作（减少层数）
RUN mkdir -p /app/downloads /app/logs && \
    chown -R rpauser:rpauser /app/downloads /app/logs

# 复制启动脚本（避免复杂的转义问题）
COPY app/business/shop_account_info/start.sh /app/start.sh
RUN chmod +x /app/start.sh

# 健康检查（轻量级）
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; print('Health check passed'); sys.exit(0)" || exit 1

# 切换到非root用户
USER rpauser

# 默认入口点
ENTRYPOINT ["/app/start.sh"] 