#!/bin/bash
set -e
echo "=== RPA Container Starting (shop_account_info) ==="
echo "Timezone: $(date)"
echo "Python Path: $PYTHONPATH"
echo "Working Directory: $(pwd)"
echo "=================================================="
echo "Environment Variables:"
echo "BUSINESS_TYPE: $BUSINESS_TYPE"
echo "SCRIPT_NAME: $SCRIPT_NAME"
echo "TZ: $TZ"
echo "RPA_EXECUTION_MODE: $RPA_EXECUTION_MODE"
echo "=================================================="

# Verify browser installation
echo "Verifying browser installation..."
ls -la /home/<USER>/.cache/ms-playwright/ 2>/dev/null && echo "Browser cache directory found" || echo "Browser cache not found"

# Verify core dependencies
echo "Verifying core dependencies..."
python -c "import playwright; print('playwright loaded')"

# Create download directory
mkdir -p /app/downloads

# Start main business script
echo "Starting shop_account_info main script..."
echo "=================================================="
export PYTHONPATH=/app:$PYTHONPATH
cd /app && python app/business/shop_account_info/shop_account_processor_async.py 