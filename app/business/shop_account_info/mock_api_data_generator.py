"""
模拟API数据生成器

用于生成测试用的API响应数据，包括：
1. 订单列表API响应
2. 订单详情API响应（包含操作日志）
3. 登录失效响应
4. 各种边界情况的响应

使用方式：
    python mock_api_data_generator.py
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any


class MockApiDataGenerator:
    """模拟API数据生成器"""
    
    def __init__(self):
        self.output_dir = "debug_data/mock_responses"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def generate_all_mock_data(self):
        """生成所有模拟数据"""
        print("🔧 开始生成模拟API响应数据")
        
        # 生成订单列表响应
        self._generate_order_list_responses()
        
        # 生成订单详情响应
        self._generate_order_detail_responses()
        
        # 生成登录失效响应
        self._generate_auth_failure_responses()
        
        # 生成边界情况响应
        self._generate_edge_case_responses()
        
        print("✅ 所有模拟数据生成完成")
        
    def _generate_order_list_responses(self):
        """生成订单列表API响应"""
        print("📋 生成订单列表响应数据")
        
        # 正常的订单列表响应
        normal_response = {
            "code": 200,
            "message": "success",
            "data": {
                "page_data": {
                    "page": 1,
                    "pages": 2,
                    "total": 25,
                    "size": 20
                },
                "data_list": [
                    {
                        "value": {
                            "order_id": "FXAM250524015033",
                            "account": "ICE CATCA(ICE CATCA)",
                            "sku": "*************",
                            "create_time": "2025-05-25 10:00:00",
                            "status": "已发货"
                        }
                    },
                    {
                        "value": {
                            "order_id": "FXAM250524015034",
                            "account": "TEST SHOP(TEST SHOP)",
                            "sku": "*************",
                            "create_time": "2025-05-25 11:30:00",
                            "status": "处理中"
                        }
                    }
                ]
            }
        }
        
        # 空订单列表响应
        empty_response = {
            "code": 200,
            "message": "success",
            "data": {
                "page_data": {
                    "page": 1,
                    "pages": 0,
                    "total": 0,
                    "size": 20
                },
                "data_list": []
            }
        }
        
        self._save_json("order_list_normal.json", normal_response)
        self._save_json("order_list_empty.json", empty_response)
        
    def _generate_order_detail_responses(self):
        """生成订单详情API响应"""
        print("📄 生成订单详情响应数据")
        
        # 包含匹配sourceOrderNo的订单详情
        detail_with_match = {
            "code": 200,
            "message": "success",
            "data": {
                "datas": {
                    "data_list": {
                        "basic_info": {
                            "order_id": "FXAM250524015033",
                            "account": "ICE CATCA(ICE CATCA)",
                            "create_time": "2025-05-25 10:00:00"
                        },
                        "operate_log_list": {
                            "operateLogList": [
                                {
                                    "id": 1,
                                    "content": "订单创建成功",
                                    "create_time": "2025-05-25 10:00:00",
                                    "create_user": "system",
                                    "operator": "系统"
                                },
                                {
                                    "id": 2,
                                    "content": "包裹号分配：FXPK250524509609，准备发货",
                                    "create_time": "2025-05-25 10:05:00",
                                    "create_user": "warehouse",
                                    "operator": "仓库系统"
                                },
                                {
                                    "id": 3,
                                    "content": "订单处理完成，包裹号：FXPK250524509609，已发货",
                                    "create_time": "2025-05-25 10:10:00",
                                    "create_user": "logistics",
                                    "operator": "物流系统"
                                }
                            ]
                        },
                        "package_info": {
                            "package_list": [
                                {
                                    "package_no": "FXPK250524509609",
                                    "status": "已发货",
                                    "tracking_no": "YT1234567890"
                                }
                            ]
                        }
                    }
                }
            }
        }
        
        # 不包含匹配sourceOrderNo的订单详情
        detail_without_match = {
            "code": 200,
            "message": "success",
            "data": {
                "datas": {
                    "data_list": {
                        "basic_info": {
                            "order_id": "FXAM250524015034",
                            "account": "TEST SHOP(TEST SHOP)",
                            "create_time": "2025-05-25 11:30:00"
                        },
                        "operate_log_list": {
                            "operateLogList": [
                                {
                                    "id": 1,
                                    "content": "订单创建成功",
                                    "create_time": "2025-05-25 11:30:00",
                                    "create_user": "system",
                                    "operator": "系统"
                                },
                                {
                                    "id": 2,
                                    "content": "订单审核中，等待处理",
                                    "create_time": "2025-05-25 11:35:00",
                                    "create_user": "reviewer",
                                    "operator": "审核员"
                                }
                            ]
                        }
                    }
                }
            }
        }
        
        # 结构异常的订单详情
        detail_malformed = {
            "code": 200,
            "message": "success",
            "data": {
                "other_structure": {
                    "logs": "FXPK250524509609 在这里"
                }
            }
        }
        
        self._save_json("order_detail_with_match.json", detail_with_match)
        self._save_json("order_detail_without_match.json", detail_without_match)
        self._save_json("order_detail_malformed.json", detail_malformed)
        
    def _generate_auth_failure_responses(self):
        """生成登录失效响应"""
        print("🔐 生成登录失效响应数据")
        
        # E4002错误码
        auth_fail_e4002 = {
            "status": 0,
            "errorCode": "E4002",
            "errorMess": "登录状态失效，请重新登录",
            "path": "",
            "msg": "未知异常",
            "http_status_code": 401
        }
        
        # 401状态码
        auth_fail_401 = {
            "status": 0,
            "http_status_code": 401,
            "errorMess": "未授权访问",
            "msg": "请重新登录"
        }
        
        # 包含关键词的错误信息
        auth_fail_keywords = {
            "status": 0,
            "errorMess": "身份验证失败，登录状态失效",
            "msg": "请重新登录后重试"
        }
        
        self._save_json("auth_failure_e4002.json", auth_fail_e4002)
        self._save_json("auth_failure_401.json", auth_fail_401)
        self._save_json("auth_failure_keywords.json", auth_fail_keywords)
        
    def _generate_edge_case_responses(self):
        """生成边界情况响应"""
        print("⚠️ 生成边界情况响应数据")
        
        # 网络错误
        network_error = {
            "error": "network_timeout",
            "message": "请求超时"
        }
        
        # 服务器错误
        server_error = {
            "code": 500,
            "message": "服务器内部错误",
            "error": "internal_server_error"
        }
        
        # 空响应
        empty_response = {}
        
        # 非JSON响应
        html_response = "<html><body>Error 404</body></html>"
        
        self._save_json("network_error.json", network_error)
        self._save_json("server_error.json", server_error)
        self._save_json("empty_response.json", empty_response)
        
        # 保存HTML响应
        with open(f"{self.output_dir}/html_response.txt", "w", encoding="utf-8") as f:
            f.write(html_response)
            
    def _save_json(self, filename: str, data: Any):
        """保存JSON数据到文件"""
        filepath = f"{self.output_dir}/{filename}"
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"  ✅ 已生成: {filename}")
        
    def generate_test_config(self):
        """生成测试配置文件"""
        print("⚙️ 生成测试配置文件")
        
        test_config = {
            "debugMode": True,
            "batchProcessingEnabled": True,
            "maxBatchSize": 5,
            "timeWindowHours": 24,
            "test_data": {
                "systemSku": "*************",
                "sourceOrderNo": "FXPK250524509609",
                "creationTime": "2025-05-25 10:00:00"
            },
            "detailConditionList": [
                {
                    "systemSku": "*************",
                    "creationTime": "2025-05-25 10:00:00",
                    "sourceOrderNo": "FXPK250524509609",
                    "accountTradeDetailId": "debug_001"
                },
                {
                    "systemSku": "*************",
                    "creationTime": "2025-05-25 11:30:00",
                    "sourceOrderNo": "FXPK250524509609",
                    "accountTradeDetailId": "debug_002"
                },
                {
                    "systemSku": "*************",
                    "creationTime": "2025-05-25 12:00:00",
                    "sourceOrderNo": "NOTFOUND123",
                    "accountTradeDetailId": "debug_003"
                }
            ]
        }
        
        self._save_json("test_config.json", test_config)
        
    def generate_database_test_data(self):
        """生成数据库测试数据"""
        print("🗄️ 生成数据库测试数据")
        
        # 生成INSERT语句
        insert_statements = [
            """
            INSERT INTO account_trade_detail (id, system_sku, source_order_no, creation_time, flow_status, execution_log)
            VALUES 
            ('debug_001', '*************', 'FXPK250524509609', '2025-05-25 10:00:00', 0, '待处理'),
            ('debug_002', '*************', 'FXPK250524509609', '2025-05-25 11:30:00', 0, '待处理'),
            ('debug_003', '*************', 'NOTFOUND123', '2025-05-25 12:00:00', 0, '待处理');
            """,
            """
            -- 查询测试数据
            SELECT id, system_sku, source_order_no, order_no, shop, flow_status, execution_log
            FROM account_trade_detail 
            WHERE id IN ('debug_001', 'debug_002', 'debug_003');
            """,
            """
            -- 清理测试数据
            DELETE FROM account_trade_detail 
            WHERE id IN ('debug_001', 'debug_002', 'debug_003');
            """
        ]
        
        with open(f"{self.output_dir}/database_test_data.sql", "w", encoding="utf-8") as f:
            f.write("\n".join(insert_statements))
            
        print("  ✅ 已生成: database_test_data.sql")


def main():
    """主函数"""
    generator = MockApiDataGenerator()
    generator.generate_all_mock_data()
    generator.generate_test_config()
    generator.generate_database_test_data()
    
    print("\n🎉 所有测试数据生成完成！")
    print(f"📁 数据保存位置: {generator.output_dir}")
    print("\n📋 生成的文件列表:")
    
    # 列出生成的文件
    for root, dirs, files in os.walk(generator.output_dir):
        for file in files:
            print(f"  - {file}")


if __name__ == "__main__":
    main()
