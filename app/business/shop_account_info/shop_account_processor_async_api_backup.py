"""
店铺账户信息API处理器

基于API驱动的现代化实现，完全替代RPA操作。
实现5步API流程：
1. 登录RPA + 网络拦截 → 获取Token和用户信息
2. API请求订单列表 → 获取订单数据
3. 遍历订单列表 → 调用订单详情API
4. 解析订单详情 → 查找包裹号信息
5. 保存到数据库 → 完成数据存储

保留原有功能：
- 分组处理逻辑（SKU分组优化）
- 详细的日志记录和操作追踪
- 数据库前置过滤避免重复处理
- 批量任务管理和状态跟踪
- 错误处理和重试机制
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from app.core.base_rpa_async import AsyncBaseRPA
from app.core.database import DatabaseManager
from app.core.web_driver import BaseWebDriver
from app.business.shop_account_info.shop_account_api_client import ShopAccountApiClient
from app.shared.utils import ConcurrentOrderProcessor, BatchProcessor, OrderDetailCache, CacheManager


class AsyncShopAccountInfoAPIProcessor(AsyncBaseRPA):
    """
    店铺账户信息API处理器
    
    基于API的现代化实现，保留原有的分组和日志功能
    """
    
    def __init__(self, task_id: str = None):
        """
        初始化店铺账户信息API处理器
        
        Args:
            task_id: 任务ID，用于追踪和日志关联
        """
        # 先初始化重写方法需要的基础属性
        self._current_task_data = None
        self._batch_mode = False
        self._last_sku = None
        self._page_state = "unknown"
        self._valid_batch_tasks = []
        
        # 调用父类构造函数
        super().__init__(
            business_type="shop_account_info",
            script_name="shop_account_processor_async",
            task_id=task_id
        )
        
        # 成功和失败结果列表 - 类级别维护
        self.success_orders = []
        self.failed_orders = []
        self.failed_orders_detail = {}
        
        # 初始化API客户端
        self.api_client = ShopAccountApiClient(
            logger=self.logger,
            business_type=self.business_type,
            script_name=self.script_name
        )
        
        # 初始化数据库管理器（用于数据库过滤查询）
        try:
            self._db_manager_for_filtering = DatabaseManager(
                business_type=self.business_type,
                script_name=self.script_name,
                task_id=task_id
            )
            self.logger.info("数据库管理器初始化成功")
        except Exception as e:
            self.logger.warning(f"数据库管理器初始化失败: {e}，将使用备用方案")
            self._db_manager_for_filtering = None

        # 初始化并发处理器和缓存
        self._init_concurrent_and_cache()

        self.logger.info("✅ 店铺账户信息API处理器初始化完成")

    def _init_concurrent_and_cache(self):
        """初始化并发处理器和缓存组件"""
        try:
            # 从配置获取并发和缓存参数
            max_concurrent = self.get_int_config('max_concurrent', 2)
            request_interval = float(self.get_config('request_interval', 1.0))
            cache_duration = self.get_int_config('cache_duration_minutes', 30)
            max_cache_size = self.get_int_config('max_cache_size', 1000)
            batch_size = self.get_int_config('batch_size', 10)

            # 初始化订单详情缓存
            self.order_cache = OrderDetailCache(
                cache_duration_minutes=cache_duration,
                max_cache_size=max_cache_size,
                logger=self.logger
            )

            # 初始化缓存管理器
            self.cache_manager = CacheManager(
                cache=self.order_cache,
                auto_cleanup_interval=300  # 5分钟自动清理
            )

            # 初始化并发处理器
            self.concurrent_processor = ConcurrentOrderProcessor(
                max_concurrent=max_concurrent,
                request_interval=request_interval,
                logger=self.logger
            )

            # 初始化批处理器
            self.batch_processor = BatchProcessor(
                batch_size=batch_size,
                batch_interval=0.5,  # 批次间隔0.5秒
                logger=self.logger
            )

            self.logger.info("✅ 并发处理器和缓存组件初始化完成", extra_data={
                'max_concurrent': max_concurrent,
                'request_interval': request_interval,
                'cache_duration_minutes': cache_duration,
                'max_cache_size': max_cache_size,
                'batch_size': batch_size
            })

        except Exception as e:
            self.logger.error(f"❌ 并发处理器和缓存组件初始化失败: {str(e)}")
            # 使用默认配置作为备用
            self.order_cache = OrderDetailCache(logger=self.logger)
            self.cache_manager = CacheManager(self.order_cache)
            self.concurrent_processor = ConcurrentOrderProcessor(logger=self.logger)
            self.batch_processor = BatchProcessor(logger=self.logger)
    
    def get_config(self, key: str, default: Any = None, task_data: Dict[str, Any] = None) -> Any:
        """
        获取配置值，支持任务级覆盖，环境变量大小写不敏感
        
        优先级：任务数据 > 环境变量(大写) > 业务配置 > 默认值
        
        Args:
            key: 配置键名
            default: 默认值
            task_data: 任务数据（可选）
            
        Returns:
            Any: 配置值
        """
        # 使用当前任务数据或传入的任务数据
        current_task = task_data or self._current_task_data
        
        # 1. 优先从任务数据获取
        if current_task and isinstance(current_task, dict) and key in current_task:
            value = current_task[key]
            self.logger.debug(f"从任务数据获取配置 {key}: {value}")
            return value
        
        # 2. 从环境变量获取（大小写不敏感）
        import os
        env_value = os.getenv(key.upper()) or os.getenv(key.lower()) or os.getenv(key)
        if env_value is not None:
            self.logger.debug(f"从环境变量获取配置 {key}: {env_value}")
            return env_value
        
        # 3. 从业务配置获取
        try:
            from app.config.config import ConfigManager
            config_manager = ConfigManager(self.business_type, self.script_name)
            business_value = config_manager.get(key, default)
            if business_value != default:
                self.logger.debug(f"从业务配置获取配置 {key}: {business_value}")
                return business_value
        except Exception as e:
            self.logger.debug(f"获取业务配置异常: {e}")
        
        # 4. 返回默认值
        self.logger.debug(f"使用默认配置 {key}: {default}")
        return default
    
    def get_bool_config(self, key: str, default: bool = False, task_data: Dict[str, Any] = None) -> bool:
        """获取布尔类型配置"""
        value = self.get_config(key, default, task_data)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on', 'enabled')
        return bool(value)
    
    def get_json_config(self, key: str, default: Any = None, task_data: Dict[str, Any] = None) -> Any:
        """
        获取JSON/列表类型配置值，支持任务级覆盖，环境变量大小写不敏感
        支持Java toString()格式解析

        Args:
            key: 配置键名
            default: 默认值
            task_data: 任务数据（可选）

        Returns:
            Dict[str, Any] 或 List: JSON配置值
        """
        if default is None:
            default = []

        # 1. 当前任务数据（批量处理中）
        current_task = task_data or getattr(self, '_current_task_data', None)
        if current_task and isinstance(current_task, dict) and key in current_task:
            value = current_task[key]
            if isinstance(value, (dict, list)):
                return value
            elif isinstance(value, str):
                parsed_value = self._parse_config_value(value, default)
                return parsed_value
            else:
                return default

        # 2. 环境变量查找（大小写不敏感）
        import os
        upper_key = key.upper()
        if upper_key in os.environ:
            value = os.environ[upper_key]
            if isinstance(value, str):
                parsed_value = self._parse_config_value(value, default)
                self.logger.info(f"✅ 从环境变量 {upper_key} 获取配置，解析后数量: {len(parsed_value) if isinstance(parsed_value, list) else 'N/A'}")
                return parsed_value
            else:
                return default

        # 3. 如果大写版本不存在，尝试原始key
        if key in os.environ:
            value = os.environ[key]
            if isinstance(value, str):
                parsed_value = self._parse_config_value(value, default)
                self.logger.info(f"✅ 从环境变量 {key} 获取配置，解析后数量: {len(parsed_value) if isinstance(parsed_value, list) else 'N/A'}")
                return parsed_value
            else:
                return default

        # 4. 使用基类的JSON配置获取逻辑
        try:
            from app.config.config import ConfigManager
            config_manager = ConfigManager(self.business_type, self.script_name)
            business_value = config_manager.get_json(key, default)
            if business_value != default:
                self.logger.debug(f"从业务配置获取JSON配置 {key}")
                return business_value
        except Exception as e:
            self.logger.debug(f"获取业务JSON配置失败: {e}")

        return default
    
    def get_int_config(self, key: str, default: int = 0, task_data: Dict[str, Any] = None) -> int:
        """获取整数类型配置"""
        config_value = self.get_config(key, default, task_data)
        
        if isinstance(config_value, str):
            try:
                return int(config_value)
            except ValueError:
                self.logger.warning(f"配置项 {key} 不是有效的整数格式，使用默认值")
                return default
        
        return config_value if isinstance(config_value, int) else default

    def _parse_config_value(self, value: str, default) -> Any:
        """
        解析配置值，支持标准JSON和Java toString()格式

        Args:
            value: 要解析的字符串值
            default: 默认值

        Returns:
            解析后的配置值
        """
        try:
            # 尝试标准JSON解析
            import json
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            try:
                # 尝试Java toString()格式解析
                return self._parse_java_format(value)
            except Exception as e:
                self.logger.warning(f"⚠️ 配置值解析失败: {e}, 使用默认值")
                return default

    def _parse_java_format(self, java_str: str) -> list:
        """
        解析Java toString()格式的字符串为Python列表
        格式示例: [{key1=value1, key2=value2}, {key3=value3}]

        Args:
            java_str: Java toString()格式字符串

        Returns:
            List[Dict]: 解析后的列表
        """
        import re

        try:
            # 移除外层方括号
            content = java_str.strip()
            if content.startswith('[') and content.endswith(']'):
                content = content[1:-1]

            # 分割对象 - 处理嵌套的大括号
            objects = []
            brace_count = 0
            current_obj = ""

            for char in content:
                if char == '{':
                    brace_count += 1
                    current_obj += char
                elif char == '}':
                    brace_count -= 1
                    current_obj += char
                    if brace_count == 0:
                        objects.append(current_obj.strip())
                        current_obj = ""
                elif brace_count > 0:
                    current_obj += char
                # 忽略对象之间的逗号和空白

            result = []
            for obj_str in objects:
                if obj_str.startswith('{') and obj_str.endswith('}'):
                    # 解析单个对象
                    obj_content = obj_str[1:-1]  # 移除大括号
                    obj_dict = {}

                    # 分割键值对
                    pairs = re.split(r',\s*(?![^{]*})', obj_content)
                    for pair in pairs:
                        if '=' in pair:
                            key, value = pair.split('=', 1)
                            key = key.strip()
                            value = value.strip()

                            # 移除引号
                            if value.startswith('"') and value.endswith('"'):
                                value = value[1:-1]
                            elif value.startswith("'") and value.endswith("'"):
                                value = value[1:-1]

                            obj_dict[key] = value

                    if obj_dict:
                        result.append(obj_dict)

            return result

        except Exception as e:
            self.logger.error(f"❌ Java格式解析失败: {e}")
            raise

    def _get_batch_tasks(self) -> List[Dict[str, Any]]:
        """
        获取批量任务列表
        
        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        # Debug模式使用内置测试数据
        if self.get_bool_config('debugMode', False):
            self.logger.info("Debug模式：使用增强的测试数据")
            return [
                {
                    "systemSku": "*************",
                    "creationTime": "2025-07-25 12:04:31",
                    "sourceOrderNo": "FXPK250524509609",
                    "accountTradeDetailId": "test_task_001"
                },
                {
                    "systemSku": "*************",
                    "creationTime": "2025-07-25 10:04:31",
                    "sourceOrderNo": "FXPK250524509610",
                    "accountTradeDetailId": "test_task_002"
                },
                {
                    "systemSku": "*************",
                    "creationTime": "2025-07-25 08:04:31",
                    "sourceOrderNo": "FXPK250524509611",
                    "accountTradeDetailId": "test_task_003"
                },
                {
                    "systemSku": "*************",
                    "creationTime": "2025-07-25 06:04:31",
                    "sourceOrderNo": "NOTFOUND123456",
                    "accountTradeDetailId": "test_task_004"
                }
            ]
        
        # 从配置获取批量任务列表
        batch_tasks = self.get_json_config('detailConditionList', [])
        if not batch_tasks:
            return []
            
        # 移除数量限制，改为日志记录
        max_batch_size = self.get_int_config('maxBatchSize', 50)
        if len(batch_tasks) > max_batch_size:
            self.logger.info(f"批量任务数量({len(batch_tasks)})超过建议值({max_batch_size})，但继续处理所有任务")
            
        return batch_tasks
    
    async def _pre_execute_validation(self):
        """执行前置验证 - 仅支持批量模式"""
        await super()._pre_execute_validation()
        
        self.logger.info("开始执行店铺账户信息API处理器前置验证（仅批量模式）")
        
        # 获取批量任务
        batch_tasks = self._get_batch_tasks()
        if not batch_tasks:
            raise ValueError("未找到有效的批量任务配置")
        
        self.logger.info(f"获取到 {len(batch_tasks)} 个批量任务")
        
        # 验证批量任务
        self._valid_batch_tasks = await self._validate_batch_tasks(batch_tasks)
        
        # 过滤已处理的任务
        self._valid_batch_tasks = await self._filter_already_processed_tasks(self._valid_batch_tasks)
        
        if not self._valid_batch_tasks:
            raise ValueError("所有任务都已处理完成或验证失败")
        
        self.logger.info(f"前置验证完成，有效任务数量: {len(self._valid_batch_tasks)}")
    
    async def _validate_batch_tasks(self, batch_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """验证批量任务配置，将无效任务添加到failed_orders，返回有效任务"""
        if not self.get_bool_config('batchProcessingEnabled', True):
            error_msg = "批量处理功能未启用"
            self.logger.error(f"{error_msg}，所有批量任务添加到失败列表")
            
            # 批量处理功能未启用：将所有批量任务添加到失败列表
            for i, task in enumerate(batch_tasks):
                self._add_to_failed_orders(task, context="前置验证", reason="批量处理功能未启用")
            
            raise ValueError(error_msg)
        
        valid_tasks = []
        required_fields = ['systemSku', 'creationTime', 'sourceOrderNo', 'accountTradeDetailId']
        
        for i, task in enumerate(batch_tasks):
            try:
                # 验证必需字段
                missing_fields = [field for field in required_fields if not task.get(field)]
                if missing_fields:
                    error_reason = f"缺少必需字段: {', '.join(missing_fields)}"
                    self.logger.warning(f"任务 {i+1} 验证失败: {error_reason}")
                    self._add_to_failed_orders(task, context="前置验证", reason=error_reason)
                    continue
                
                # 验证时间格式（增强版 - 支持多种格式自动转换）
                try:
                    normalized_time = self._normalize_time_format(task['creationTime'])
                    if normalized_time:
                        # 更新任务中的时间为标准格式
                        task['creationTime'] = normalized_time
                        self.logger.debug(f"任务 {i+1} 时间格式已标准化: {normalized_time}")
                    else:
                        raise ValueError(f"无法标准化时间格式: {task['creationTime']}")
                except Exception as e:
                    error_reason = f"creationTime格式错误: {task['creationTime']}"
                    self.logger.warning(f"任务 {i+1} 验证失败: {error_reason}")
                    self._add_to_failed_orders(task, context="前置验证", reason=error_reason)
                    continue
                
                valid_tasks.append(task)
                self.logger.debug(f"任务 {i+1} 验证通过")
                
            except Exception as e:
                error_reason = f"验证异常: {str(e)}"
                self.logger.error(f"任务 {i+1} 验证异常: {error_reason}")
                self._add_to_failed_orders(task, context="前置验证", reason=error_reason)
        
        self.logger.info(f"批量任务验证完成，有效任务: {len(valid_tasks)}, 无效任务: {len(batch_tasks) - len(valid_tasks)}")
        return valid_tasks
    
    def _add_to_failed_orders(self, task_data: Dict[str, Any], context: str, reason: str):
        """添加失败任务到失败列表"""
        from datetime import datetime

        account_trade_detail_id = task_data.get('accountTradeDetailId', 'unknown')

        # 添加到简单失败列表（向后兼容）
        if account_trade_detail_id not in self.failed_orders:
            self.failed_orders.append(account_trade_detail_id)

        # 添加到详细失败信息
        self.failed_orders_detail[account_trade_detail_id] = {
            "execution_log": f"[{context}] {reason}",
            "error_reason": reason,
            "context": context,
            "task_data": task_data,
            "timestamp": datetime.now().isoformat()
        }

        self.logger.debug(f"任务添加到失败列表: {account_trade_detail_id} - {reason}")
    
    async def _filter_already_processed_tasks(self, valid_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤已经成功处理的任务（flow_status=2），避免重复执行
        
        Args:
            valid_tasks: 通过基本验证的任务列表
            
        Returns:
            List[Dict[str, Any]]: 过滤后需要处理的任务列表
        """
        try:
            if not self._db_manager_for_filtering:
                self.logger.warning("数据库管理器不可用，跳过已处理任务过滤")
                return valid_tasks
            
            # 提取所有accountTradeDetailId
            account_ids = [task.get('accountTradeDetailId') for task in valid_tasks if task.get('accountTradeDetailId')]
            
            if not account_ids:
                self.logger.warning("没有有效的accountTradeDetailId，跳过数据库过滤")
                return valid_tasks
            
            # 查询已处理的ID
            processed_ids = await self._query_already_processed_ids(account_ids)
            
            # 过滤任务
            filtered_tasks = []
            for task in valid_tasks:
                account_id = task.get('accountTradeDetailId')
                if account_id in processed_ids:
                    self.logger.info(f"任务 {account_id} 已处理完成，跳过")
                    self._add_to_failed_orders(task, context="数据库过滤", reason="任务已处理完成(flow_status=2)")
                else:
                    filtered_tasks.append(task)
            
            self.logger.info(f"数据库过滤完成，需处理任务: {len(filtered_tasks)}, 已处理任务: {len(processed_ids)}")
            return filtered_tasks
            
        except Exception as e:
            self.logger.error(f"过滤已处理任务异常: {e}")
            return valid_tasks
    
    async def _query_already_processed_ids(self, account_ids: List[str]) -> set:
        """
        批量查询数据库，获取已成功处理的accountTradeDetailId列表（flow_status=2）
        
        Args:
            account_ids: 要查询的accountTradeDetailId列表
            
        Returns:
            set: 已成功处理的accountTradeDetailId集合
        """
        try:
            if not account_ids:
                return set()
            
            # 构建IN查询
            ids_str = ','.join(f"'{id_}'" for id_ in account_ids)
            query_sql = f"""
                SELECT id 
                FROM account_trade_detail 
                WHERE id IN ({ids_str}) 
                AND flow_status = 2
            """
            
            self.logger.debug(f"查询已处理任务SQL: {query_sql}")
            
            # 执行查询
            import asyncio
            results = await asyncio.to_thread(
                self._db_manager_for_filtering.execute_query,
                query_sql
            )
            
            processed_ids = {str(row[0]) for row in results} if results else set()
            
            self.logger.info(f"查询到 {len(processed_ids)} 个已处理的任务")
            return processed_ids
            
        except Exception as e:
            self.logger.error(f"查询已处理任务异常: {e}")
            return set()  # 返回空集合，所有任务都会被处理

    async def _execute_batch_mode(self) -> Dict[str, Any]:
        """执行批量模式（API版本）"""
        # 重置订单结果列表
        self._reset_order_lists()

        # 使用验证后的有效任务列表
        valid_tasks = getattr(self, '_valid_batch_tasks', self._get_batch_tasks())
        total_tasks = len(valid_tasks)
        self.logger.info(f"开始执行API批量模式，有效任务数量: {total_tasks}")

        # 按SKU分组任务
        grouped_tasks = self._group_tasks_by_sku(valid_tasks)
        self.logger.info(f"任务按SKU分组完成，共 {len(grouped_tasks)} 个SKU组")

        start_time = datetime.now()

        try:
            # 使用Web驱动进行登录和认证
            async with self.web_driver_context() as driver:
                # 步骤1: 登录验证和获取认证信息
                self.logger.info("🔐 步骤1: 登录验证和获取认证信息")
                tokens, user_info = await self.api_client.authenticate_and_get_user_info(driver)

                if not tokens or not user_info:
                    raise Exception("登录验证或认证信息获取失败")

                self.logger.info("✅ 认证信息获取成功", extra_data={
                    'user_id': user_info.get('uid'),
                    'distributor_id': user_info.get('distributor_id'),
                    'has_jwt_token': bool(tokens.get('jwt_token')),
                    'jwt_token_length': len(tokens.get('jwt_token', '')),
                    'jwt_token_preview': tokens.get('jwt_token', '')[:50] + '...' if tokens.get('jwt_token') else 'None',
                    'has_token1_check': bool(tokens.get('token1_check')),
                    'has_device_number': bool(tokens.get('device_number')),
                    'has_anticlimb_code': bool(tokens.get('anticlimb_verify_code')),
                    'token_keys': list(tokens.keys()) if tokens else []
                })

                # 设置driver引用，用于自动重新认证
                self.api_client.set_driver(driver)

                # 验证Token完整性
                await self._validate_tokens(tokens, driver)

                # 步骤2-5: 处理每个SKU组
                for sku, sku_tasks in grouped_tasks.items():
                    self.logger.info(f"🔄 开始处理SKU组: {sku}，任务数量: {len(sku_tasks)}")

                    try:
                        await self._process_sku_group_api(sku, sku_tasks, tokens, user_info)
                        self.logger.info(f"✅ SKU组 {sku} 处理完成")

                    except Exception as e:
                        self.logger.error(f"❌ SKU组 {sku} 处理异常: {str(e)}")
                        # 将该SKU组的所有任务标记为失败
                        for task in sku_tasks:
                            self._add_to_failed_orders(task, context=f"SKU组处理", reason=f"SKU组处理异常: {str(e)}")

        except Exception as e:
            self.logger.error(f"❌ 批量模式执行异常: {str(e)}")
            # 将所有剩余任务标记为失败
            for task in valid_tasks:
                account_id = task.get('accountTradeDetailId')
                if (account_id not in [order.get('accountTradeDetailId') for order in self.success_orders] and
                    account_id not in self.failed_orders):
                    self._add_to_failed_orders(task, context="批量执行", reason=f"批量执行异常: {str(e)}")

        # 计算执行统计
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # 🔧 批量处理失败订单 - 更新数据库状态
        if self.failed_orders:
            self.logger.info("🔄 开始批量处理失败订单，更新数据库状态")
            try:
                batch_result = await self._batch_process_failed_orders()
                if batch_result.get("success", False):
                    self.logger.info(f"✅ 失败订单批量处理完成，更新了 {batch_result.get('affected_rows', 0)} 条记录")
                else:
                    self.logger.warning(f"⚠️ 失败订单批量处理未完全成功: {batch_result.get('error', 'Unknown error')}")
            except Exception as batch_error:
                self.logger.error(f"❌ 失败订单批量处理异常: {batch_error}")

        # 构建最终结果
        final_results = {
            "execution_summary": {
                "total_tasks": total_tasks,
                "success_count": len(self.success_orders),
                "failed_count": len(self.failed_orders),
                "execution_time_seconds": execution_time,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "sku_groups_count": len(grouped_tasks)
            },
            "success_orders": self.success_orders,
            "failed_orders": self.failed_orders,
            "failed_orders_detail": self.failed_orders_detail
        }

        self.logger.info(f"🎯 API批量模式执行完成", extra_data={
            "总任务数": total_tasks,
            "成功数": len(self.success_orders),
            "失败数": len(self.failed_orders),
            "执行时间": f"{execution_time:.2f}秒",
            "SKU组数": len(grouped_tasks)
        })

        return final_results

    def _reset_order_lists(self):
        """重置成功和失败订单列表"""
        self.success_orders.clear()
        self.failed_orders.clear()
        self.failed_orders_detail.clear()
        self.logger.info("订单结果列表已重置")

    def _group_tasks_by_sku(self, batch_tasks: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按SKU分组任务，优化批量处理"""
        grouped = {}
        for task in batch_tasks:
            sku = task.get('systemSku', 'unknown')
            if sku not in grouped:
                grouped[sku] = []
            grouped[sku].append(task)
        return grouped

    async def _validate_tokens(self, tokens: Dict[str, str], driver) -> None:
        """
        验证Token的完整性和有效性

        Args:
            tokens: Token字典
            driver: Web驱动实例
        """
        try:
            self.logger.info("🔍 开始验证Token完整性")

            # 检查必需的Token
            required_tokens = ['jwt_token', 'device_number', 'anticlimb_verify_code']
            missing_tokens = []

            for token_name in required_tokens:
                if not tokens.get(token_name):
                    missing_tokens.append(token_name)

            if missing_tokens:
                self.logger.warning(f"⚠️ 缺少必需的Token: {missing_tokens}")

                # 尝试重新提取缺失的Token
                self.logger.info("🔄 尝试重新提取缺失的Token")
                page = driver.page

                # 等待页面稳定
                await page.wait_for_load_state("networkidle")
                await asyncio.sleep(2)

                # 重新提取所有Token
                from app.shared.clients.token_extractor import UniversalTokenExtractor
                token_extractor = UniversalTokenExtractor(self.logger)
                new_tokens = await token_extractor.extract_all_tokens(page)

                # 更新缺失的Token
                for token_name in missing_tokens:
                    if new_tokens.get(token_name):
                        tokens[token_name] = new_tokens[token_name]
                        self.logger.info(f"✅ 成功补充Token: {token_name}")
                    else:
                        self.logger.warning(f"❌ 仍然无法获取Token: {token_name}")

            # 验证JWT Token格式
            jwt_token = tokens.get('jwt_token', '')
            if jwt_token:
                if not jwt_token.startswith('eyJ'):
                    self.logger.warning(f"⚠️ JWT Token格式可能不正确: {jwt_token[:50]}...")
                else:
                    self.logger.info(f"✅ JWT Token格式验证通过: {jwt_token[:50]}...")

            self.logger.info("✅ Token验证完成", extra_data={
                'final_token_keys': list(tokens.keys()),
                'jwt_token_length': len(tokens.get('jwt_token', '')),
                'has_all_required': all(tokens.get(t) for t in required_tokens)
            })

        except Exception as e:
            self.logger.error(f"❌ Token验证异常: {str(e)}")
            # 不抛出异常，继续执行

    async def _process_sku_group_api(self, sku: str, sku_tasks: List[Dict[str, Any]],
                                    tokens: Dict[str, str], user_info: Dict[str, Any]):
        """
        处理单个SKU组的所有任务（API版本）

        Args:
            sku: 系统SKU
            sku_tasks: 该SKU的所有任务
            tokens: 认证Token字典
            user_info: 用户信息字典
        """
        try:
            self.logger.info(f"📦 开始处理SKU组: {sku}，任务数量: {len(sku_tasks)}")

            # 为每个任务处理订单查询
            for task_index, task in enumerate(sku_tasks):
                try:
                    self.logger.info(f"🔍 处理任务 {task_index + 1}/{len(sku_tasks)}: {task.get('accountTradeDetailId')}")

                    # 设置当前任务数据
                    self._current_task_data = task

                    # 处理单个任务
                    await self._process_single_task_api(task, tokens, user_info)

                except Exception as e:
                    self.logger.error(f"❌ 任务处理异常: {str(e)}", extra_data={
                        'task_index': task_index + 1,
                        'account_trade_detail_id': task.get('accountTradeDetailId'),
                        'sku': sku
                    })
                    self._add_to_failed_orders(task, context="任务处理", reason=f"任务处理异常: {str(e)}")

                finally:
                    # 清理当前任务数据
                    self._current_task_data = None

            self.logger.info(f"✅ SKU组 {sku} 处理完成")

        except Exception as e:
            self.logger.error(f"❌ SKU组处理异常: {str(e)}")
            raise

    async def _process_single_task_api(self, task: Dict[str, Any],
                                      tokens: Dict[str, str], user_info: Dict[str, Any]):
        """
        处理单个任务（API版本）

        Args:
            task: 任务数据
            tokens: 认证Token字典
            user_info: 用户信息字典
        """
        try:
            sku = task.get('systemSku')
            creation_time = task.get('creationTime')
            source_order_no = task.get('sourceOrderNo')
            account_trade_detail_id = task.get('accountTradeDetailId')

            self.logger.info(f"🔍 开始处理任务: {account_trade_detail_id}")

            # 步骤2: 获取订单列表
            self.logger.info(f"📋 步骤2: 获取订单列表 - SKU: {sku}")
            page = 1
            found_matching_order = False

            while not found_matching_order:
                # 调用订单列表API
                order_list_response = await self.api_client.get_order_list(
                    sku=sku,
                    creation_time=creation_time,
                    tokens=tokens,
                    user_info=user_info,
                    page=page
                )

                # 检查响应
                if order_list_response.get('code') != 200:
                    raise Exception(f"订单列表API调用失败: {order_list_response.get('message', '未知错误')}")

                # 解析订单数据
                data = order_list_response.get('data', {})
                page_data = data.get('page_data', {})
                order_list = data.get('data_list', [])

                total_orders = page_data.get('total', 0)
                current_page = page_data.get('page', 1)
                total_pages = page_data.get('pages', 1)

                self.logger.info(f"📄 获取到订单列表 - 页码: {current_page}/{total_pages}, 当前页订单数: {len(order_list)}, 总订单数: {total_orders}")

                if not order_list:
                    self.logger.warning(f"⚠️ 第 {page} 页没有订单数据")
                    break

                # 步骤3: 使用并发处理遍历订单列表，查找匹配的订单
                if self.get_bool_config('enable_concurrent_processing', True):
                    # 并发处理模式
                    found_matching_order = await self._process_orders_concurrent(
                        order_list, tokens, user_info, source_order_no,
                        sku, account_trade_detail_id
                    )
                else:
                    # 串行处理模式（原有逻辑）
                    found_matching_order = await self._process_orders_sequential(
                        order_list, tokens, user_info, source_order_no,
                        sku, account_trade_detail_id
                    )

                # 检查是否还有下一页
                if found_matching_order or page >= total_pages:
                    break

                page += 1
                self.logger.info(f"🔄 继续查询下一页: {page}")

            # 如果没有找到匹配的订单
            if not found_matching_order:
                error_msg = f"未找到包含sourceOrderNo '{source_order_no}' 的订单"
                self.logger.warning(f"⚠️ {error_msg}")
                self._add_to_failed_orders(task, context="订单查找", reason=error_msg)

        except Exception as e:
            self.logger.error(f"❌ 单个任务处理异常: {str(e)}")
            self._add_to_failed_orders(task, context="任务处理", reason=f"处理异常: {str(e)}")
            raise

    async def _check_source_order_no_in_detail(self, order_detail_response: Dict[str, Any],
                                              source_order_no: str) -> bool:
        """
        在订单详情的操作日志中查找sourceOrderNo

        根据实际API响应结构解析：data.datas.data_list.operate_log_list.operateLogList
        在每个操作日志项的content字段中搜索sourceOrderNo

        Args:
            order_detail_response: 订单详情API响应
            source_order_no: 要查找的源订单号

        Returns:
            bool: 是否找到匹配的sourceOrderNo
        """
        try:
            self.logger.debug(f"🔍 开始在订单详情中查找sourceOrderNo: {source_order_no}")

            # 检查响应基本结构
            if not isinstance(order_detail_response, dict):
                self.logger.warning("⚠️ 订单详情响应不是字典格式")
                return False

            # 按照实际API响应结构解析：data.datas.data_list.operate_log_list.operateLogList
            data = order_detail_response.get('data')
            if not isinstance(data, dict):
                self.logger.debug("❌ 响应中没有data字段或data不是字典")
                return self._fallback_search_in_response(order_detail_response, source_order_no)

            datas = data.get('datas')
            if not isinstance(datas, dict):
                self.logger.debug("❌ data中没有datas字段或datas不是字典")
                return self._fallback_search_in_response(order_detail_response, source_order_no)

            data_list = datas.get('data_list')
            if not isinstance(data_list, dict):
                self.logger.debug("❌ datas中没有data_list字段或data_list不是字典")
                return self._fallback_search_in_response(order_detail_response, source_order_no)

            operate_log_list = data_list.get('operate_log_list')
            if not isinstance(operate_log_list, dict):
                self.logger.debug("❌ data_list中没有operate_log_list字段或operate_log_list不是字典")
                return self._fallback_search_in_response(order_detail_response, source_order_no)

            operate_log_list_data = operate_log_list.get('operateLogList')
            if not isinstance(operate_log_list_data, list):
                self.logger.debug("❌ operate_log_list中没有operateLogList字段或operateLogList不是列表")
                return self._fallback_search_in_response(order_detail_response, source_order_no)

            self.logger.debug(f"📋 找到操作日志列表，共 {len(operate_log_list_data)} 条记录")

            # 在操作日志列表中搜索
            for i, log_item in enumerate(operate_log_list_data):
                if not isinstance(log_item, dict):
                    self.logger.debug(f"⚠️ 第{i+1}条日志不是字典格式，跳过")
                    continue

                # 获取content字段
                content = log_item.get('content', '')
                if not content:
                    self.logger.debug(f"第{i+1}条日志没有content字段或content为空")
                    continue

                # 在content中搜索sourceOrderNo
                content_str = str(content)
                if source_order_no in content_str:
                    self.logger.info(f"✅ 在第{i+1}条操作日志的content中找到sourceOrderNo: {source_order_no}")
                    self.logger.debug(f"匹配的日志内容: {content_str[:200]}...")  # 只显示前200字符
                    return True

                self.logger.debug(f"第{i+1}条日志content不包含目标值: {content_str[:100]}...")

            self.logger.info(f"❌ 在所有操作日志中未找到sourceOrderNo: {source_order_no}")
            return False

        except Exception as e:
            self.logger.error(f"检查sourceOrderNo异常: {str(e)}")
            # 异常情况下尝试备用搜索方法
            return self._fallback_search_in_response(order_detail_response, source_order_no)

    def _fallback_search_in_response(self, order_detail_response: Dict[str, Any], source_order_no: str) -> bool:
        """
        备用搜索方法：在整个响应中搜索sourceOrderNo

        Args:
            order_detail_response: 订单详情API响应
            source_order_no: 要查找的源订单号

        Returns:
            bool: 是否找到匹配的sourceOrderNo
        """
        try:
            self.logger.debug(f"🔄 使用备用搜索方法在整个响应中查找sourceOrderNo: {source_order_no}")

            # 将整个响应转换为JSON字符串进行搜索
            response_text = json.dumps(order_detail_response, ensure_ascii=False)
            found = source_order_no in response_text

            if found:
                self.logger.info(f"✅ 在整个响应中找到sourceOrderNo: {source_order_no}")
            else:
                self.logger.info(f"❌ 在整个响应中未找到sourceOrderNo: {source_order_no}")

            return found

        except Exception as e:
            self.logger.error(f"备用搜索方法异常: {str(e)}")
            return False

    async def _save_to_database(self, order_number: str, shop_account: str,
                               account_trade_detail_id: str) -> Dict[str, Any]:
        """
        保存订单号、店铺账号和accountTradeDetailId到数据库

        更新account_trade_detail表，根据ID匹配accountTradeDetailId，
        设置order_no、shop和flow_status字段

        Args:
            order_number: 订单号
            shop_account: 店铺账号
            account_trade_detail_id: 账户交易详情ID（对应表中的id字段）

        Returns:
            Dict[str, Any]: 数据库操作结果
        """
        try:
            self.logger.info("💾 开始数据库操作")
            self.logger.info(f"   订单号: {order_number}")
            self.logger.info(f"   店铺账号: {shop_account}")
            self.logger.info(f"   accountTradeDetailId: {account_trade_detail_id}")

            # 记录成功的execution_log
            success_message = f"成功获取店铺账号: {shop_account}"
            execution_log = await self._record_execution_log(
                log_type="SUCCESS",
                message=success_message,
                context="数据保存",
                account_trade_detail_id=account_trade_detail_id,
                is_final_success=True
            )

            # 生成log_categories
            log_categories = self._manage_log_categories(log_type="SUCCESS", is_success=True)

            # 安全处理SQL字符串，转义单引号
            safe_shop_account = shop_account.replace("'", "''")  # SQL标准转义
            safe_execution_log = execution_log.replace("'", "''")  # SQL标准转义
            safe_log_categories = log_categories.replace("'", "''")  # SQL标准转义

            set_clauses = [
                f"shop = '{safe_shop_account}'",
                "flow_status = 2",
                f"execution_log = '{safe_execution_log}'",
                f"log_categories = '{safe_log_categories}'"
            ]

            # 只有当订单号不为空时才更新订单号字段
            if order_number and order_number.strip():
                safe_order_number = order_number.replace("'", "''")  # SQL标准转义
                set_clauses.insert(0, f"order_no = '{safe_order_number}'")
                self.logger.info(f"✅ 订单号不为空，将更新order_no字段: {order_number}")
            else:
                self.logger.info("⚠️ 订单号为空，跳过order_no字段更新")

            update_sql = f"""
            UPDATE account_trade_detail
            SET {', '.join(set_clauses)}
            WHERE id = {account_trade_detail_id}
            """

            self.logger.info(f"🔄 准备执行UPDATE操作: {update_sql}")

            # 使用数据库管理器执行SQL
            if hasattr(self, 'database_manager') and self.database_manager:
                try:
                    # 使用现有的数据库管理器
                    import asyncio
                    # 在线程池中执行同步数据库操作
                    affected_rows = await asyncio.to_thread(
                        self.database_manager.execute_update,
                        update_sql.strip(),
                        None  # params参数
                    )

                    if affected_rows > 0:
                        self.logger.info(f"✅ 数据库UPDATE成功，影响行数: {affected_rows}")

                        result = {
                            "success": True,
                            "operation": "update",
                            "affected_rows": affected_rows,
                            "timestamp": datetime.now().isoformat(),
                            "sql_executed": update_sql.strip(),
                            "execution_log": execution_log,
                            "data": {
                                "account_trade_detail_id": account_trade_detail_id,
                                "order_number": order_number,
                                "shop_account": shop_account,
                                "flow_status": 2
                            }
                        }

                        self.logger.info(f"✅ 成功更新account_trade_detail表记录")
                        self.logger.info(f"   记录ID: {account_trade_detail_id}")
                        self.logger.info(f"   订单号: {order_number}")
                        self.logger.info(f"   店铺账号: {shop_account}")
                        self.logger.info(f"   流程状态: 2")

                        return result
                    else:
                        error_msg = f"UPDATE操作未影响任何记录，可能accountTradeDetailId={account_trade_detail_id}不存在"
                        self.logger.warning(f"⚠️ {error_msg}")

                        return {
                            "success": False,
                            "error": error_msg,
                            "operation": "update",
                            "affected_rows": 0,
                            "timestamp": datetime.now().isoformat(),
                            "sql_executed": update_sql.strip()
                        }

                except Exception as e:
                    error_msg = f"数据库操作异常: {str(e)}"
                    self.logger.error(f"❌ {error_msg}")

                    return {
                        "success": False,
                        "error": error_msg,
                        "operation": "update",
                        "timestamp": datetime.now().isoformat(),
                        "sql_executed": update_sql.strip()
                    }
            else:
                error_msg = "数据库管理器不可用"
                self.logger.error(f"❌ {error_msg}")

                return {
                    "success": False,
                    "error": error_msg,
                    "operation": "update",
                    "timestamp": datetime.now().isoformat(),
                    "sql_executed": update_sql.strip()
                }

        except Exception as e:
            error_msg = f"保存到数据库异常: {str(e)}"
            self.logger.error(f"❌ {error_msg}")

            return {
                "success": False,
                "error": error_msg,
                "operation": "update",
                "timestamp": datetime.now().isoformat()
            }

    async def _record_execution_log(self, log_type: str, message: str, context: str,
                                   account_trade_detail_id: str, is_final_success: bool = False) -> str:
        """
        记录执行日志

        Args:
            log_type: 日志类型（SUCCESS/ERROR/INFO）
            message: 日志消息
            context: 上下文
            account_trade_detail_id: 账户交易详情ID
            is_final_success: 是否为最终成功状态

        Returns:
            str: 格式化的执行日志
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{log_type}] [{context}] {message}"

        # 如果是最终成功，只保留成功日志
        if is_final_success:
            return log_entry

        # 否则可以累积日志（这里简化处理）
        return log_entry

    def _manage_log_categories(self, log_type: str, is_success: bool = False) -> str:
        """
        管理日志分类

        Args:
            log_type: 日志类型
            is_success: 是否成功

        Returns:
            str: 日志分类字符串
        """
        if is_success:
            return "SUCCESS,API_PROCESSING,DATABASE_UPDATE"
        else:
            return f"{log_type},API_PROCESSING"

    async def _do_execute(self) -> Dict[str, Any]:
        """
        执行具体的RPA逻辑（API版本）

        这是AsyncBaseRPA要求实现的核心方法，包含完整的API业务逻辑。

        Returns:
            Dict[str, Any]: 执行结果字典
        """
        try:
            self.logger.info("🚀 开始执行店铺账户信息API处理器")

            # 执行批量模式
            result = await self._execute_batch_mode()

            self.logger.info("✅ 店铺账户信息API处理器执行完成", extra_data={
                "总任务数": result.get("execution_summary", {}).get("total_tasks", 0),
                "成功数": result.get("execution_summary", {}).get("success_count", 0),
                "失败数": result.get("execution_summary", {}).get("failed_count", 0),
                "执行时间": f"{result.get('execution_summary', {}).get('execution_time_seconds', 0):.2f}秒"
            })

            return result

        except Exception as e:
            self.logger.error(f"❌ 店铺账户信息API处理器执行异常: {str(e)}")
            raise

    def get_web_driver(self) -> 'BaseWebDriver':
        """
        获取Web驱动实例

        Returns:
            BaseWebDriver: Web驱动实例
        """
        # 使用父类的web_driver属性
        return self.web_driver

    async def _process_single_order_with_cache(self,
                                              order_data: Dict[str, Any],
                                              tokens: Dict[str, str],
                                              user_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理单个订单，带缓存功能

        Args:
            order_data: 订单数据（包含上下文信息）
            tokens: 认证Token字典
            user_info: 用户信息字典

        Returns:
            Optional[Dict[str, Any]]: 处理结果，包含是否找到匹配和订单数据
        """
        try:
            order_id = order_data['order_id']
            account = order_data['account']
            source_order_no = order_data['source_order_no']
            user_id = user_info.get('uid', '')

            self.logger.debug(f"🔍 处理订单: {order_id}, 店铺账号: {account}")

            # 首先检查缓存
            cached_detail = await self.cache_manager.get_with_auto_cleanup(order_id, user_id)

            if cached_detail:
                self.logger.debug(f"💾 使用缓存的订单详情: {order_id}")
                order_detail_response = cached_detail
            else:
                # 缓存未命中，调用API
                self.logger.debug(f"🌐 调用API获取订单详情: {order_id}")
                order_detail_response = await self.api_client.get_order_detail(
                    order_id=order_id,
                    tokens=tokens,
                    user_info=user_info
                )

                if order_detail_response.get('code') == 200:
                    # 缓存API响应
                    self.order_cache.set(order_id, user_id, order_detail_response)
                    self.logger.debug(f"💾 订单详情已缓存: {order_id}")
                else:
                    self.logger.warning(f"⚠️ 订单详情API调用失败: {order_detail_response.get('message', '未知错误')}")
                    return None

            # 检查是否包含sourceOrderNo
            if await self._check_source_order_no_in_detail(order_detail_response, source_order_no):
                self.logger.info(f"✅ 找到匹配的订单: {order_id}, 店铺账号: {account}")
                return {
                    'found_match': True,
                    'order_data': order_data,
                    'detail_response': order_detail_response
                }
            else:
                self.logger.debug(f"❌ 订单不匹配: {order_id}")
                return {
                    'found_match': False,
                    'order_data': order_data
                }

        except Exception as e:
            self.logger.warning(f"⚠️ 处理订单异常: {str(e)}", extra_data={
                'order_id': order_data.get('order_id'),
                'exception_type': type(e).__name__
            })
            raise

    async def _process_orders_concurrent(self,
                                        order_list: List[Dict],
                                        tokens: Dict[str, str],
                                        user_info: Dict[str, Any],
                                        source_order_no: str,
                                        sku: str,
                                        account_trade_detail_id: str) -> bool:
        """
        并发处理订单列表，查找匹配的订单

        Args:
            order_list: 订单列表
            tokens: 认证Token字典
            user_info: 用户信息字典
            source_order_no: 要查找的源订单号
            sku: 系统SKU
            account_trade_detail_id: 账户交易详情ID

        Returns:
            bool: 是否找到匹配的订单
        """
        try:
            self.logger.info(f"🚀 开始并发处理订单列表，订单数量: {len(order_list)}")

            # 准备订单数据，添加必要的上下文信息
            orders_with_context = []
            for order in order_list:
                order_value = order.get('value', {})
                order_id = order_value.get('order_id')
                account = order_value.get('account')

                if order_id:  # 只处理有效的订单ID
                    orders_with_context.append({
                        'order_id': order_id,
                        'account': account,
                        'source_order_no': source_order_no,
                        'sku': sku,
                        'account_trade_detail_id': account_trade_detail_id,
                        'original_order': order
                    })

            if not orders_with_context:
                self.logger.warning("⚠️ 没有有效的订单ID，跳过处理")
                return False

            # 使用批处理器进行并发处理
            if len(orders_with_context) > self.batch_processor.batch_size:
                self.logger.info(f"📦 订单数量较多，使用批处理模式")
                results = await self.batch_processor.process_in_batches(
                    orders_with_context,
                    self.concurrent_processor,
                    self._process_single_order_with_cache,
                    tokens,
                    user_info
                )
            else:
                # 直接并发处理
                results = await self.concurrent_processor.process_orders_concurrent(
                    orders_with_context,
                    self._process_single_order_with_cache,
                    tokens,
                    user_info
                )

            # 处理结果，查找匹配的订单
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.warning(f"⚠️ 订单处理异常: {str(result)}")
                    continue

                if result and result.get('found_match'):
                    # 找到匹配的订单
                    order_data = result['order_data']
                    self.logger.info(f"✅ 找到匹配的订单: {order_data['order_id']}")

                    # 保存到数据库
                    await self._save_to_database(
                        order_data['order_id'],
                        order_data['account'],
                        account_trade_detail_id
                    )

                    # 添加到成功列表
                    self.success_orders.append({
                        'accountTradeDetailId': account_trade_detail_id,
                        'order_id': order_data['order_id'],
                        'shop_account': order_data['account'],
                        'sku': sku,
                        'source_order_no': source_order_no
                    })

                    # 记录缓存统计
                    self.order_cache.log_statistics()

                    return True

            self.logger.info("❌ 并发处理完成，未找到匹配的订单")
            return False

        except Exception as e:
            self.logger.error(f"❌ 并发处理订单异常: {str(e)}")
            return False

    async def _process_orders_sequential(self,
                                        order_list: List[Dict],
                                        tokens: Dict[str, str],
                                        user_info: Dict[str, Any],
                                        source_order_no: str,
                                        sku: str,
                                        account_trade_detail_id: str) -> bool:
        """
        串行处理订单列表，查找匹配的订单（原有逻辑）

        Args:
            order_list: 订单列表
            tokens: 认证Token字典
            user_info: 用户信息字典
            source_order_no: 要查找的源订单号
            sku: 系统SKU
            account_trade_detail_id: 账户交易详情ID

        Returns:
            bool: 是否找到匹配的订单
        """
        for order in order_list:
            order_value = order.get('value', {})
            order_id = order_value.get('order_id')
            account = order_value.get('account')  # 店铺账号

            if not order_id:
                continue

            self.logger.info(f"🔍 检查订单: {order_id}, 店铺账号: {account}")

            # 步骤4: 获取订单详情，查找包裹号
            try:
                order_detail_response = await self.api_client.get_order_detail(
                    order_id=order_id,
                    tokens=tokens,
                    user_info=user_info
                )

                if order_detail_response.get('code') != 200:
                    self.logger.warning(f"⚠️ 订单详情API调用失败: {order_detail_response.get('message', '未知错误')}")
                    continue

                # 步骤5: 在操作日志中查找sourceOrderNo
                if await self._check_source_order_no_in_detail(order_detail_response, source_order_no):
                    self.logger.info(f"✅ 找到匹配的订单: {order_id}, 店铺账号: {account}")

                    # 步骤6: 保存到数据库
                    await self._save_to_database(order_id, account, account_trade_detail_id)

                    # 添加到成功列表
                    self.success_orders.append({
                        'accountTradeDetailId': account_trade_detail_id,
                        'order_id': order_id,
                        'shop_account': account,
                        'sku': sku,
                        'source_order_no': source_order_no
                    })

                    return True

            except Exception as e:
                self.logger.warning(f"⚠️ 处理订单详情异常: {str(e)}")
                continue

        return False

    def _normalize_time_format(self, time_value: Any) -> Optional[str]:
        """
        时间格式标准化方法 - 支持多种时间格式自动转换为标准格式

        支持的格式：
        1. 标准格式：2025-05-07 12:52:00
        2. Java格式：Wed May 07 12:52:00 CST 2025
        3. ISO格式：2025-05-07T12:52:00
        4. 时间戳：**********
        5. 其他常见格式

        Args:
            time_value: 时间值（字符串、数字或其他类型）

        Returns:
            Optional[str]: 标准格式时间字符串 YYYY-MM-DD HH:MM:SS，失败返回None
        """
        if not time_value:
            return None

        try:
            import re
            from datetime import datetime

            # 转换为字符串
            if isinstance(time_value, (int, float)):
                # 处理时间戳（秒或毫秒）
                if time_value > 1e10:  # 毫秒时间戳
                    time_value = time_value / 1000
                time_str = datetime.fromtimestamp(time_value).strftime('%Y-%m-%d %H:%M:%S')
                self.logger.debug(f"时间戳转换: {time_value} -> {time_str}")
                return time_str
            elif not isinstance(time_value, str):
                time_value = str(time_value)

            time_str = time_value.strip()

            # 1. 已经是标准格式 - 直接验证
            if re.match(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$', time_str):
                try:
                    # 验证是否为有效时间
                    datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    self.logger.debug(f"标准格式验证成功: {time_str}")
                    return time_str
                except ValueError:
                    self.logger.warning(f"标准格式但无效时间: {time_str}")
                    return None

            # 2. Java toString()格式：Wed May 07 12:52:00 CST 2025
            java_pattern = r'^[A-Za-z]{3}\s+[A-Za-z]{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}\s+(CST|GMT|UTC)\s+\d{4}$'
            if re.match(java_pattern, time_str):
                try:
                    # 移除时区信息
                    time_part = re.sub(r'\s+(CST|GMT|UTC)\s+', ' ', time_str)
                    parsed_time = datetime.strptime(time_part.strip(), '%a %b %d %H:%M:%S %Y')
                    result = parsed_time.strftime('%Y-%m-%d %H:%M:%S')
                    self.logger.debug(f"Java格式转换: {time_str} -> {result}")
                    return result
                except Exception as e:
                    self.logger.warning(f"Java格式解析失败: {time_str}, 错误: {e}")

            # 3. ISO格式：2025-05-07T12:52:00 或 2025-05-07T12:52:00Z
            iso_pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:Z|[+-]\d{2}:\d{2})?$'
            if re.match(iso_pattern, time_str):
                try:
                    # 移除时区信息
                    clean_time = time_str.replace('Z', '').split('+')[0].split('-', 3)
                    if len(clean_time) == 4:
                        clean_time = '-'.join(clean_time[:3])
                    else:
                        clean_time = clean_time[0] if len(clean_time) == 1 else time_str.split('T')[0] + 'T' + time_str.split('T')[1].split('+')[0].split('Z')[0]

                    if 'T' in clean_time:
                        parsed_time = datetime.fromisoformat(clean_time.replace('T', ' '))
                    else:
                        parsed_time = datetime.strptime(clean_time, '%Y-%m-%d %H:%M:%S')

                    result = parsed_time.strftime('%Y-%m-%d %H:%M:%S')
                    self.logger.debug(f"ISO格式转换: {time_str} -> {result}")
                    return result
                except Exception as e:
                    self.logger.warning(f"ISO格式解析失败: {time_str}, 错误: {e}")

            # 4. 中文格式：2025年05月07日 12:52:00
            chinese_pattern = r'^\d{4}年\d{1,2}月\d{1,2}日\s+\d{2}:\d{2}:\d{2}$'
            if re.match(chinese_pattern, time_str):
                try:
                    parsed_time = datetime.strptime(time_str, '%Y年%m月%d日 %H:%M:%S')
                    result = parsed_time.strftime('%Y-%m-%d %H:%M:%S')
                    self.logger.debug(f"中文格式转换: {time_str} -> {result}")
                    return result
                except Exception as e:
                    self.logger.warning(f"中文格式解析失败: {time_str}, 错误: {e}")

            # 5. 其他分隔符格式：2025/05/07 12:52:00 或 2025.05.07 12:52:00
            alt_pattern = r'^\d{4}[/.-]\d{1,2}[/.-]\d{1,2}\s+\d{2}:\d{2}:\d{2}$'
            if re.match(alt_pattern, time_str):
                try:
                    # 统一替换分隔符
                    normalized = re.sub(r'[/.]', '-', time_str)
                    parsed_time = datetime.strptime(normalized, '%Y-%m-%d %H:%M:%S')
                    result = parsed_time.strftime('%Y-%m-%d %H:%M:%S')
                    self.logger.debug(f"替代格式转换: {time_str} -> {result}")
                    return result
                except Exception as e:
                    self.logger.warning(f"替代格式解析失败: {time_str}, 错误: {e}")

            # 6. 仅日期格式：自动添加时间
            date_only_pattern = r'^\d{4}-\d{2}-\d{2}$'
            if re.match(date_only_pattern, time_str):
                result = f"{time_str} 00:00:00"
                self.logger.debug(f"日期格式转换: {time_str} -> {result}")
                return result

            # 7. 宽松匹配：查找时间模式
            broad_pattern = r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})[^\d]*(\d{2}):(\d{2}):(\d{2})'
            match = re.search(broad_pattern, time_str)
            if match:
                try:
                    year, month, day, hour, minute, second = match.groups()
                    result = f"{year}-{month.zfill(2)}-{day.zfill(2)} {hour}:{minute}:{second}"
                    # 验证生成的时间
                    datetime.strptime(result, '%Y-%m-%d %H:%M:%S')
                    self.logger.debug(f"宽松匹配转换: {time_str} -> {result}")
                    return result
                except Exception as e:
                    self.logger.warning(f"宽松匹配解析失败: {time_str}, 错误: {e}")

            # 所有格式都失败
            self.logger.error(f"❌ 无法识别时间格式: {time_str}")
            return None

        except Exception as e:
            self.logger.error(f"❌ 时间格式标准化异常: {str(e)}, 输入: {time_value}")
            return None

    def _get_current_system_sku(self) -> Optional[str]:
        """
        获取当前任务的systemSku值

        优先级：当前任务数据 > 备用task_data > 配置文件（不推荐）

        Returns:
            Optional[str]: 当前任务的systemSku值，如果没有找到则返回None
        """
        try:
            # 方法1：从当前任务数据中获取（最高优先级）
            if self._current_task_data and 'systemSku' in self._current_task_data:
                system_sku = self._current_task_data.get('systemSku')
                if system_sku is not None:
                    self.logger.debug(f"从_current_task_data获取systemSku: {system_sku}")
                    return str(system_sku).strip()

            # 所有方法都失败
            self.logger.error("❌ 无法获取systemSku值")
            self.logger.error("   请确保任务数据中包含systemSku字段")
            return None

        except Exception as e:
            self.logger.error(f"❌ 获取systemSku时发生异常: {str(e)}")
            return None

    def _get_current_creation_time(self) -> Optional[str]:
        """
        获取当前任务的creationTime值（增强版 - 自动时间格式标准化）

        优先级：当前任务数据 > 备用task_data > 配置文件（不推荐）

        Returns:
            Optional[str]: 标准格式的creationTime值，如果没有找到则返回None
        """
        try:
            # 方法1：从当前任务数据中获取（最高优先级）
            if self._current_task_data and 'creationTime' in self._current_task_data:
                creation_time = self._current_task_data.get('creationTime')
                if creation_time is not None:
                    # 🔧 时间格式标准化处理
                    normalized_time = self._normalize_time_format(creation_time)
                    if normalized_time:
                        self.logger.debug(f"从_current_task_data获取creationTime（已标准化）: {normalized_time}")
                        return normalized_time
                    else:
                        self.logger.error(f"creationTime格式标准化失败: {creation_time}")

            # 所有方法都失败
            self.logger.error("❌ 无法获取creationTime值")
            self.logger.error("   请确保任务数据中包含creationTime字段")
            return None

        except Exception as e:
            self.logger.error(f"❌ 获取creationTime时发生异常: {str(e)}")
            return None

    def _get_current_source_order_no(self) -> Optional[str]:
        """
        获取当前任务的sourceOrderNo值

        优先级：当前任务数据 > 备用task_data > 配置文件（不推荐）

        Returns:
            Optional[str]: 当前任务的sourceOrderNo值，如果没有找到则返回None
        """
        try:
            # 方法1：从当前任务数据中获取（最高优先级）
            if self._current_task_data and 'sourceOrderNo' in self._current_task_data:
                source_order_no = self._current_task_data.get('sourceOrderNo')
                if source_order_no is not None:
                    self.logger.debug(f"从_current_task_data获取sourceOrderNo: {source_order_no}")
                    return str(source_order_no).strip()

            # 所有方法都失败
            self.logger.error("❌ 无法获取sourceOrderNo值")
            self.logger.error("   请确保任务数据中包含sourceOrderNo字段")
            return None

        except Exception as e:
            self.logger.error(f"❌ 获取sourceOrderNo时发生异常: {str(e)}")
            return None

    def _get_current_account_trade_detail_id(self) -> Optional[str]:
        """
        获取当前任务的accountTradeDetailId

        Returns:
            Optional[str]: 当前任务的accountTradeDetailId值
        """
        try:
            # 从当前任务数据中获取
            if self._current_task_data and 'accountTradeDetailId' in self._current_task_data:
                account_id = self._current_task_data.get('accountTradeDetailId')
                if account_id is not None:
                    # 确保返回字符串格式
                    if isinstance(account_id, (int, float)):
                        return str(int(account_id))
                    else:
                        return str(account_id).strip()

            return None

        except Exception as e:
            self.logger.debug(f"获取accountTradeDetailId失败: {e}")
            return None

    def _get_processor_name(self) -> str:
        """
        获取处理器名称，优先获取当前job的jobName

        Returns:
            str: 处理器名称
        """
        try:
            # 尝试从环境变量获取jobName
            import os
            job_name = os.environ.get('JOB_NAME', '')
            if job_name:
                return job_name

            # 备用：使用脚本名称
            return self.script_name if hasattr(self, 'script_name') else "shop_account_processor_async"

        except Exception as e:
            self.logger.debug(f"获取处理器名称失败: {e}")
            return "shop_account_processor_async"

    async def _batch_process_failed_orders(self) -> Dict[str, Any]:
        """
        批量处理失败订单 - 将失败的account_trade_detail_id的flow_status更新为1

        使用单条SQL批量更新，减少MySQL IO请求

        Returns:
            Dict[str, Any]: 批量更新结果
        """
        try:
            if not self.failed_orders:
                self.logger.info("📋 没有失败订单需要处理")
                return {"success": True, "processed_count": 0, "message": "没有失败订单"}

            # 去重处理，确保同一个account_trade_detail_id只处理一次
            unique_failed_ids = list(set(self.failed_orders))

            self.logger.info(f"🔄 开始批量处理失败订单，共 {len(unique_failed_ids)} 个ID")
            self.logger.info(f"   失败的account_trade_detail_id: {unique_failed_ids}")

            # 🔧 生成批量更新SQL - 一次IO完成所有更新
            batch_update_result = await self._execute_batch_update_failed_orders(unique_failed_ids)

            if batch_update_result.get("success", False):
                self.logger.info(f"✅ 批量处理失败订单完成，更新了 {batch_update_result.get('affected_rows', 0)} 条记录")

                # 清空失败订单列表
                self.failed_orders.clear()

                return batch_update_result
            else:
                self.logger.error(f"❌ 批量处理失败订单失败: {batch_update_result.get('error', 'Unknown error')}")
                return batch_update_result

        except Exception as e:
            error_msg = f"批量处理失败订单异常: {str(e)}"
            self.logger.error(error_msg)

            return {
                "success": False,
                "error": error_msg,
                "processed_count": 0,
                "timestamp": datetime.now().isoformat()
            }

    async def _execute_batch_update_failed_orders(self, failed_ids: List[str]) -> Dict[str, Any]:
        """
        执行批量更新失败订单的SQL操作（对标备份版本）

        Args:
            failed_ids: 失败的account_trade_detail_id列表

        Returns:
            Dict[str, Any]: 执行结果
        """
        from datetime import datetime

        try:
            if not failed_ids:
                return {"success": True, "affected_rows": 0, "message": "没有需要更新的ID"}

            # 🔧 增强ID格式化 - 区分数字ID和字符串ID（对标备份版本）
            numeric_ids = []
            string_ids = []

            for id_val in failed_ids:
                str_id = str(id_val).strip()
                try:
                    # 尝试转换为整数（纯数字ID）
                    int_id = int(str_id)
                    numeric_ids.append(int_id)
                except ValueError:
                    # 字符串ID（如debug_001）
                    string_ids.append(str_id)

            self.logger.info(f"🔧 使用个性化更新方式，为每个ID设置具体的错误信息")

            # 🔄 使用个性化更新方式，为每个ID设置其具体的execution_log
            # 而不是使用通用的批量更新，这样可以保留每个ID的具体错误信息
            update_statements = []

            # 为每个失败的ID生成个性化的UPDATE语句
            for failed_id in failed_ids:
                # 获取该ID的具体错误信息
                if failed_id in self.failed_orders_detail:
                    detail_info = self.failed_orders_detail[failed_id]
                    specific_execution_log = detail_info["execution_log"]
                else:
                    # 备用：如果没有详细信息，使用通用错误信息
                    specific_execution_log = f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {self._get_processor_name()}: [批量处理] RPA执行失败，原因未知"

                # 生成失败时的log_categories
                error_log_categories = self._manage_log_categories(log_type="ERROR", is_success=False)

                # 安全处理SQL字符串，转义单引号
                safe_execution_log = specific_execution_log.replace("'", "''")
                safe_log_categories = error_log_categories.replace("'", "''")

                # 确定ID的数据类型并构造WHERE条件（对标备份版本）
                try:
                    int_id = int(str(failed_id).strip())
                    where_condition = f"id = {int_id}"
                except ValueError:
                    # 字符串ID
                    where_condition = f"CAST(id AS CHAR) = '{failed_id}'"

                # 构造个性化UPDATE语句（对标备份版本）
                update_sql = f"""UPDATE account_trade_detail
SET flow_status = 1,
    execution_log = '{safe_execution_log}',
    log_categories = '{safe_log_categories}'
WHERE {where_condition}
AND flow_status != 2"""

                update_statements.append(update_sql)

            # 执行个性化批量更新
            return await self._execute_personalized_batch_update(update_statements, failed_ids)

        except Exception as e:
            error_msg = f"批量更新SQL执行异常: {str(e)}"
            self.logger.error(error_msg)

            return {
                "success": False,
                "error": error_msg,
                "operation": "batch_update",
                "processed_ids": failed_ids,
                "timestamp": datetime.now().isoformat()
            }

    async def _execute_personalized_batch_update(self, update_statements: List[str], failed_ids: List[str]) -> Dict[str, Any]:
        """
        执行个性化的批量更新，为每个ID使用其具体的错误信息（对标备份版本）

        Args:
            update_statements: 个性化UPDATE语句列表
            failed_ids: 失败的ID列表

        Returns:
            Dict[str, Any]: 执行结果
        """
        from datetime import datetime

        try:
            if not update_statements:
                return {"success": True, "affected_rows": 0, "message": "没有需要更新的语句"}

            self.logger.info(f"🔧 执行个性化批量更新，共 {len(update_statements)} 条UPDATE语句")

            total_affected_rows = 0
            successful_updates = 0
            failed_updates = 0

            # 尝试使用数据库管理器执行
            if hasattr(self, '_db_manager') and self._db_manager:
                try:
                    # 方案1：尝试事务批量执行
                    try:
                        # 将所有UPDATE语句合并为一个事务
                        transaction_sql = ";\n".join(update_statements) + ";"

                        affected_rows = await self._db_manager.execute_update(
                            transaction_sql,
                            None  # params参数
                        )

                        if affected_rows >= 0:
                            total_affected_rows = affected_rows
                            successful_updates = len(update_statements)

                            self.logger.info(f"✅ 个性化批量更新成功，影响行数: {affected_rows}")

                            return {
                                "success": True,
                                "operation": "personalized_batch_update",
                                "affected_rows": total_affected_rows,
                                "successful_updates": successful_updates,
                                "failed_updates": failed_updates,
                                "processed_ids": failed_ids,
                                "update_count": len(update_statements),
                                "timestamp": datetime.now().isoformat(),
                                "method": "database_manager_transaction"
                            }
                        else:
                            self.logger.warning("⚠️ 事务批量更新未影响任何记录，尝试逐条执行")
                            raise Exception("事务批量更新未影响记录")

                    except Exception as transaction_error:
                        self.logger.warning(f"⚠️ 事务批量更新失败: {transaction_error}，尝试逐条执行")

                        # 方案2：逐条执行UPDATE语句
                        for i, update_sql in enumerate(update_statements):
                            try:
                                affected_rows = await self._db_manager.execute_update(
                                    update_sql,
                                    None
                                )

                                if affected_rows > 0:
                                    total_affected_rows += affected_rows
                                    successful_updates += 1
                                    self.logger.debug(f"✅ UPDATE语句 {i+1}/{len(update_statements)} 执行成功，影响行数: {affected_rows}")
                                else:
                                    failed_updates += 1
                                    self.logger.debug(f"⚠️ UPDATE语句 {i+1}/{len(update_statements)} 未影响任何记录")

                            except Exception as single_error:
                                failed_updates += 1
                                self.logger.error(f"❌ UPDATE语句 {i+1}/{len(update_statements)} 执行失败: {single_error}")

                        if successful_updates > 0:
                            self.logger.info(f"✅ 个性化逐条更新完成，成功: {successful_updates}, 失败: {failed_updates}, 总影响行数: {total_affected_rows}")

                            return {
                                "success": True,
                                "operation": "personalized_batch_update",
                                "affected_rows": total_affected_rows,
                                "successful_updates": successful_updates,
                                "failed_updates": failed_updates,
                                "processed_ids": failed_ids,
                                "update_count": len(update_statements),
                                "timestamp": datetime.now().isoformat(),
                                "method": "database_manager_sequential"
                            }
                        else:
                            self.logger.error("❌ 所有UPDATE语句都执行失败")
                            raise Exception("所有UPDATE语句都执行失败")

                except Exception as db_manager_error:
                    self.logger.error(f"❌ 数据库管理器执行失败: {db_manager_error}")
                    # 回退到备用方案
                    return await self._fallback_personalized_update(update_statements, failed_ids)
            else:
                self.logger.warning("⚠️ 未找到数据库管理器实例，使用备用个性化更新方案")
                return await self._fallback_personalized_update(update_statements, failed_ids)

        except Exception as e:
            error_msg = f"个性化批量更新异常: {str(e)}"
            self.logger.error(error_msg)

            return {
                "success": False,
                "error": error_msg,
                "operation": "personalized_batch_update",
                "processed_ids": failed_ids,
                "update_count": len(update_statements),
                "timestamp": datetime.now().isoformat()
            }

    async def _fallback_personalized_update(self, update_statements: List[str], failed_ids: List[str]) -> Dict[str, Any]:
        """
        个性化更新的备用方案（对标备份版本）

        Args:
            update_statements: UPDATE语句列表
            failed_ids: 失败的ID列表

        Returns:
            Dict[str, Any]: 执行结果
        """
        from datetime import datetime

        try:
            # 方案1：尝试使用临时数据库管理器
            try:
                from app.core.database import DatabaseManager
                temp_db_manager = DatabaseManager(self.business_type, self.script_name)

                total_affected_rows = 0
                successful_updates = 0

                for i, update_sql in enumerate(update_statements):
                    try:
                        affected_rows = temp_db_manager.execute_update(
                            update_sql,
                            None
                        )

                        if affected_rows > 0:
                            total_affected_rows += affected_rows
                            successful_updates += 1

                    except Exception as single_error:
                        self.logger.warning(f"⚠️ 备用方案UPDATE语句 {i+1} 失败: {single_error}")

                if successful_updates > 0:
                    self.logger.info(f"✅ 备用个性化更新成功：更新了 {total_affected_rows} 条记录")

                    return {
                        "success": True,
                        "operation": "personalized_batch_update",
                        "affected_rows": total_affected_rows,
                        "successful_updates": successful_updates,
                        "failed_updates": len(update_statements) - successful_updates,
                        "processed_ids": failed_ids,
                        "update_count": len(update_statements),
                        "timestamp": datetime.now().isoformat(),
                        "method": "fallback_database_manager"
                    }

            except ImportError:
                self.logger.warning("⚠️ 无法导入DatabaseManager，尝试其他备用方案")
            except Exception as temp_db_error:
                self.logger.warning(f"⚠️ 临时数据库管理器失败: {temp_db_error}")

            # 方案2：记录到文件，供后续批处理
            try:
                import json
                import os

                pending_operations = []
                for i, update_sql in enumerate(update_statements):
                    pending_operations.append({
                        "timestamp": datetime.now().isoformat(),
                        "operation": "personalized_update",
                        "table": "account_trade_detail",
                        "sql": update_sql,
                        "target_id": failed_ids[i] if i < len(failed_ids) else "unknown",
                        "target_flow_status": 1,
                        "condition": "flow_status != 2",
                        "status": "pending"
                    })

                os.makedirs("logs", exist_ok=True)
                pending_file = f"logs/pending_personalized_operations_{datetime.now().strftime('%Y%m%d')}.jsonl"

                with open(pending_file, "a", encoding="utf-8") as f:
                    for operation in pending_operations:
                        f.write(json.dumps(operation, ensure_ascii=False) + "\n")

                self.logger.info(f"📝 个性化更新操作已记录到文件: {pending_file}")

                return {
                    "success": True,  # 标记为成功，因为操作已记录
                    "operation": "personalized_batch_update",
                    "affected_rows": 0,  # 实际未执行
                    "successful_updates": 0,
                    "failed_updates": len(update_statements),
                    "processed_ids": failed_ids,
                    "update_count": len(update_statements),
                    "timestamp": datetime.now().isoformat(),
                    "method": "file_logging",
                    "pending_file": pending_file,
                    "message": "个性化更新操作已记录到文件，等待后续批处理"
                }

            except Exception as file_error:
                self.logger.error(f"❌ 文件记录也失败: {file_error}")

            # 最终备用方案：详细日志记录
            self.logger.info("📋 最终备用方案：记录个性化更新信息到日志")
            for i, update_sql in enumerate(update_statements):
                self.logger.info(f"   UPDATE语句 {i+1}: {update_sql}")

            return {
                "success": False,
                "operation": "personalized_batch_update",
                "error": "所有个性化更新方案都失败，已记录到日志",
                "processed_ids": failed_ids,
                "update_count": len(update_statements),
                "timestamp": datetime.now().isoformat(),
                "method": "log_only"
            }

        except Exception as fallback_error:
            self.logger.error(f"❌ 个性化更新备用方案失败: {fallback_error}")

            return {
                "success": False,
                "operation": "personalized_batch_update",
                "error": f"备用方案失败: {str(fallback_error)}",
                "processed_ids": failed_ids,
                "update_count": len(update_statements),
                "timestamp": datetime.now().isoformat(),
                "method": "fallback_failed"
            }

    async def _fallback_batch_update(self, batch_sql: str, failed_ids: List[str]) -> Dict[str, Any]:
        """
        批量更新的备用方案

        Args:
            batch_sql: 批量更新SQL
            failed_ids: 失败的ID列表

        Returns:
            Dict[str, Any]: 执行结果
        """
        from datetime import datetime

        try:
            # 方案1：尝试使用临时数据库管理器
            try:
                from app.core.database import DatabaseManager
                temp_db_manager = DatabaseManager(self.business_type, self.script_name)

                affected_rows = await temp_db_manager.execute_update(
                    batch_sql,
                    None  # params参数
                )

                if affected_rows >= 0:  # >= 0 因为可能没有需要更新的记录
                    self.logger.info(f"✅ 备用方案成功：批量更新了 {affected_rows} 条记录")

                    return {
                        "success": True,
                        "operation": "batch_update",
                        "affected_rows": affected_rows,
                        "processed_ids": failed_ids,
                        "sql_executed": batch_sql,
                        "timestamp": datetime.now().isoformat(),
                        "method": "fallback_database_manager"
                    }

            except ImportError:
                self.logger.warning("⚠️ 无法导入DatabaseManager，尝试其他备用方案")
            except Exception as temp_db_error:
                self.logger.warning(f"⚠️ 临时数据库管理器失败: {temp_db_error}")

            # 方案2：记录到文件，供后续批处理
            try:
                import json
                import os

                pending_batch_operation = {
                    "timestamp": datetime.now().isoformat(),
                    "operation": "batch_update_failed_orders",
                    "table": "account_trade_detail",
                    "sql": batch_sql,
                    "affected_ids": failed_ids,
                    "target_flow_status": 1,
                    "condition": "flow_status != 2",
                    "status": "pending"
                }

                os.makedirs("logs", exist_ok=True)
                pending_file = f"logs/pending_batch_operations_{datetime.now().strftime('%Y%m%d')}.jsonl"

                with open(pending_file, "a", encoding="utf-8") as f:
                    f.write(json.dumps(pending_batch_operation, ensure_ascii=False) + "\n")

                self.logger.info(f"📝 批量更新操作已记录到文件: {pending_file}")

                return {
                    "success": True,  # 标记为成功，因为操作已记录
                    "operation": "batch_update",
                    "affected_rows": 0,  # 实际未执行
                    "processed_ids": failed_ids,
                    "timestamp": datetime.now().isoformat(),
                    "method": "file_logging",
                    "pending_file": pending_file,
                    "message": "批量更新操作已记录到文件，等待后续批处理"
                }

            except Exception as file_error:
                self.logger.error(f"❌ 文件记录也失败: {file_error}")

            # 最终备用方案：详细日志记录
            self.logger.info("📋 最终备用方案：记录批量更新信息到日志")
            self.logger.info(f"   需要执行的批量SQL: {batch_sql}")
            self.logger.info(f"   影响的ID数量: {len(failed_ids)}")
            self.logger.info(f"   失败的IDs: {failed_ids}")

            return {
                "success": False,
                "operation": "batch_update",
                "error": "所有批量更新方案都失败，已记录到日志",
                "processed_ids": failed_ids,
                "sql_to_execute": batch_sql,
                "timestamp": datetime.now().isoformat(),
                "method": "log_only"
            }

        except Exception as fallback_error:
            self.logger.error(f"❌ 批量更新备用方案失败: {fallback_error}")

            return {
                "success": False,
                "operation": "batch_update",
                "error": f"备用方案失败: {str(fallback_error)}",
                "processed_ids": failed_ids,
                "timestamp": datetime.now().isoformat(),
                "method": "fallback_failed"
            }


async def main():
    """
    主函数 - 创建并执行异步店铺账号信息API处理器
    """
    try:
        # 创建异步API处理器实例
        processor = AsyncShopAccountInfoAPIProcessor()

        # 执行API处理任务
        result = await processor.execute()

        # 输出结果
        print(f"异步API处理器执行结果: {result['status']}")
        if result['status'] == 'success':
            print(f"处理的订单数量: {result['data'].get('total_processed', 0)}")
        else:
            print(f"执行失败: {result.get('error', {}).get('error_message', 'Unknown error')}")

        return result

    except Exception as e:
        print(f"异步API处理器执行异常: {str(e)}")
        return {"status": "failed", "error": str(e)}


if __name__ == "__main__":
    asyncio.run(main())
