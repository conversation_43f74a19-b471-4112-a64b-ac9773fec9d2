{"_comments": {"description": "店铺账户信息提取业务专用配置", "priority": "业务级配置会覆盖项目全局配置中的相同项目", "usage": "通用配置请查看 app/config/default.json"}, "_comment_business_config": "========== 店铺账户信息业务专用配置 ==========", "timeWindowHours": 24, "_timeWindowHours_comment": "时间窗口范围（小时），查找创建时间前N小时内的订单", "_comment_batch_config": "========== 批量处理配置 ==========", "debugMode": false, "_debugMode_comment": "调试模式，启用时使用内置测试数据进行批量处理，生产环境应为false，通过环境变量DETAILCONDITIONLIST传递任务配置", "batchProcessingEnabled": true, "_batchProcessingEnabled_comment": "是否启用批量处理功能，必须为true", "maxBatchSize": 50, "_maxBatchSize_comment": "单次批量处理的最大任务数量，防止资源过载", "_comment_override_global": "========== 覆盖全局配置的项目 ==========", "PLAYWRIGHT_PAGE_LOAD_WAIT": 15, "_PLAYWRIGHT_PAGE_LOAD_WAIT_comment": "店铺账户信息业务需要更长的页面等待时间（秒）", "PLAYWRIGHT_TIMEOUT": 45, "_PLAYWRIGHT_TIMEOUT_comment": "Playwright元素等待超时时间（秒），店铺账户信息提取需要更长的等待时间", "LOG_LEVEL": "INFO", "_LOG_LEVEL_comment": "日志级别，生产环境建议使用INFO，调试时可设为DEBUG"}