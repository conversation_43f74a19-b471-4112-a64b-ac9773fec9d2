"""
通用认证管理器测试脚本

测试基于block_manager成熟实现的UniversalAuthManager：
1. 认证管理器初始化测试
2. 缓存机制测试
3. 多渠道用户信息提取测试
4. 与API客户端集成测试

使用方式：
    python test_auth_manager.py
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.shared.utils.auth_manager import UniversalAuthManager
from app.business.shop_account_info.shop_account_api_client import ShopAccountApiClient
from app.utils.logger import get_rpa_logger


async def test_auth_manager_initialization():
    """测试认证管理器初始化"""
    print("🧪 测试1: 认证管理器初始化")
    
    try:
        # 创建认证管理器
        test_logger = get_rpa_logger("shop_account_info", "test_auth_manager")
        auth_manager = UniversalAuthManager(
            logger=test_logger,
            business_type="shop_account_info",
            script_name="test_auth_manager"
        )
        
        print("✅ 认证管理器初始化成功")
        
        # 测试基本属性
        assert hasattr(auth_manager, 'yimai_login_manager')
        assert hasattr(auth_manager, 'token_extractor')
        assert hasattr(auth_manager, 'user_info_extractor')
        assert hasattr(auth_manager, 'login_response_data')
        assert hasattr(auth_manager, 'cached_tokens')
        assert hasattr(auth_manager, 'cached_user_info')
        
        print("✅ 基本属性验证通过")
        
        # 测试缓存状态
        is_valid = auth_manager._is_cache_valid()
        assert not is_valid  # 初始状态应该无缓存
        print("✅ 初始缓存状态验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 认证管理器初始化测试失败: {str(e)}")
        return False


async def test_cache_mechanism():
    """测试缓存机制"""
    print("\n🧪 测试2: 缓存机制")
    
    try:
        test_logger = get_rpa_logger("shop_account_info", "test_cache")
        auth_manager = UniversalAuthManager(
            logger=test_logger,
            business_type="shop_account_info",
            script_name="test_cache"
        )
        
        # 测试空缓存
        tokens, user_info = auth_manager.get_cached_auth_info()
        assert tokens is None and user_info is None
        print("✅ 空缓存测试通过")
        
        # 模拟设置缓存
        from datetime import datetime
        mock_tokens = {'jwt_token': 'test_token', 'session_id': 'test_session'}
        mock_user_info = {'uid': 'test_user', 'distributor_id': 'test_dist'}
        
        auth_manager.cached_tokens = mock_tokens
        auth_manager.cached_user_info = mock_user_info
        auth_manager.last_auth_time = datetime.now()
        
        # 测试有效缓存
        assert auth_manager._is_cache_valid()
        cached_tokens, cached_user_info = auth_manager.get_cached_auth_info()
        assert cached_tokens == mock_tokens
        assert cached_user_info == mock_user_info
        print("✅ 有效缓存测试通过")
        
        # 测试清除缓存
        auth_manager.clear_cache()
        assert not auth_manager._is_cache_valid()
        tokens, user_info = auth_manager.get_cached_auth_info()
        assert tokens is None and user_info is None
        print("✅ 缓存清除测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存机制测试失败: {str(e)}")
        return False


async def test_distributor_id_extraction():
    """测试distributor_id提取功能"""
    print("\n🧪 测试3: distributor_id提取功能")
    
    try:
        test_logger = get_rpa_logger("shop_account_info", "test_distributor")
        auth_manager = UniversalAuthManager(
            logger=test_logger,
            business_type="shop_account_info",
            script_name="test_distributor"
        )
        
        # 测试数据1：标准路径
        response_data1 = {
            'code': 200,
            'data': {
                'account_data': {
                    'id': 'user123',
                    'distributor_id': 'dist456'
                }
            }
        }
        
        distributor_id = auth_manager._extract_distributor_id_from_response(response_data1)
        assert distributor_id == 'dist456'
        print("✅ 标准路径提取测试通过")
        
        # 测试数据2：备用路径
        response_data2 = {
            'data': {
                'distributor_id': 'dist789'
            }
        }
        
        distributor_id = auth_manager._extract_distributor_id_from_response(response_data2)
        assert distributor_id == 'dist789'
        print("✅ 备用路径提取测试通过")
        
        # 测试数据3：无distributor_id
        response_data3 = {
            'data': {
                'account_data': {
                    'id': 'user123'
                }
            }
        }
        
        distributor_id = auth_manager._extract_distributor_id_from_response(response_data3)
        assert distributor_id is None
        print("✅ 无distributor_id测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ distributor_id提取测试失败: {str(e)}")
        return False


async def test_user_info_conversion():
    """测试用户信息转换功能"""
    print("\n🧪 测试4: 用户信息转换功能")
    
    try:
        test_logger = get_rpa_logger("shop_account_info", "test_conversion")
        auth_manager = UniversalAuthManager(
            logger=test_logger,
            business_type="shop_account_info",
            script_name="test_conversion"
        )
        
        # 模拟UserInfo对象
        class MockUserInfo:
            def __init__(self):
                self.user_id = 'test_user_123'
                self.distributor_id = 'test_dist_456'
                self.account = 'test_account'
                self.account_name = 'Test Account Name'
                self.authority_id = 'auth_123'
        
        mock_user_info = MockUserInfo()
        
        # 测试转换
        user_info_dict = auth_manager._convert_user_info_to_dict(mock_user_info)
        
        # 验证转换结果
        assert user_info_dict['uid'] == 'test_user_123'
        assert user_info_dict['distributor_id'] == 'test_dist_456'
        assert user_info_dict['account'] == 'test_account'
        assert user_info_dict['account_name'] == 'Test Account Name'
        assert user_info_dict['authority_id'] == 'auth_123'
        
        print("✅ 用户信息转换测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户信息转换测试失败: {str(e)}")
        return False


async def test_api_client_integration():
    """测试与API客户端的集成"""
    print("\n🧪 测试5: API客户端集成")
    
    try:
        # 创建API客户端
        test_logger = get_rpa_logger("shop_account_info", "test_integration")
        api_client = ShopAccountApiClient(
            logger=test_logger,
            business_type="shop_account_info",
            script_name="test_integration"
        )
        
        print("✅ API客户端创建成功")
        
        # 验证认证管理器集成
        assert hasattr(api_client, 'auth_manager')
        assert isinstance(api_client.auth_manager, UniversalAuthManager)
        print("✅ 认证管理器集成验证通过")
        
        # 测试认证状态检查
        is_authenticated = api_client.is_authenticated()
        assert not is_authenticated  # 初始状态应该未认证
        print("✅ 初始认证状态验证通过")
        
        # 测试缓存认证信息获取
        tokens, user_info = api_client.get_cached_auth_info()
        assert tokens is None and user_info is None
        print("✅ 缓存认证信息获取测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ API客户端集成测试失败: {str(e)}")
        return False


async def test_network_response_handling():
    """测试网络响应处理"""
    print("\n🧪 测试6: 网络响应处理")
    
    try:
        test_logger = get_rpa_logger("shop_account_info", "test_network")
        auth_manager = UniversalAuthManager(
            logger=test_logger,
            business_type="shop_account_info",
            script_name="test_network"
        )
        
        # 模拟响应对象
        class MockResponse:
            def __init__(self, url, status, text_content):
                self.url = url
                self.status = status
                self._text_content = text_content
            
            async def text(self):
                return self._text_content
        
        # 测试登录响应处理
        login_response_text = '''
        {
            "code": 200,
            "data": {
                "account_data": {
                    "id": "user123",
                    "distributor_id": "dist456"
                }
            }
        }
        '''
        
        mock_response = MockResponse(
            url="https://example.com/login/accountLogin",
            status=200,
            text_content=login_response_text
        )
        
        # 处理响应
        await auth_manager._handle_response(mock_response)
        
        # 验证处理结果
        assert auth_manager.login_response_data is not None
        assert auth_manager.login_response_data['code'] == 200
        assert auth_manager.user_id == "user123"
        
        print("✅ 登录响应处理测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络响应处理测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始通用认证管理器测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(await test_auth_manager_initialization())
    test_results.append(await test_cache_mechanism())
    test_results.append(await test_distributor_id_extraction())
    test_results.append(await test_user_info_conversion())
    test_results.append(await test_api_client_integration())
    test_results.append(await test_network_response_handling())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 60)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！通用认证管理器功能正常")
        print("\n🎉 基于block_manager的成熟实现已成功集成")
        print("📈 相比原有实现的改进：")
        print("   - ✅ 统一的认证流程")
        print("   - ✅ 多渠道用户信息提取")
        print("   - ✅ 智能缓存机制")
        print("   - ✅ 增强的distributor_id提取")
        print("   - ✅ 完善的错误处理")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行异常: {str(e)}")
        sys.exit(1)
