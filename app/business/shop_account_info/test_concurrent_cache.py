"""
并发处理和缓存功能测试脚本

测试新增的并发处理和缓存功能：
1. 并发处理器测试
2. 缓存功能测试
3. 批处理器测试
4. 性能对比测试

使用方式：
    python test_concurrent_cache.py
"""

import asyncio
import logging
import time
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.shared.utils import ConcurrentOrderProcessor, BatchProcessor, OrderDetailCache, CacheManager


async def test_order_cache():
    """测试订单缓存功能"""
    print("🧪 测试1: 订单缓存功能")
    
    try:
        # 创建缓存实例
        cache = OrderDetailCache(cache_duration_minutes=1, max_cache_size=100)
        
        # 测试数据
        test_orders = [
            ("order_001", "user_001", {"status": "completed", "amount": 100}),
            ("order_002", "user_001", {"status": "pending", "amount": 200}),
            ("order_003", "user_002", {"status": "shipped", "amount": 150})
        ]
        
        # 测试设置缓存
        for order_id, user_id, data in test_orders:
            cache.set(order_id, user_id, data)
            print(f"✅ 缓存已设置: {order_id}")
        
        # 测试获取缓存
        for order_id, user_id, expected_data in test_orders:
            cached_data = cache.get(order_id, user_id)
            if cached_data == expected_data:
                print(f"✅ 缓存命中: {order_id}")
            else:
                print(f"❌ 缓存不匹配: {order_id}")
        
        # 测试缓存统计
        stats = cache.get_statistics()
        print(f"📊 缓存统计: 命中率 {stats['hit_rate']:.1f}%, 大小 {stats['cache_size']}")
        
        # 测试缓存过期（等待1分钟后测试）
        print("⏱️ 等待5秒测试缓存过期...")
        await asyncio.sleep(5)

        # 修改缓存时间为更短的时间进行测试
        from datetime import timedelta
        cache.cache_duration = timedelta(seconds=1)  # 改为1秒过期

        # 再等待2秒确保过期
        await asyncio.sleep(2)

        expired_data = cache.get("order_001", "user_001")
        if expired_data is None:
            print("✅ 缓存过期测试通过")
        else:
            print("⚠️ 缓存可能未正确过期")
        
        return True
        
    except Exception as e:
        print(f"❌ 订单缓存测试失败: {str(e)}")
        return False


async def test_concurrent_processor():
    """测试并发处理器"""
    print("\n🧪 测试2: 并发处理器")
    
    try:
        # 创建并发处理器
        processor = ConcurrentOrderProcessor(
            max_concurrent=2,
            request_interval=0.5,
            logger=logging.getLogger("test_concurrent")
        )
        
        # 模拟API调用函数
        async def mock_api_call(order_data, *args, **kwargs):
            order_id = order_data.get('order_id', 'unknown')
            # 模拟API调用延迟
            await asyncio.sleep(0.2)
            
            # 模拟一些订单处理失败
            if order_id.endswith('_fail'):
                raise Exception(f"模拟API调用失败: {order_id}")
            
            return {
                'order_id': order_id,
                'status': 'processed',
                'processing_time': 0.2
            }
        
        # 测试订单数据
        test_orders = [
            {'order_id': 'order_001'},
            {'order_id': 'order_002'},
            {'order_id': 'order_003_fail'},  # 这个会失败
            {'order_id': 'order_004'},
            {'order_id': 'order_005'}
        ]
        
        print(f"🚀 开始并发处理 {len(test_orders)} 个订单")
        start_time = time.time()
        
        # 执行并发处理
        results = await processor.process_orders_concurrent(
            test_orders,
            mock_api_call
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 分析结果
        successful_results = [r for r in results if not isinstance(r, Exception)]
        failed_results = [r for r in results if isinstance(r, Exception)]
        
        print(f"✅ 并发处理完成")
        print(f"   总耗时: {processing_time:.2f}秒")
        print(f"   成功: {len(successful_results)}个")
        print(f"   失败: {len(failed_results)}个")
        
        # 获取统计信息
        stats = processor.get_statistics()
        print(f"📊 处理统计: 成功率 {stats['success_rate']:.1f}%, 平均耗时 {stats['average_time_per_request']:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 并发处理器测试失败: {str(e)}")
        return False


async def test_batch_processor():
    """测试批处理器"""
    print("\n🧪 测试3: 批处理器")
    
    try:
        # 创建组件
        concurrent_processor = ConcurrentOrderProcessor(
            max_concurrent=2,
            request_interval=0.3,
            logger=logging.getLogger("test_batch")
        )
        
        batch_processor = BatchProcessor(
            batch_size=3,
            batch_interval=0.5,
            logger=logging.getLogger("test_batch")
        )
        
        # 模拟API调用
        async def mock_api_call(order_data, *args, **kwargs):
            order_id = order_data.get('order_id', 'unknown')
            await asyncio.sleep(0.1)  # 模拟API延迟
            return {'order_id': order_id, 'batch_processed': True}
        
        # 创建较多的测试订单
        test_orders = [{'order_id': f'batch_order_{i:03d}'} for i in range(10)]
        
        print(f"📦 开始批处理 {len(test_orders)} 个订单")
        start_time = time.time()
        
        # 执行批处理
        results = await batch_processor.process_in_batches(
            test_orders,
            concurrent_processor,
            mock_api_call
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✅ 批处理完成")
        print(f"   总耗时: {processing_time:.2f}秒")
        print(f"   处理结果: {len(results)}个")
        
        # 验证所有订单都被处理
        processed_orders = [r.get('order_id') for r in results if isinstance(r, dict)]
        print(f"   成功处理: {len(processed_orders)}个订单")
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理器测试失败: {str(e)}")
        return False


async def test_cache_manager():
    """测试缓存管理器"""
    print("\n🧪 测试4: 缓存管理器")
    
    try:
        # 创建缓存和管理器
        cache = OrderDetailCache(cache_duration_minutes=0.1, max_cache_size=50)  # 6秒过期
        manager = CacheManager(cache, auto_cleanup_interval=3)  # 3秒自动清理
        
        # 设置一些测试数据
        test_data = [
            ("mgr_order_001", "user_001", {"data": "test1"}),
            ("mgr_order_002", "user_001", {"data": "test2"}),
            ("mgr_order_003", "user_002", {"data": "test3"})
        ]
        
        for order_id, user_id, data in test_data:
            cache.set(order_id, user_id, data)
        
        print(f"✅ 设置了 {len(test_data)} 个缓存条目")
        
        # 测试自动清理功能
        print("⏱️ 等待自动清理...")
        await asyncio.sleep(4)  # 等待超过清理间隔
        
        # 尝试获取数据，这会触发自动清理
        result = await manager.get_with_auto_cleanup("mgr_order_001", "user_001")
        
        # 生成缓存报告
        report = manager.generate_cache_report()
        print("📊 缓存报告:")
        print(report)
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存管理器测试失败: {str(e)}")
        return False


async def test_performance_comparison():
    """性能对比测试"""
    print("\n🧪 测试5: 性能对比（串行 vs 并发）")
    
    try:
        # 模拟API调用
        async def mock_slow_api(order_data, *args, **kwargs):
            await asyncio.sleep(0.2)  # 模拟200ms的API延迟
            return {'order_id': order_data.get('order_id'), 'processed': True}
        
        test_orders = [{'order_id': f'perf_order_{i:03d}'} for i in range(8)]
        
        # 串行处理测试
        print("🐌 串行处理测试...")
        start_time = time.time()
        
        serial_results = []
        for order in test_orders:
            result = await mock_slow_api(order)
            serial_results.append(result)
        
        serial_time = time.time() - start_time
        
        # 并发处理测试
        print("🚀 并发处理测试...")
        processor = ConcurrentOrderProcessor(max_concurrent=3, request_interval=0.1)
        
        start_time = time.time()
        concurrent_results = await processor.process_orders_concurrent(
            test_orders,
            mock_slow_api
        )
        concurrent_time = time.time() - start_time
        
        # 性能对比
        speedup = serial_time / concurrent_time
        print(f"📊 性能对比结果:")
        print(f"   串行处理: {serial_time:.2f}秒")
        print(f"   并发处理: {concurrent_time:.2f}秒")
        print(f"   性能提升: {speedup:.1f}倍")
        
        if speedup > 1.5:
            print("✅ 并发处理显著提升性能")
        else:
            print("⚠️ 并发处理性能提升有限")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始并发处理和缓存功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 执行所有测试
    test_results.append(await test_order_cache())
    test_results.append(await test_concurrent_processor())
    test_results.append(await test_batch_processor())
    test_results.append(await test_cache_manager())
    test_results.append(await test_performance_comparison())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 60)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！并发处理和缓存功能正常")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行异常: {str(e)}")
        sys.exit(1)
