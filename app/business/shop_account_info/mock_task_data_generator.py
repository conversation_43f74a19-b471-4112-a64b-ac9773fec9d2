"""
模拟任务数据生成器

用于生成测试用的任务数据，包括：
1. account_trade_detail表的测试记录
2. 各种测试场景的任务数据
3. 数据库插入和清理脚本

使用方式：
    python mock_task_data_generator.py
"""

import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


class MockTaskDataGenerator:
    """模拟任务数据生成器"""
    
    def __init__(self):
        self.output_dir = "debug_data/task_data"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 测试用的基础数据
        self.test_base_time = datetime.now()
        self.test_skus = [
            "*************",  # 主要测试SKU
            "*************",  # 第二个测试SKU
            "*************",  # 第三个测试SKU
            "*************",  # 第四个测试SKU
            "*************"   # 第五个测试SKU
        ]
        
        self.test_source_order_nos = [
            "FXPK250524509609",  # 主要测试包裹号
            "FXPK250524509610",  # 第二个测试包裹号
            "FXPK250524509611",  # 第三个测试包裹号
            "NOTFOUND123456",    # 不存在的包裹号（用于测试未找到的情况）
            "TESTORDER789"       # 另一个测试包裹号
        ]
        
    def generate_all_task_data(self):
        """生成所有任务数据"""
        print("🔧 开始生成模拟任务数据")
        
        # 生成数据库测试记录
        self._generate_database_test_records()
        
        # 生成各种测试场景的任务数据
        self._generate_scenario_task_data()
        
        # 生成批量测试数据
        self._generate_batch_test_data()
        
        # 生成数据库操作脚本
        self._generate_database_scripts()
        
        print("✅ 所有任务数据生成完成")
        
    def _generate_database_test_records(self):
        """生成数据库测试记录"""
        print("🗄️ 生成数据库测试记录")
        
        test_records = []
        
        # 场景1: 正常匹配的记录
        for i, (sku, source_order_no) in enumerate(zip(self.test_skus[:3], self.test_source_order_nos[:3])):
            creation_time = self.test_base_time - timedelta(hours=i*2)
            record = {
                "id": f"test_task_{i+1:03d}",
                "system_sku": sku,
                "source_order_no": source_order_no,
                "creation_time": creation_time.strftime("%Y-%m-%d %H:%M:%S"),
                "flow_status": 0,  # 待处理
                "execution_log": "待处理 - 测试数据",
                "order_no": None,  # 待填充
                "shop": None,      # 待填充
                "update_time": creation_time.strftime("%Y-%m-%d %H:%M:%S"),
                "scenario": "正常匹配测试"
            }
            test_records.append(record)
        
        # 场景2: 不匹配的记录
        for i, sku in enumerate(self.test_skus[3:4]):
            creation_time = self.test_base_time - timedelta(hours=(i+3)*2)
            record = {
                "id": f"test_task_{i+4:03d}",
                "system_sku": sku,
                "source_order_no": self.test_source_order_nos[3],  # NOTFOUND123456
                "creation_time": creation_time.strftime("%Y-%m-%d %H:%M:%S"),
                "flow_status": 0,
                "execution_log": "待处理 - 测试数据（预期不匹配）",
                "order_no": None,
                "shop": None,
                "update_time": creation_time.strftime("%Y-%m-%d %H:%M:%S"),
                "scenario": "不匹配测试"
            }
            test_records.append(record)
        
        # 场景3: 边界情况测试
        edge_cases = [
            {
                "id": "test_task_005",
                "system_sku": self.test_skus[4],
                "source_order_no": "",  # 空的source_order_no
                "scenario": "空包裹号测试"
            },
            {
                "id": "test_task_006", 
                "system_sku": "",  # 空的system_sku
                "source_order_no": self.test_source_order_nos[4],
                "scenario": "空SKU测试"
            }
        ]
        
        for i, edge_case in enumerate(edge_cases):
            creation_time = self.test_base_time - timedelta(hours=(i+5)*2)
            record = {
                **edge_case,
                "creation_time": creation_time.strftime("%Y-%m-%d %H:%M:%S"),
                "flow_status": 0,
                "execution_log": f"待处理 - {edge_case['scenario']}",
                "order_no": None,
                "shop": None,
                "update_time": creation_time.strftime("%Y-%m-%d %H:%M:%S")
            }
            test_records.append(record)
        
        # 保存测试记录
        self._save_json("database_test_records.json", test_records)
        
    def _generate_scenario_task_data(self):
        """生成各种测试场景的任务数据"""
        print("🎭 生成测试场景任务数据")
        
        scenarios = {
            "scenario_1_normal_flow": {
                "description": "正常流程测试 - 找到匹配的sourceOrderNo",
                "tasks": [
                    {
                        "accountTradeDetailId": "test_task_001",
                        "systemSku": "*************",
                        "sourceOrderNo": "FXPK250524509609",
                        "creationTime": (self.test_base_time - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
                        "expected_result": "找到匹配"
                    }
                ]
            },
            
            "scenario_2_no_match": {
                "description": "无匹配测试 - 未找到匹配的sourceOrderNo",
                "tasks": [
                    {
                        "accountTradeDetailId": "test_task_004",
                        "systemSku": "*************",
                        "sourceOrderNo": "NOTFOUND123456",
                        "creationTime": (self.test_base_time - timedelta(hours=6)).strftime("%Y-%m-%d %H:%M:%S"),
                        "expected_result": "未找到匹配"
                    }
                ]
            },
            
            "scenario_3_auth_failure": {
                "description": "登录失效测试 - 模拟认证失败后重新认证",
                "tasks": [
                    {
                        "accountTradeDetailId": "test_task_002",
                        "systemSku": "*************",
                        "sourceOrderNo": "FXPK250524509610",
                        "creationTime": (self.test_base_time - timedelta(hours=3)).strftime("%Y-%m-%d %H:%M:%S"),
                        "expected_result": "认证失败后重试成功",
                        "simulate_auth_failure": True
                    }
                ]
            },
            
            "scenario_4_batch_processing": {
                "description": "批量处理测试 - 多个任务批量处理",
                "tasks": [
                    {
                        "accountTradeDetailId": "test_task_001",
                        "systemSku": "*************",
                        "sourceOrderNo": "FXPK250524509609",
                        "creationTime": (self.test_base_time - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S")
                    },
                    {
                        "accountTradeDetailId": "test_task_002",
                        "systemSku": "*************", 
                        "sourceOrderNo": "FXPK250524509610",
                        "creationTime": (self.test_base_time - timedelta(hours=3)).strftime("%Y-%m-%d %H:%M:%S")
                    },
                    {
                        "accountTradeDetailId": "test_task_003",
                        "systemSku": "*************",
                        "sourceOrderNo": "FXPK250524509611",
                        "creationTime": (self.test_base_time - timedelta(hours=5)).strftime("%Y-%m-%d %H:%M:%S")
                    }
                ]
            },
            
            "scenario_5_edge_cases": {
                "description": "边界情况测试 - 各种异常输入",
                "tasks": [
                    {
                        "accountTradeDetailId": "test_task_005",
                        "systemSku": "*************",
                        "sourceOrderNo": "",  # 空包裹号
                        "creationTime": (self.test_base_time - timedelta(hours=9)).strftime("%Y-%m-%d %H:%M:%S"),
                        "expected_result": "处理空包裹号"
                    },
                    {
                        "accountTradeDetailId": "test_task_006",
                        "systemSku": "",  # 空SKU
                        "sourceOrderNo": "TESTORDER789",
                        "creationTime": (self.test_base_time - timedelta(hours=11)).strftime("%Y-%m-%d %H:%M:%S"),
                        "expected_result": "处理空SKU"
                    }
                ]
            }
        }
        
        # 保存各个场景的任务数据
        for scenario_name, scenario_data in scenarios.items():
            self._save_json(f"{scenario_name}.json", scenario_data)
            
    def _generate_batch_test_data(self):
        """生成批量测试数据"""
        print("📦 生成批量测试数据")
        
        # 小批量测试（3个任务）
        small_batch = {
            "description": "小批量测试 - 3个任务",
            "batch_size": 3,
            "tasks": []
        }
        
        for i in range(3):
            task = {
                "accountTradeDetailId": f"batch_small_{i+1:03d}",
                "systemSku": self.test_skus[i],
                "sourceOrderNo": self.test_source_order_nos[i],
                "creationTime": (self.test_base_time - timedelta(hours=i*2)).strftime("%Y-%m-%d %H:%M:%S")
            }
            small_batch["tasks"].append(task)
            
        # 中等批量测试（10个任务）
        medium_batch = {
            "description": "中等批量测试 - 10个任务",
            "batch_size": 10,
            "tasks": []
        }
        
        for i in range(10):
            sku_index = i % len(self.test_skus)
            order_index = i % len(self.test_source_order_nos)
            task = {
                "accountTradeDetailId": f"batch_medium_{i+1:03d}",
                "systemSku": self.test_skus[sku_index],
                "sourceOrderNo": self.test_source_order_nos[order_index],
                "creationTime": (self.test_base_time - timedelta(hours=i)).strftime("%Y-%m-%d %H:%M:%S")
            }
            medium_batch["tasks"].append(task)
            
        # 保存批量测试数据
        self._save_json("batch_test_small.json", small_batch)
        self._save_json("batch_test_medium.json", medium_batch)
        
    def _generate_database_scripts(self):
        """生成数据库操作脚本"""
        print("💾 生成数据库操作脚本")
        
        # 读取测试记录
        with open(f"{self.output_dir}/database_test_records.json", "r", encoding="utf-8") as f:
            test_records = json.load(f)
        
        # 生成INSERT脚本
        insert_script = "-- 插入测试数据\n"
        insert_script += "-- 用于店铺账户信息API增强功能测试\n\n"
        
        insert_values = []
        for record in test_records:
            values = f"('{record['id']}', '{record['system_sku']}', '{record['source_order_no']}', " \
                    f"'{record['creation_time']}', {record['flow_status']}, '{record['execution_log']}', " \
                    f"NULL, NULL, '{record['update_time']}')"
            insert_values.append(values)
        
        insert_script += "INSERT INTO account_trade_detail (\n"
        insert_script += "    id, system_sku, source_order_no, creation_time, flow_status, \n"
        insert_script += "    execution_log, order_no, shop, update_time\n"
        insert_script += ") VALUES \n"
        insert_script += ",\n".join(insert_values) + ";\n\n"
        
        # 生成查询脚本
        query_script = "-- 查询测试数据\n"
        query_script += "SELECT \n"
        query_script += "    id, system_sku, source_order_no, creation_time, \n"
        query_script += "    flow_status, execution_log, order_no, shop\n"
        query_script += "FROM account_trade_detail \n"
        query_script += "WHERE id LIKE 'test_task_%' OR id LIKE 'batch_%'\n"
        query_script += "ORDER BY creation_time DESC;\n\n"
        
        # 生成清理脚本
        cleanup_script = "-- 清理测试数据\n"
        cleanup_script += "DELETE FROM account_trade_detail \n"
        cleanup_script += "WHERE id LIKE 'test_task_%' OR id LIKE 'batch_%';\n\n"
        
        # 生成更新脚本（模拟处理结果）
        update_script = "-- 更新测试数据（模拟处理结果）\n"
        update_script += "-- 模拟找到匹配的情况\n"
        update_script += "UPDATE account_trade_detail SET \n"
        update_script += "    order_no = 'FXAM250524015033',\n"
        update_script += "    shop = 'ICE CATCA(ICE CATCA)',\n"
        update_script += "    flow_status = 1,\n"
        update_script += "    execution_log = '✅ 处理成功：找到匹配的sourceOrderNo'\n"
        update_script += "WHERE id IN ('test_task_001', 'test_task_002', 'test_task_003');\n\n"
        
        update_script += "-- 模拟未找到匹配的情况\n"
        update_script += "UPDATE account_trade_detail SET \n"
        update_script += "    flow_status = 2,\n"
        update_script += "    execution_log = '❌ 处理完成：未找到匹配的sourceOrderNo'\n"
        update_script += "WHERE id = 'test_task_004';\n\n"
        
        # 合并所有脚本
        full_script = insert_script + query_script + update_script + cleanup_script
        
        # 保存脚本文件
        with open(f"{self.output_dir}/database_operations.sql", "w", encoding="utf-8") as f:
            f.write(full_script)
            
        print("  ✅ 已生成: database_operations.sql")
        
    def _save_json(self, filename: str, data: Any):
        """保存JSON数据到文件"""
        filepath = f"{self.output_dir}/{filename}"
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"  ✅ 已生成: {filename}")
        
    def generate_test_config(self):
        """生成测试配置文件"""
        print("⚙️ 生成测试配置文件")
        
        test_config = {
            "test_environment": {
                "debugMode": True,
                "batchProcessingEnabled": True,
                "maxBatchSize": 10,
                "timeWindowHours": 24,
                "use_real_api": True,  # 使用真实API而不是模拟响应
                "log_level": "DEBUG"
            },
            "test_data_info": {
                "base_time": self.test_base_time.isoformat(),
                "test_skus": self.test_skus,
                "test_source_order_nos": self.test_source_order_nos,
                "total_test_records": 6,
                "scenarios": [
                    "正常匹配测试",
                    "不匹配测试", 
                    "空包裹号测试",
                    "空SKU测试",
                    "批量处理测试",
                    "认证失败测试"
                ]
            },
            "database_config": {
                "test_table": "account_trade_detail",
                "test_id_prefix": "test_task_",
                "batch_id_prefix": "batch_",
                "cleanup_on_start": True,
                "backup_before_test": False
            },
            "api_config": {
                "use_real_endpoints": True,
                "timeout_seconds": 60,
                "retry_count": 3,
                "simulate_auth_failure": False  # 可以设置为True来测试认证失败场景
            }
        }
        
        self._save_json("test_config.json", test_config)
        
    def print_usage_guide(self):
        """打印使用指南"""
        print("\n" + "=" * 80)
        print("📋 任务数据使用指南")
        print("=" * 80)
        
        print("\n🗄️ 数据库准备:")
        print("1. 执行SQL脚本插入测试数据:")
        print("   mysql -u username -p database_name < debug_data/task_data/database_operations.sql")
        
        print("\n🧪 运行测试:")
        print("1. 使用真实API测试:")
        print("   python app/business/shop_account_info/shop_account_processor_api.py")
        
        print("2. 指定测试场景:")
        print("   # 正常流程测试")
        print("   python -c \"import json; print(json.load(open('debug_data/task_data/scenario_1_normal_flow.json')))\"")
        
        print("\n📊 验证结果:")
        print("1. 查询处理结果:")
        print("   SELECT * FROM account_trade_detail WHERE id LIKE 'test_task_%';")
        
        print("2. 检查日志文件:")
        print("   tail -f logs/shop_account_info_*.log")
        
        print("\n🧹 清理数据:")
        print("1. 删除测试数据:")
        print("   DELETE FROM account_trade_detail WHERE id LIKE 'test_task_%';")


def main():
    """主函数"""
    generator = MockTaskDataGenerator()
    
    print("🚀 开始生成店铺账户信息API测试任务数据")
    print("=" * 80)
    
    generator.generate_all_task_data()
    generator.generate_test_config()
    generator.print_usage_guide()
    
    print(f"\n🎉 任务数据生成完成！")
    print(f"📁 数据保存位置: {generator.output_dir}")
    print("\n📋 生成的文件列表:")
    
    # 列出生成的文件
    for root, dirs, files in os.walk(generator.output_dir):
        for file in files:
            print(f"  - {file}")


if __name__ == "__main__":
    main()
