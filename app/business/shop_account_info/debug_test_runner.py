"""
店铺账户信息API增强功能 - Debug测试运行器

用于测试新增强的功能：
1. 正确解析订单详情API响应中的操作日志数据结构
2. 检测登录失效并自动重新认证
3. 验证系统的健壮性和用户体验

使用方式：
    python debug_test_runner.py
"""

import asyncio
import logging
import os
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from app.business.shop_account_info.shop_account_processor_api import AsyncShopAccountInfoAPIProcessor
from app.business.shop_account_info.shop_account_api_client import ShopAccountApiClient
from app.utils.logger import get_rpa_logger


class DebugTestRunner:
    """Debug测试运行器"""
    
    def __init__(self):
        self.logger = get_rpa_logger("shop_account_info", "debug_test")
        self.test_results = []
        
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始店铺账户信息API增强功能测试")
        print("=" * 80)
        
        # 设置debug环境
        self._setup_debug_environment()
        
        # 执行测试
        await self._test_order_detail_parsing()
        await self._test_auth_failure_detection()
        await self._test_auto_reauth_mechanism()
        await self._test_enhanced_error_handling()
        await self._test_full_api_processor()
        
        # 输出测试结果
        self._print_test_summary()
        
    def _setup_debug_environment(self):
        """设置debug测试环境"""
        print("🔧 设置debug测试环境")
        
        # 设置环境变量
        os.environ['debugMode'] = 'true'
        os.environ['batchProcessingEnabled'] = 'true'
        os.environ['maxBatchSize'] = '10'
        
        # 创建测试目录
        os.makedirs("logs", exist_ok=True)
        os.makedirs("debug_data", exist_ok=True)
        
        print("✅ Debug环境设置完成")
        
    async def _test_order_detail_parsing(self):
        """测试订单详情解析逻辑"""
        print("\n🧪 测试1: 订单详情解析逻辑")
        
        try:
            processor = AsyncShopAccountInfoAPIProcessor(task_id="debug_test_001")
            
            # 创建模拟的订单详情API响应
            mock_response = {
                "code": 200,
                "data": {
                    "datas": {
                        "data_list": {
                            "operate_log_list": {
                                "operateLogList": [
                                    {
                                        "id": 1,
                                        "content": "订单创建，包裹号：FXPK250524509609",
                                        "create_time": "2025-05-25 10:00:00",
                                        "operator": "system"
                                    },
                                    {
                                        "id": 2,
                                        "content": "订单处理中，其他信息",
                                        "create_time": "2025-05-25 10:05:00",
                                        "operator": "user"
                                    },
                                    {
                                        "id": 3,
                                        "content": "包裹发货，包裹号：FXPK250524509609，发货完成",
                                        "create_time": "2025-05-25 10:10:00",
                                        "operator": "system"
                                    }
                                ]
                            }
                        }
                    }
                }
            }
            
            # 测试正确匹配
            result = await processor._check_source_order_no_in_detail(mock_response, "FXPK250524509609")
            assert result == True, "应该找到匹配的sourceOrderNo"
            
            # 测试不匹配
            result = await processor._check_source_order_no_in_detail(mock_response, "NOTFOUND123")
            assert result == False, "不应该找到不存在的sourceOrderNo"
            
            # 测试备用搜索机制
            mock_response_no_logs = {"code": 200, "data": {"other_field": "FXPK250524509609"}}
            result = await processor._check_source_order_no_in_detail(mock_response_no_logs, "FXPK250524509609")
            assert result == True, "备用搜索应该找到匹配"
            
            print("✅ 订单详情解析逻辑测试通过")
            self.test_results.append(("订单详情解析", True))
            
        except Exception as e:
            print(f"❌ 订单详情解析逻辑测试失败: {str(e)}")
            self.test_results.append(("订单详情解析", False))
            
    async def _test_auth_failure_detection(self):
        """测试登录失效检测机制"""
        print("\n🧪 测试2: 登录失效检测机制")
        
        try:
            api_client = ShopAccountApiClient(
                logger=self.logger,
                business_type="shop_account_info",
                script_name="debug_test"
            )
            
            # 测试各种登录失效响应
            test_cases = [
                {
                    "name": "errorCode E4002",
                    "response": {"status": 0, "errorCode": "E4002", "errorMess": "登录状态失效，请重新登录"},
                    "expected": True
                },
                {
                    "name": "http_status_code 401",
                    "response": {"status": 0, "http_status_code": 401, "msg": "未授权"},
                    "expected": True
                },
                {
                    "name": "错误信息包含关键词",
                    "response": {"status": 0, "errorMess": "登录状态失效", "msg": "请重新登录"},
                    "expected": True
                },
                {
                    "name": "正常响应",
                    "response": {"status": 1, "code": 200, "data": {}},
                    "expected": False
                }
            ]
            
            for case in test_cases:
                result = api_client._is_auth_failed_response(case["response"])
                assert result == case["expected"], f"测试用例 '{case['name']}' 失败"
                print(f"  ✅ {case['name']}: {result}")
            
            print("✅ 登录失效检测机制测试通过")
            self.test_results.append(("登录失效检测", True))
            
        except Exception as e:
            print(f"❌ 登录失效检测机制测试失败: {str(e)}")
            self.test_results.append(("登录失效检测", False))
            
    async def _test_auto_reauth_mechanism(self):
        """测试自动重新认证机制"""
        print("\n🧪 测试3: 自动重新认证机制")
        
        try:
            api_client = ShopAccountApiClient(
                logger=self.logger,
                business_type="shop_account_info",
                script_name="debug_test"
            )
            
            # 测试认证错误识别
            auth_error = Exception("AUTH_FAILED: 登录状态失效，需要重新认证")
            result = api_client._is_auth_error(auth_error)
            assert result == True, "应该识别为认证错误"
            
            # 测试非认证错误
            normal_error = Exception("网络连接失败")
            result = api_client._is_auth_error(normal_error)
            assert result == False, "不应该识别为认证错误"
            
            print("✅ 自动重新认证机制测试通过")
            self.test_results.append(("自动重新认证", True))
            
        except Exception as e:
            print(f"❌ 自动重新认证机制测试失败: {str(e)}")
            self.test_results.append(("自动重新认证", False))
            
    async def _test_enhanced_error_handling(self):
        """测试增强的错误处理"""
        print("\n🧪 测试4: 增强的错误处理")
        
        try:
            processor = AsyncShopAccountInfoAPIProcessor(task_id="debug_test_004")
            
            # 测试安全的字典访问
            invalid_response = {"invalid": "structure"}
            result = await processor._check_source_order_no_in_detail(invalid_response, "TEST123")
            # 应该返回False而不是抛出异常
            assert result == False, "无效结构应该返回False"
            
            # 测试备用搜索
            result = processor._fallback_search_in_response({"data": "TEST123"}, "TEST123")
            assert result == True, "备用搜索应该找到匹配"
            
            print("✅ 增强的错误处理测试通过")
            self.test_results.append(("错误处理", True))
            
        except Exception as e:
            print(f"❌ 增强的错误处理测试失败: {str(e)}")
            self.test_results.append(("错误处理", False))
            
    async def _test_full_api_processor(self):
        """测试完整的API处理器"""
        print("\n🧪 测试5: 完整的API处理器")
        
        try:
            processor = AsyncShopAccountInfoAPIProcessor(task_id="debug_test_005")
            
            # 测试初始化
            assert processor.business_type == "shop_account_info"
            assert processor.script_name == "shop_account_processor_api"
            assert hasattr(processor, 'api_client')
            
            # 测试批量任务获取（debug模式）
            batch_tasks = processor._get_batch_tasks()
            assert len(batch_tasks) == 2, "Debug模式应该返回2个测试任务"
            assert batch_tasks[0]['systemSku'] == "*************"
            assert batch_tasks[0]['sourceOrderNo'] == "FXPK250524509609"
            
            # 测试任务验证
            valid_tasks = await processor._validate_batch_tasks(batch_tasks)
            assert len(valid_tasks) == 2, "所有测试任务都应该有效"
            
            print("✅ 完整的API处理器测试通过")
            self.test_results.append(("API处理器", True))
            
        except Exception as e:
            print(f"❌ 完整的API处理器测试失败: {str(e)}")
            self.test_results.append(("API处理器", False))
            
    def _print_test_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("🎯 测试总结")
        print("=" * 80)
        
        passed = sum(1 for _, result in self.test_results if result)
        total = len(self.test_results)
        
        for test_name, result in self.test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！API增强功能正常工作")
        else:
            print("⚠️ 部分测试失败，需要进一步调试")
            
        # 保存测试结果
        self._save_test_results()
        
    def _save_test_results(self):
        """保存测试结果到文件"""
        try:
            test_report = {
                "timestamp": datetime.now().isoformat(),
                "test_type": "api_enhancement_debug",
                "total_tests": len(self.test_results),
                "passed_tests": sum(1 for _, result in self.test_results if result),
                "results": [
                    {"test_name": name, "passed": result}
                    for name, result in self.test_results
                ],
                "environment": {
                    "debugMode": os.environ.get('debugMode'),
                    "batchProcessingEnabled": os.environ.get('batchProcessingEnabled')
                }
            }
            
            with open("debug_data/test_results.json", "w", encoding="utf-8") as f:
                json.dump(test_report, f, ensure_ascii=False, indent=2)
                
            print(f"\n📄 测试结果已保存到: debug_data/test_results.json")
            
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {str(e)}")


async def main():
    """主函数"""
    runner = DebugTestRunner()
    await runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
