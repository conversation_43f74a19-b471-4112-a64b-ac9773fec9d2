# 店铺账户信息API测试任务数据使用指南

## 📋 概述

本指南说明如何使用生成的测试任务数据来测试店铺账户信息API增强功能。与之前的模拟响应测试不同，这里使用**真实的API响应**和**模拟的任务数据**。

## 🎯 测试目标

验证以下增强功能：
1. **正确解析订单详情API响应结构** - 使用真实API响应
2. **在操作日志content中搜索sourceOrderNo** - 验证实际匹配逻辑
3. **检测登录失效并自动重新认证** - 测试真实认证流程
4. **处理各种边界情况** - 空值、异常数据等

## 📁 生成的测试数据

### 数据库测试记录
- **文件**: `debug_data/task_data/database_test_records.json`
- **内容**: 6条测试记录，包含不同场景
- **用途**: 插入到account_trade_detail表作为待处理任务

### 测试场景数据
1. **scenario_1_normal_flow.json** - 正常流程测试
2. **scenario_2_no_match.json** - 无匹配测试
3. **scenario_3_auth_failure.json** - 认证失败测试
4. **scenario_4_batch_processing.json** - 批量处理测试
5. **scenario_5_edge_cases.json** - 边界情况测试

### 批量测试数据
- **batch_test_small.json** - 小批量测试（3个任务）
- **batch_test_medium.json** - 中等批量测试（10个任务）

### 数据库操作脚本
- **database_operations.sql** - 完整的数据库操作脚本

## 🚀 快速开始

### 步骤1: 准备数据库
```sql
-- 插入测试数据
mysql -u your_username -p your_database < debug_data/task_data/database_operations.sql
```

### 步骤2: 验证数据插入
```sql
SELECT id, system_sku, source_order_no, creation_time, scenario 
FROM account_trade_detail 
WHERE id LIKE 'test_task_%' 
ORDER BY creation_time DESC;
```

应该看到6条测试记录：
- test_task_001: ************* -> FXPK250524509609 (正常匹配)
- test_task_002: ************* -> FXPK250524509610 (正常匹配)
- test_task_003: ************* -> FXPK250524509611 (正常匹配)
- test_task_004: ************* -> NOTFOUND123456 (预期不匹配)
- test_task_005: ************* -> '' (空包裹号)
- test_task_006: '' -> TESTORDER789 (空SKU)

### 步骤3: 运行真实API测试
```bash
# 运行所有场景测试
python app/business/shop_account_info/real_api_test_runner.py

# 运行指定场景
python app/business/shop_account_info/real_api_test_runner.py --scenario scenario_1_normal_flow

# 查看使用指南
python app/business/shop_account_info/real_api_test_runner.py --help-guide
```

### 步骤4: 运行实际的API处理器
```bash
# 运行店铺账户信息API处理器（使用真实API）
python app/business/shop_account_info/shop_account_processor_api.py
```

## 📊 测试数据详情

### 测试用的SKU和包裹号
```json
{
  "test_skus": [
    "*************",  // 主要测试SKU
    "*************",  // 第二个测试SKU
    "*************",  // 第三个测试SKU
    "*************",  // 第四个测试SKU
    "*************"   // 第五个测试SKU
  ],
  "test_source_order_nos": [
    "FXPK250524509609",  // 主要测试包裹号
    "FXPK250524509610",  // 第二个测试包裹号
    "FXPK250524509611",  // 第三个测试包裹号
    "NOTFOUND123456",    // 不存在的包裹号
    "TESTORDER789"       // 另一个测试包裹号
  ]
}
```

### 测试场景说明

#### 场景1: 正常流程测试
- **目标**: 验证能够找到匹配的sourceOrderNo
- **数据**: test_task_001 (************* -> FXPK250524509609)
- **预期**: 在真实API响应的操作日志中找到匹配

#### 场景2: 无匹配测试
- **目标**: 验证正确处理未找到匹配的情况
- **数据**: test_task_004 (************* -> NOTFOUND123456)
- **预期**: 在操作日志中找不到匹配，返回未找到

#### 场景3: 认证失败测试
- **目标**: 验证登录失效检测和自动重新认证
- **数据**: test_task_002 (************* -> FXPK250524509610)
- **预期**: 模拟认证失败，然后自动重新认证并重试

#### 场景4: 批量处理测试
- **目标**: 验证批量任务处理能力
- **数据**: test_task_001, test_task_002, test_task_003
- **预期**: 批量处理多个任务，每个任务独立处理

#### 场景5: 边界情况测试
- **目标**: 验证异常数据的处理
- **数据**: 空SKU、空包裹号等
- **预期**: 正确处理异常情况，不崩溃

## 🔍 验证测试结果

### 查看处理结果
```sql
-- 查看所有测试任务的处理结果
SELECT 
    id, 
    system_sku, 
    source_order_no, 
    order_no, 
    shop, 
    flow_status, 
    execution_log,
    update_time
FROM account_trade_detail 
WHERE id LIKE 'test_task_%' 
ORDER BY update_time DESC;
```

### 预期的处理结果
- **flow_status = 1**: 处理成功，找到匹配
- **flow_status = 2**: 处理完成，未找到匹配
- **flow_status = 0**: 待处理或处理失败

### 查看日志文件
```bash
# 查看最新的日志
tail -f logs/shop_account_info_*.log

# 搜索特定的日志
grep "sourceOrderNo" logs/shop_account_info_*.log
grep "找到匹配" logs/shop_account_info_*.log
```

## 🧪 高级测试

### 测试登录失效场景
1. 修改认证信息使其失效
2. 运行测试，观察是否检测到登录失效
3. 验证是否自动重新认证

### 测试API响应解析
1. 运行正常流程测试
2. 检查日志中的解析过程
3. 验证是否正确解析了 `data.datas.data_list.operate_log_list.operateLogList`

### 测试备用搜索机制
1. 使用异常的API响应结构
2. 验证备用搜索是否正常工作

## 🧹 清理测试数据

### 清理数据库测试数据
```sql
-- 删除所有测试数据
DELETE FROM account_trade_detail 
WHERE id LIKE 'test_task_%' OR id LIKE 'batch_%';

-- 验证清理结果
SELECT COUNT(*) FROM account_trade_detail 
WHERE id LIKE 'test_task_%' OR id LIKE 'batch_%';
```

### 清理日志文件
```bash
# 清理测试日志（可选）
rm logs/shop_account_info_*_real_api_test.log
```

## 📝 注意事项

1. **真实API调用**: 测试会产生真实的API调用，确保网络连接正常
2. **认证信息**: 确保环境变量中的认证信息有效
3. **数据库连接**: 确保数据库连接配置正确
4. **测试环境**: 建议在测试环境而非生产环境运行
5. **API限制**: 注意API调用频率限制，避免被限流

## 🆘 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库配置
   echo $DB_HOST $DB_PORT $DB_DATABASE $DB_USERNAME
   ```

2. **认证失败**
   ```bash
   # 检查认证环境变量
   echo $YIMAI_USERNAME $YIMAI_PASSWORD
   ```

3. **API调用失败**
   - 检查网络连接
   - 验证API端点是否可访问
   - 检查认证信息是否有效

4. **测试数据未找到**
   ```bash
   # 重新生成测试数据
   python app/business/shop_account_info/mock_task_data_generator.py
   ```

## 📞 支持

如有问题，请检查：
1. 日志文件中的详细错误信息
2. 数据库中的任务状态
3. 网络连接和API可访问性
4. 认证信息的有效性

---

**最后更新**: 2025-01-25  
**版本**: 1.0.0  
**状态**: ✅ 可用
