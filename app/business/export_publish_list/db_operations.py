"""
刊登成功列表数据库操作器

专门处理publish_success_list表的数据操作：
1. 批量查询已存在记录
2. 批量新增记录
3. 批量更新记录
4. 事务安全保障
"""

import logging
from typing import List, Dict, Any, Tuple
from datetime import datetime

from app.core.database import DatabaseManager


class PublishSuccessDBOperator:
    """刊登成功列表数据库操作器"""
    
    def __init__(self, business_type: str = "export_publish_list", 
                 script_name: str = "csv_import", logger: logging.Logger = None):
        """
        初始化数据库操作器
        
        Args:
            business_type: 业务类型
            script_name: 脚本名称
            logger: 日志记录器
        """
        self.business_type = business_type
        self.script_name = script_name
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager(business_type, script_name)
        
        # 表名
        self.table_name = "publish_success_list"
        
        # 字段列表（排除auto_increment的id字段）
        self.insert_fields = [
            'platform', 'source', 'sku', 'spu', 'seller_sku_parent', 'seller_sku_child',
            'asin', 'upc', 'ean', 'brand', 'part_number', 'variant_type',
            'variant_name_1', 'variant_name_2', 'variant_name_3', 'price_local_currency',
            'quantity', 'account', 'site', 'warehouse', 'node_id', 'platform_category_path',
            'product_line', 'product_type', 'chinese_title', 'sales_person',
            'platform_return_message', 'link_online_status', 'creator', 'create_time',
            'last_fetch_time', 'sync_time'
        ]
        
        # 更新字段（排除唯一索引字段和ID）
        self.update_fields = [field for field in self.insert_fields 
                             if field not in ['seller_sku_child', 'account']]
        
        self.logger.info("数据库操作器初始化完成", extra_data={
            'table_name': self.table_name,
            'insert_fields_count': len(self.insert_fields),
            'update_fields_count': len(self.update_fields)
        })
    
    def get_existing_records(self, unique_keys: List[str]) -> List[Dict[str, Any]]:
        """
        根据唯一键批量查询已存在的记录（使用临时表JOIN优化）
        
        Args:
            unique_keys: 唯一键列表，格式为 "seller_sku_child#account"
            
        Returns:
            List[Dict[str, Any]]: 已存在的记录列表
        """
        if not unique_keys:
            return []
        
        try:
            self.logger.info(f"查询已存在记录（临时表JOIN优化）", extra_data={
                'unique_keys_count': len(unique_keys),
                'sample_keys': unique_keys[:5]  # 显示前5个样例
            })
            
            # 解析唯一键
            search_data = []
            for key in unique_keys:
                if '#' in key:
                    seller_sku, account = key.split('#', 1)
                    search_data.append((seller_sku, account))
            
            if not search_data:
                self.logger.warning("没有有效的唯一键")
                return []
            
            all_results = []
            batch_size = 10000  # 使用临时表可以增加批次大小
            total_batches = (len(search_data) + batch_size - 1) // batch_size
            
            self.logger.info(f"开始分批查询：总计{total_batches}批次，每批次{batch_size}个键")
            
            import time
            for batch_idx in range(0, len(search_data), batch_size):
                batch_data = search_data[batch_idx:batch_idx + batch_size]
                current_batch_num = batch_idx // batch_size + 1
                
                self.logger.info(f"执行第{current_batch_num}/{total_batches}批次查询，包含{len(batch_data)}个键")
                
                # 创建临时表名（使用时间戳+随机数确保唯一性）
                import random
                temp_table = f"temp_search_{int(time.time() * 1000)}_{random.randint(1000, 9999)}_{current_batch_num}"
                
                try:
                    # 使用事务确保临时表正确创建和清理
                    with self.db_manager.transaction() as conn:
                        cursor = conn.cursor()
                        
                        # 检查数据库连接健康状态
                        conn.ping(reconnect=True)
                        self.logger.info(f"批次{current_batch_num}数据库连接健康检查通过")
                        
                        # 创建临时表
                        create_table_sql = f"""
                            CREATE TEMPORARY TABLE {temp_table} (
                                seller_sku_child VARCHAR(255) NOT NULL,
                                account VARCHAR(255) NOT NULL,
                                PRIMARY KEY (seller_sku_child, account)
                            )
                        """
                        cursor.execute(create_table_sql)
                        
                        self.logger.info(f"临时表 {temp_table} 创建成功")
                        
                        # 批量插入搜索条件到临时表
                        insert_sql = f"INSERT INTO {temp_table} (seller_sku_child, account) VALUES (%s, %s)"
                        
                        # 检查数据有效性
                        valid_data = []
                        invalid_count = 0
                        for item in batch_data:
                            if len(item) == 2 and item[0] and item[1]:
                                valid_data.append(item)
                            else:
                                invalid_count += 1
                                self.logger.warning(f"无效数据跳过: {item}")
                        
                        if invalid_count > 0:
                            self.logger.warning(f"批次{current_batch_num}中有{invalid_count}条无效数据被跳过")
                        
                        if valid_data:
                            # 🔧 调试信息：显示即将插入临时表的数据样例
                            sample_temp_data = valid_data[:3]
                            self.logger.info(f"批次{current_batch_num}临时表插入样例: {sample_temp_data}")
                            
                            cursor.executemany(insert_sql, valid_data)
                            self.logger.info(f"向临时表插入{len(valid_data)}条有效搜索条件")
                        else:
                            self.logger.warning(f"批次{current_batch_num}没有有效数据，跳过处理")
                            continue
                        
                        # 使用INNER JOIN查询（性能优化核心）
                        join_sql = f"""
                            SELECT p.id, p.seller_sku_child, p.account, p.platform, p.source, p.sku, p.spu,
                                   p.seller_sku_parent, p.asin, p.upc, p.ean, p.brand, p.part_number,
                                   p.variant_type, p.variant_name_1, p.variant_name_2, p.variant_name_3,
                                   p.price_local_currency, p.quantity, p.site, p.warehouse, p.node_id,
                                   p.platform_category_path, p.product_line, p.product_type,
                                   p.chinese_title, p.sales_person, p.platform_return_message,
                                   p.link_online_status, p.creator, p.create_time, p.last_fetch_time,
                                   p.sync_time, p.created_at, p.updated_at
                            FROM {self.table_name} p
                            INNER JOIN {temp_table} t ON p.seller_sku_child = t.seller_sku_child 
                                                       AND p.account = t.account
                        """
                        
                        self.logger.info(f"批次{current_batch_num}开始执行JOIN查询")
                        
                        # 执行JOIN查询
                        cursor.execute(join_sql)
                        batch_results = cursor.fetchall()
                        
                        self.logger.info(f"批次{current_batch_num}查询执行完成，原始结果数量: {len(batch_results)}")
                        
                        # 转换为字典格式
                        if batch_results:
                            columns = [desc[0] for desc in cursor.description]
                            
                            # 🔧 调试：检查列名和原始数据
                            self.logger.info(f"🔍 查询列名: {columns[:5]}")
                            if batch_results:
                                raw_row = batch_results[0]
                                self.logger.info(f"🔍 原始行数据类型: {type(raw_row)}")
                                # 检查数据是否已经是字典格式
                                if isinstance(raw_row, dict):
                                    self.logger.info(f"🔍 原始行数据已是字典格式: {dict(list(raw_row.items())[:3])}")
                                else:
                                    self.logger.info(f"🔍 原始行数据: {raw_row}")
                            
                            # 🔧 修复：检查数据类型，避免重复转换
                            if batch_results:
                                if isinstance(batch_results[0], dict):
                                    # 如果已经是字典格式，直接使用
                                    self.logger.info(f"🔍 数据已是字典格式，无需转换")
                                    converted_results = batch_results
                                else:
                                    # 如果是元组格式，进行转换
                                    self.logger.info(f"🔍 数据为元组格式，进行字典转换")
                                    converted_results = [dict(zip(columns, row)) for row in batch_results]
                            else:
                                converted_results = []
                            
                            # 🔧 调试：检查转换后的结果
                            if converted_results:
                                sample_result = converted_results[0]
                                self.logger.info(f"🔍 最终结果样例: seller_sku_child={sample_result.get('seller_sku_child')}, account={sample_result.get('account')}")
                            
                            all_results.extend(converted_results)
                            self.logger.info(f"批次{current_batch_num}成功处理{len(converted_results)}条记录")
                        else:
                            self.logger.info(f"批次{current_batch_num}没有找到匹配的记录")
                        
                        self.logger.info(f"第{current_batch_num}批次查询完成，找到{len(converted_results)}条记录，累计{len(all_results)}条记录")
                        
                        # 临时表会在事务结束时自动清理
                        
                except Exception as e:
                    self.logger.error(f"第{current_batch_num}批次查询失败: {str(e)}")
                    # 🚨 批次失败时抛出异常，避免数据不完整
                    # 不能continue，否则会导致数据截断
                    raise Exception(f"批次{current_batch_num}查询失败，为避免数据不完整，终止处理: {str(e)}")
            
            self.logger.info(f"临时表JOIN查询全部完成，总计找到 {len(all_results)} 条已存在记录")
            
            # 🔧 直接验证：查询数据库中的实际数据
            if all_results:
                # 直接查询数据库中的前几条记录
                verify_sql = f"""
                    SELECT seller_sku_child, account, platform, source, sku 
                    FROM {self.table_name} 
                    WHERE seller_sku_child IN ('seller_sku_child', '10112107889110abeijwg', 'GS2228701beijwg')
                    ORDER BY id DESC 
                    LIMIT 10
                """
                verify_results = self.db_manager.execute_query(verify_sql)
                if verify_results:
                    self.logger.info(f"🔍 数据库验证查询结果: {verify_results[:3]}")
                else:
                    self.logger.info("🔍 数据库验证查询没有找到结果")
            
            # 🔧 调试信息：显示部分已存在记录的唯一键
            if all_results:
                sample_existing_keys = []
                for record in all_results[:5]:  # 显示前5个已存在记录的唯一键
                    # 🔧 调试：检查record的实际内容
                    self.logger.info(f"🔍 记录结构调试: {dict(list(record.items())[:5])}")
                    
                    seller_sku_value = record.get('seller_sku_child', '')
                    account_value = record.get('account', '')
                    
                    # 🔧 调试：检查字段值
                    self.logger.info(f"🔍 字段值调试: seller_sku_child='{seller_sku_value}', account='{account_value}'")
                    
                    key = f"{seller_sku_value}#{account_value}"
                    sample_existing_keys.append(key)
                
                self.logger.info(f"已存在记录样例唯一键: {sample_existing_keys}")
            else:
                self.logger.warning("⚠️ 没有找到任何已存在记录，这可能导致重复数据被标记为新增")
                
                # 🚨 额外验证：如果没找到任何记录，直接查询数据库验证是否真的存在数据
                if unique_keys:
                    sample_keys = unique_keys[:3]  # 取前3个键验证
                    for sample_key in sample_keys:
                        if '#' in sample_key:
                            seller_sku, account = sample_key.split('#', 1)
                            verify_sql = f"""
                                SELECT COUNT(*) as count, seller_sku_child, account 
                                FROM {self.table_name} 
                                WHERE seller_sku_child = %s AND account = %s
                            """
                            verify_result = self.db_manager.execute_query(verify_sql, (seller_sku, account))
                            if verify_result and verify_result[0]['count'] > 0:
                                self.logger.error(f"🚨 验证失败！数据库中确实存在记录 {sample_key}，但临时表JOIN查询没有找到")
                            else:
                                self.logger.info(f"✅ 验证通过：数据库中确实不存在记录 {sample_key}")
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"查询已存在记录失败: {str(e)}", extra_data={
                'unique_keys_count': len(unique_keys),
                'exception_type': type(e).__name__
            })
            raise
    
    def batch_insert_records(self, records: List[Dict[str, Any]]) -> int:
        """
        批量新增记录（优化版：分小事务，使用executemany）
        
        Args:
            records: 要新增的记录列表
            
        Returns:
            int: 成功插入的记录数
        """
        if not records:
            return 0
        
        try:
            self.logger.info(f"开始批量新增记录", extra_data={
                'records_count': len(records)
            })
            
            # 准备SQL语句
            placeholders = ', '.join(['%s'] * len(self.insert_fields))
            sql = f"""
                INSERT INTO {self.table_name} 
                ({', '.join(self.insert_fields)}, created_at, updated_at)
                VALUES ({placeholders}, NOW(), NOW())
            """
            
            # 准备参数列表
            params_list = []
            current_time = datetime.now()
            
            for record in records:
                params = []
                for field in self.insert_fields:
                    value = record.get(field, '')
                    # 处理空值和NULL值
                    if value == '' or value is None:
                        # 对于日期时间字段，None表示NULL，空字符串也转为NULL
                        if field in ['create_time', 'last_fetch_time', 'sync_time']:
                            value = None
                        else:
                            value = ''
                    params.append(value)
                
                params_list.append(tuple(params))
            
            # 分小事务批量插入，避免超时
            total_affected = 0
            batch_size = 1000  # 每批次1000条记录，减小事务大小
            transaction_size = 5000  # 每个事务最多5000条记录
            
            for i in range(0, len(params_list), transaction_size):
                transaction_params = params_list[i:i + transaction_size]
                transaction_num = i // transaction_size + 1
                total_transactions = (len(params_list) + transaction_size - 1) // transaction_size
                
                self.logger.info(f"执行第{transaction_num}/{total_transactions}个事务，包含{len(transaction_params)}条记录")
                
                try:
                    # 每个事务单独处理
                    with self.db_manager.transaction() as conn:
                        cursor = conn.cursor()
                        
                        # 添加连接保活（每个事务开始时ping一下）
                        conn.ping(reconnect=True)
                        
                        # 在事务内按小批次使用executemany
                        transaction_affected = 0
                        for j in range(0, len(transaction_params), batch_size):
                            batch_params = transaction_params[j:j + batch_size]
                            
                            # 使用executemany批量执行，提高效率
                            affected = cursor.executemany(sql, batch_params)
                            transaction_affected += affected
                            
                            batch_num = j // batch_size + 1
                            total_batches_in_transaction = (len(transaction_params) + batch_size - 1) // batch_size
                            self.logger.info(f"事务{transaction_num} - 批次{batch_num}/{total_batches_in_transaction} 插入完成: {len(batch_params)}条")
                        
                        total_affected += transaction_affected
                        self.logger.info(f"事务{transaction_num}完成，插入{transaction_affected}条记录")
                        
                except Exception as e:
                    error_str = str(e)
                    self.logger.error(f"事务{transaction_num}失败: {error_str}")
                    
                    # 🔧 如果是唯一约束冲突，提取冲突的键值并记录
                    if "1062" in error_str and "Duplicate entry" in error_str:
                        # 提取重复的键值
                        import re
                        match = re.search(r"Duplicate entry '([^']+)'", error_str)
                        if match:
                            duplicate_key = match.group(1)
                            self.logger.warning(f"🚨 检测到唯一约束冲突，重复键值: {duplicate_key}")
                            
                            # 在当前事务的记录中查找匹配的记录
                            conflict_records = []
                            for param in transaction_params:
                                # 获取seller_sku_child和account字段的值（按insert_fields顺序）
                                seller_sku_idx = self.insert_fields.index('seller_sku_child')
                                account_idx = self.insert_fields.index('account')
                                
                                record_key = f"{param[seller_sku_idx]}-{param[account_idx]}"
                                if record_key == duplicate_key:
                                    conflict_records.append({
                                        'seller_sku_child': param[seller_sku_idx],
                                        'account': param[account_idx],
                                        'transaction': transaction_num
                                    })
                            
                            if conflict_records:
                                self.logger.warning(f"冲突记录详情: {conflict_records[:3]}")  # 最多显示3个
                    
                    # 继续处理下一个事务，不因为单个事务失败而终止整个过程
                    continue
            
            self.logger.info(f"批量新增完成", extra_data={
                'total_records': len(records),
                'affected_rows': total_affected
            })
            
            return total_affected
            
        except Exception as e:
            self.logger.error(f"批量新增记录失败: {str(e)}", extra_data={
                'records_count': len(records),
                'exception_type': type(e).__name__
            })
            raise
    
    def batch_update_records(self, records: List[Dict[str, Any]]) -> int:
        """
        批量更新记录（优化版：分小事务，使用executemany）
        
        Args:
            records: 要更新的记录列表（必须包含id字段）
            
        Returns:
            int: 成功更新的记录数
        """
        if not records:
            return 0
        
        try:
            self.logger.info(f"开始批量更新记录", extra_data={
                'records_count': len(records)
            })
            
            # 准备SQL语句
            set_clauses = [f"{field} = %s" for field in self.update_fields]
            sql = f"""
                UPDATE {self.table_name} 
                SET {', '.join(set_clauses)}, updated_at = NOW()
                WHERE id = %s
            """
            
            # 准备参数列表
            params_list = []
            
            for record in records:
                if 'id' not in record:
                    self.logger.warning(f"记录缺少ID字段，跳过更新: {record.get('seller_sku_child', 'unknown')}")
                    continue
                
                params = []
                # 添加要更新的字段值
                for field in self.update_fields:
                    value = record.get(field, '')
                    # 处理空值和NULL值
                    if value == '' or value is None:
                        # 对于日期时间字段，None表示NULL，空字符串也转为NULL
                        if field in ['create_time', 'last_fetch_time', 'sync_time']:
                            value = None
                        else:
                            value = ''
                    params.append(value)
                
                # 添加WHERE条件的ID
                params.append(record['id'])
                
                params_list.append(tuple(params))
            
            if not params_list:
                self.logger.warning("没有有效的更新记录")
                return 0
            
            # 分小事务批量更新，避免超时
            total_affected = 0
            batch_size = 1000  # 每批次1000条记录
            transaction_size = 3000  # 每个事务最多3000条记录（更新比插入慢）
            
            for i in range(0, len(params_list), transaction_size):
                transaction_params = params_list[i:i + transaction_size]
                transaction_num = i // transaction_size + 1
                total_transactions = (len(params_list) + transaction_size - 1) // transaction_size
                
                self.logger.info(f"执行第{transaction_num}/{total_transactions}个更新事务，包含{len(transaction_params)}条记录")
                
                try:
                    # 每个事务单独处理
                    with self.db_manager.transaction() as conn:
                        cursor = conn.cursor()
                        
                        # 添加连接保活
                        conn.ping(reconnect=True)
                        
                        # 在事务内按小批次使用executemany
                        transaction_affected = 0
                        for j in range(0, len(transaction_params), batch_size):
                            batch_params = transaction_params[j:j + batch_size]
                            
                            # 使用executemany批量执行
                            affected = cursor.executemany(sql, batch_params)
                            transaction_affected += affected
                            
                            batch_num = j // batch_size + 1
                            total_batches_in_transaction = (len(transaction_params) + batch_size - 1) // batch_size
                            self.logger.info(f"更新事务{transaction_num} - 批次{batch_num}/{total_batches_in_transaction} 完成: {len(batch_params)}条")
                        
                        total_affected += transaction_affected
                        self.logger.info(f"更新事务{transaction_num}完成，更新{transaction_affected}条记录")
                        
                except Exception as e:
                    self.logger.error(f"更新事务{transaction_num}失败: {str(e)}")
                    # 继续处理下一个事务
                    continue
            
            self.logger.info(f"批量更新完成", extra_data={
                'total_records': len(records),
                'valid_records': len(params_list),
                'affected_rows': total_affected
            })
            
            return total_affected
            
        except Exception as e:
            self.logger.error(f"批量更新记录失败: {str(e)}", extra_data={
                'records_count': len(records),
                'exception_type': type(e).__name__
            })
            raise
    
    def process_records(self, records: List[Dict[str, Any]], force_update: bool = False) -> Dict[str, Any]:
        """
        处理记录：自动识别新增和更新，并执行批量操作（增强版：支持冲突智能恢复）
        
        Args:
            records: 要处理的记录列表
            force_update: 是否强制更新所有已存在记录（不检查变化）
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        if not records:
            return {
                'total_records': 0,
                'insert_count': 0,
                'update_count': 0,
                'error_count': 0,
                'success': True,
                'message': '没有记录需要处理'
            }
        
        try:
            self.logger.info(f"开始处理记录", extra_data={
                'total_records': len(records)
            })
            
            # 提取唯一键
            from .csv_processor import CSVProcessor
            csv_processor = CSVProcessor(self.logger)
            
            unique_keys = [csv_processor.get_unique_key(record) for record in records]
            
            # 🔧 调试信息：显示部分CSV记录的唯一键
            self.logger.info(f"CSV记录样例唯一键: {unique_keys[:5]}")
            
            # 查询已存在的记录
            existing_records = self.get_existing_records(unique_keys)
            
            # 分组记录（新增vs更新）
            grouped_records = csv_processor.group_records_by_operation(records, existing_records, force_update)
            
            insert_records = grouped_records['insert']
            update_records = grouped_records['update']
            
            # 🔧 调试信息：显示分组结果和样例
            self.logger.info(f"记录分组结果 - 新增: {len(insert_records)}条, 更新: {len(update_records)}条")
            
            if insert_records:
                insert_sample_keys = [csv_processor.get_unique_key(record) for record in insert_records[:5]]
                self.logger.info(f"新增记录样例唯一键: {insert_sample_keys}")
            
            if update_records:
                update_sample_keys = [csv_processor.get_unique_key(record) for record in update_records[:5]]
                self.logger.info(f"更新记录样例唯一键: {update_sample_keys}")
            
            # 🚨 关键检查：如果所有记录都被分到新增组，可能有问题
            if len(insert_records) == len(records) and len(existing_records) > 0:
                self.logger.warning(f"⚠️ 检测到异常：所有{len(records)}条记录都被分到新增组，但数据库中存在{len(existing_records)}条已存在记录")
            
            # 🎯 增强版：支持冲突智能恢复的执行逻辑
            return self._execute_with_conflict_recovery(insert_records, update_records, csv_processor, force_update)
            
        except Exception as e:
            result = {
                'total_records': len(records),
                'insert_count': 0,
                'update_count': 0,
                'error_count': len(records),
                'success': False,
                'message': f'处理失败: {str(e)}'
            }
            
            self.logger.error(f"记录处理失败: {str(e)}", extra_data=result)
            
            return result
    
    def _execute_with_conflict_recovery(self, insert_records: List[Dict[str, Any]], 
                                      update_records: List[Dict[str, Any]], 
                                      csv_processor, 
                                      force_update: bool = False) -> Dict[str, Any]:
        """
        执行批量操作，支持冲突智能恢复
        
        Args:
            insert_records: 新增记录列表
            update_records: 更新记录列表
            csv_processor: CSV处理器实例
            force_update: 是否强制更新
            
        Returns:
            Dict[str, Any]: 处理结果统计
        """
        insert_count = 0
        update_count = 0
        error_count = 0
        retry_count = 0
        max_retries = 2  # 最多重试2次
        
        # 首先执行更新操作（更新操作不会有冲突问题）
        if update_records:
            try:
                update_count = self.batch_update_records(update_records)
                self.logger.info(f"✅ 批量更新完成: {update_count}条")
            except Exception as e:
                error_count += len(update_records)
                self.logger.error(f"批量更新失败: {str(e)}")
                # 更新失败不影响插入操作，继续执行
        
        # 处理插入操作（支持冲突恢复）
        remaining_insert_records = insert_records.copy()
        
        while retry_count <= max_retries and remaining_insert_records:
            try:
                attempt_info = f"第{retry_count + 1}次尝试" if retry_count == 0 else f"第{retry_count}次冲突恢复重试"
                self.logger.info(f"🚀 {attempt_info}执行批量插入: {len(remaining_insert_records)}条记录")
                
                # 尝试批量插入
                insert_result = self.batch_insert_records(remaining_insert_records)
                insert_count += insert_result
                
                self.logger.info(f"✅ 批量插入完成: {insert_result}条")
                break  # 成功完成，退出重试循环
                
            except Exception as e:
                error_str = str(e)
                
                # 🎯 检测是否为唯一约束冲突
                if "1062" in error_str and "Duplicate entry" in error_str:
                    retry_count += 1
                    
                    if retry_count > max_retries:
                        self.logger.error(f"❌ 冲突恢复重试次数已达上限({max_retries}次)，放弃处理")
                        error_count += len(remaining_insert_records)
                        break
                    
                    self.logger.warning(f"🔍 检测到唯一约束冲突，开始智能冲突恢复 (第{retry_count}次重试)")
                    
                    # 🎯 提取冲突的唯一键并重新分组
                    conflict_resolution_result = self._resolve_conflicts(remaining_insert_records, csv_processor, force_update)
                    
                    if conflict_resolution_result['success']:
                        # 更新插入和更新列表
                        remaining_insert_records = conflict_resolution_result['remaining_insert_records']
                        recovered_update_records = conflict_resolution_result['recovered_update_records']
                        
                        self.logger.info(f"🔄 冲突解析完成", extra_data={
                            'remaining_insert': len(remaining_insert_records),
                            'recovered_update': len(recovered_update_records),
                            'resolved_conflicts': conflict_resolution_result['resolved_conflicts_count']
                        })
                        
                        # 立即执行恢复出的更新记录
                        if recovered_update_records:
                            try:
                                recovered_update_count = self.batch_update_records(recovered_update_records)
                                update_count += recovered_update_count
                                self.logger.info(f"✅ 冲突恢复更新完成: {recovered_update_count}条")
                            except Exception as update_e:
                                self.logger.error(f"❌ 冲突恢复更新失败: {str(update_e)}")
                                error_count += len(recovered_update_records)
                        
                        # 如果没有剩余插入记录，直接退出
                        if not remaining_insert_records:
                            self.logger.info("🎉 所有冲突记录已成功转换为更新操作")
                            break
                        
                        # 继续下一次插入尝试
                        continue
                    else:
                        self.logger.error(f"❌ 冲突解析失败: {conflict_resolution_result['message']}")
                        error_count += len(remaining_insert_records)
                        break
                else:
                    # 非冲突异常，直接失败
                    self.logger.error(f"❌ 非冲突异常，插入失败: {error_str}")
                    error_count += len(remaining_insert_records)
                    break
        
        # 计算最终结果
        total_records = len(insert_records) + len(update_records)
        success_count = insert_count + update_count
        
        result = {
            'total_records': total_records,
            'insert_count': insert_count,
            'update_count': update_count,
            'error_count': error_count,
            'success': error_count == 0,
            'message': f'处理完成：新增{insert_count}条，更新{update_count}条，失败{error_count}条',
            'conflict_recovery_attempts': retry_count
        }
        
        if retry_count > 0:
            result['message'] += f' (经过{retry_count}次冲突恢复)'
        
        self.logger.info(f"记录处理完成", extra_data=result)
        
        return result
    
    def _resolve_conflicts(self, conflict_insert_records: List[Dict[str, Any]], 
                          csv_processor, 
                          force_update: bool = False) -> Dict[str, Any]:
        """
        解析冲突：将冲突的插入记录转换为更新记录
        
        Args:
            conflict_insert_records: 发生冲突的插入记录列表
            csv_processor: CSV处理器实例
            force_update: 是否强制更新
            
        Returns:
            Dict[str, Any]: 冲突解析结果
        """
        try:
            self.logger.info(f"🔍 开始解析{len(conflict_insert_records)}条冲突记录")
            
            # 提取冲突记录的唯一键
            conflict_unique_keys = [csv_processor.get_unique_key(record) for record in conflict_insert_records]
            
            # 重新查询数据库，获取这些冲突记录的数据库信息
            self.logger.info(f"🔍 重新查询冲突记录的数据库信息")
            existing_conflict_records = self.get_existing_records(conflict_unique_keys)
            
            if not existing_conflict_records:
                return {
                    'success': False,
                    'message': '无法查询到冲突记录的数据库信息',
                    'remaining_insert_records': conflict_insert_records,
                    'recovered_update_records': [],
                    'resolved_conflicts_count': 0
                }
            
            # 🎯 重新分组：基于最新的数据库查询结果
            self.logger.info(f"🔄 基于{len(existing_conflict_records)}条数据库记录重新分组")
            regrouped_records = csv_processor.group_records_by_operation(
                conflict_insert_records, existing_conflict_records, force_update
            )
            
            remaining_insert_records = regrouped_records['insert']
            recovered_update_records = regrouped_records['update']
            
            resolved_conflicts_count = len(recovered_update_records)
            
            self.logger.info(f"🎯 冲突解析结果", extra_data={
                'original_conflict_records': len(conflict_insert_records),
                'remaining_insert_records': len(remaining_insert_records),
                'recovered_update_records': len(recovered_update_records),
                'resolved_conflicts_count': resolved_conflicts_count,
                'resolution_rate': f"{(resolved_conflicts_count/len(conflict_insert_records)*100):.1f}%" if conflict_insert_records else "0%"
            })
            
            # 显示恢复的更新记录样例
            if recovered_update_records:
                sample_recovered_keys = [csv_processor.get_unique_key(record) for record in recovered_update_records[:3]]
                self.logger.info(f"恢复的更新记录样例唯一键: {sample_recovered_keys}")
            
            return {
                'success': True,
                'message': f'成功解析{resolved_conflicts_count}条冲突记录',
                'remaining_insert_records': remaining_insert_records,
                'recovered_update_records': recovered_update_records,
                'resolved_conflicts_count': resolved_conflicts_count
            }
            
        except Exception as e:
            self.logger.error(f"❌ 冲突解析异常: {str(e)}")
            return {
                'success': False,
                'message': f'冲突解析异常: {str(e)}',
                'remaining_insert_records': conflict_insert_records,
                'recovered_update_records': [],
                'resolved_conflicts_count': 0
            }
    
    def get_table_statistics(self) -> Dict[str, Any]:
        """
        获取表统计信息
        
        Returns:
            Dict[str, Any]: 表统计信息
        """
        try:
            # 总记录数
            total_sql = f"SELECT COUNT(*) as total_count FROM {self.table_name}"
            total_result = self.db_manager.execute_query(total_sql)
            total_count = total_result[0]['total_count'] if total_result else 0
            
            # 今日新增记录数
            today_sql = f"""
                SELECT COUNT(*) as today_count 
                FROM {self.table_name} 
                WHERE DATE(created_at) = CURDATE()
            """
            today_result = self.db_manager.execute_query(today_sql)
            today_count = today_result[0]['today_count'] if today_result else 0
            
            # 按平台统计
            platform_sql = f"""
                SELECT platform, COUNT(*) as count 
                FROM {self.table_name} 
                GROUP BY platform 
                ORDER BY count DESC 
                LIMIT 10
            """
            platform_result = self.db_manager.execute_query(platform_sql)
            
            return {
                'total_count': total_count,
                'today_count': today_count,
                'platform_stats': platform_result,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取表统计信息失败: {str(e)}")
            return {
                'total_count': 0,
                'today_count': 0,
                'platform_stats': [],
                'error': str(e)
            } 