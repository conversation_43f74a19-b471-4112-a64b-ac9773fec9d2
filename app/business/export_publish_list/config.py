"""
导出功能配置文件

定义导出相关的默认配置和常量
"""

import os
from typing import Dict, Any
from datetime import datetime


class ExportConfig:
    """导出功能配置类"""
    
    # 接口配置
    BASE_URL = "https://salecentersaasapi.yibainetwork.com"
    EXPORT_ENDPOINT = "/publish/publish_success/publish_success_export"
    MANAGEMENT_ENDPOINT = "/common/common_system_export/common_system_export_list"
    
    # 平台配置
    DEFAULT_PLATFORM_CODE = "AMAZON"
    SUPPORTED_PLATFORMS = ["AMAZON", "EBAY", "WISH", "ALIEXPRESS"]
    
    # 导出类型配置
    DEFAULT_DATA_TYPE_FILTER = "【刊登成功列表】-列表导出"
    SUPPORTED_DATA_TYPES = [
        "【刊登成功列表】-列表导出",
        "【订单列表】-列表导出",
        "【产品列表】-列表导出"
    ]
    
    # 文件配置
    DEFAULT_DOWNLOAD_DIR = "downloads"
    SUPPORTED_FILE_FORMATS = [".csv", ".xlsx", ".txt"]
    MAX_FILE_SIZE_MB = 100  # 最大文件大小限制
    
    # 轮询配置
    DEFAULT_POLL_INTERVAL = 10  # 轮询间隔(秒)
    MAX_POLL_TIME = 600  # 最大轮询时间(秒)
    POLL_TIMEOUT_WARNING = 300  # 轮询超时警告时间(秒)
    
    # 重试配置
    DEFAULT_MAX_RETRY_COUNT = 3
    DEFAULT_RETRY_DELAY = 5  # 重试延迟(秒)
    
    # 请求配置
    REQUEST_TIMEOUT = 30  # HTTP请求超时(秒)
    DOWNLOAD_TIMEOUT = 120  # 文件下载超时(秒)
    
    # Token配置
    JWT_TOKEN_MIN_LENGTH = 100  # JWT Token最小长度
    TOKEN_CACHE_DURATION = 300  # Token缓存时间(秒)
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            Dict[str, Any]: 默认配置字典
        """
        return {
            # 基础配置
            'platform_code': cls.DEFAULT_PLATFORM_CODE,
            'download_dir': cls.DEFAULT_DOWNLOAD_DIR,
            'data_type_filter': cls.DEFAULT_DATA_TYPE_FILTER,
            
            # 轮询配置
            'poll_interval': cls.DEFAULT_POLL_INTERVAL,
            'max_poll_time': cls.MAX_POLL_TIME,
            
            # 重试配置
            'max_retry_count': cls.DEFAULT_MAX_RETRY_COUNT,
            'retry_delay': cls.DEFAULT_RETRY_DELAY,
            
            # 超时配置
            'request_timeout': cls.REQUEST_TIMEOUT,
            'download_timeout': cls.DOWNLOAD_TIMEOUT,
            
            # 文件配置
            'max_file_size_mb': cls.MAX_FILE_SIZE_MB,
            
            # 功能开关
            'auto_login': True,
            'auto_create_download_dir': True,
            'enable_retry': True,
            'enable_token_cache': True,
            
            # 日志配置
            'log_level': cls.LOG_LEVEL,
            'enable_detailed_logs': True
        }
    
    @classmethod
    def get_date_range_today(cls) -> tuple:
        """
        获取今天的时间范围
        
        Returns:
            tuple: (start_date, end_date)
        """
        today = datetime.now()
        start_date = today.strftime('%Y-%m-%d 00:00:00')
        end_date = today.strftime('%Y-%m-%d 23:59:59')
        return start_date, end_date
    
    @classmethod
    def get_date_range_yesterday_to_today(cls) -> tuple:
        """
        获取昨天到今天的时间范围（默认导出时间）
        
        Returns:
            tuple: (start_date, end_date)
        """
        from datetime import timedelta
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        start_date = yesterday.strftime('%Y-%m-%d 00:00:00')
        end_date = today.strftime('%Y-%m-%d 23:59:59')
        return start_date, end_date
    
    @classmethod
    def get_date_range_yesterday(cls) -> tuple:
        """
        获取昨天的时间范围（仅昨天一天，不包含今天）
        
        Returns:
            tuple: (start_date, end_date)
        """
        from datetime import timedelta
        yesterday = datetime.now() - timedelta(days=1)
        start_date = yesterday.strftime('%Y-%m-%d 00:00:00')
        end_date = yesterday.strftime('%Y-%m-%d 23:59:59')
        return start_date, end_date
    
    @classmethod
    def get_date_range_last_week(cls) -> tuple:
        """
        获取上周的时间范围
        
        Returns:
            tuple: (start_date, end_date)
        """
        from datetime import timedelta
        today = datetime.now()
        last_week_start = today - timedelta(days=7)
        start_date = last_week_start.strftime('%Y-%m-%d 00:00:00')
        end_date = today.strftime('%Y-%m-%d 23:59:59')
        return start_date, end_date
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """
        验证配置的有效性
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置项
            required_keys = [
                'platform_code', 'download_dir', 'poll_interval', 
                'max_retry_count', 'request_timeout'
            ]
            
            for key in required_keys:
                if key not in config:
                    print(f"缺少必需的配置项: {key}")
                    return False
            
            # 检查平台代码
            if config['platform_code'] not in cls.SUPPORTED_PLATFORMS:
                print(f"不支持的平台代码: {config['platform_code']}")
                return False
            
            # 检查数值范围
            if config['poll_interval'] <= 0:
                print("轮询间隔必须大于0")
                return False
            
            if config['max_retry_count'] < 0:
                print("最大重试次数不能小于0")
                return False
            
            if config['request_timeout'] <= 0:
                print("请求超时时间必须大于0")
                return False
            
            # 检查下载目录
            download_dir = config['download_dir']
            if not os.path.exists(download_dir):
                if config.get('auto_create_download_dir', True):
                    try:
                        os.makedirs(download_dir, exist_ok=True)
                        print(f"已创建下载目录: {download_dir}")
                    except Exception as e:
                        print(f"无法创建下载目录 {download_dir}: {e}")
                        return False
                else:
                    print(f"下载目录不存在: {download_dir}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"配置验证异常: {e}")
            return False
    
    @classmethod
    def create_download_directory(cls, download_dir: str = None) -> str:
        """
        创建下载目录
        
        Args:
            download_dir: 下载目录路径
            
        Returns:
            str: 创建的目录路径
        """
        if not download_dir:
            download_dir = cls.DEFAULT_DOWNLOAD_DIR
        
        # 确保目录存在
        os.makedirs(download_dir, exist_ok=True)
        
        # 创建按日期分类的子目录
        today = datetime.now().strftime('%Y-%m-%d')
        date_dir = os.path.join(download_dir, today)
        os.makedirs(date_dir, exist_ok=True)
        
        return date_dir


# 导出状态常量
class ExportStatus:
    """导出状态常量"""
    
    PENDING = "等待中"
    GENERATING = "生成中"
    COMPLETED = "生成完成"
    FAILED = "生成失败"
    TIMEOUT = "超时"
    
    @classmethod
    def is_completed(cls, status: str) -> bool:
        """检查状态是否为完成"""
        return status == cls.COMPLETED
    
    @classmethod
    def is_failed(cls, status: str) -> bool:
        """检查状态是否为失败"""
        return status in [cls.FAILED, cls.TIMEOUT]
    
    @classmethod
    def is_processing(cls, status: str) -> bool:
        """检查状态是否为处理中"""
        return status in [cls.PENDING, cls.GENERATING]


# 错误代码常量
class ExportErrorCode:
    """导出错误代码常量"""
    
    LOGIN_FAILED = "LOGIN_FAILED"
    TOKEN_INVALID = "TOKEN_INVALID"
    REQUEST_FAILED = "REQUEST_FAILED"
    TIMEOUT = "TIMEOUT"
    DOWNLOAD_FAILED = "DOWNLOAD_FAILED"
    FILE_NOT_FOUND = "FILE_NOT_FOUND"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    
    ERROR_MESSAGES = {
        LOGIN_FAILED: "登录失败",
        TOKEN_INVALID: "Token无效或已过期",
        REQUEST_FAILED: "请求失败",
        TIMEOUT: "操作超时",
        DOWNLOAD_FAILED: "文件下载失败",
        FILE_NOT_FOUND: "文件不存在",
        PERMISSION_DENIED: "权限不足"
    }
    
    @classmethod
    def get_error_message(cls, error_code: str) -> str:
        """获取错误信息"""
        return cls.ERROR_MESSAGES.get(error_code, "未知错误")


# 全局配置实例
DEFAULT_EXPORT_CONFIG = ExportConfig.get_default_config() 