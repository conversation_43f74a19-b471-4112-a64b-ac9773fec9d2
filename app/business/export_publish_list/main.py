"""
亿迈系统导出功能主脚本

自动化刊登成功列表导出并存储到数据库
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# 添加项目根路径到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.core.base_rpa_async import AsyncBaseRPA
from app.business.export_publish_list.export_manager import ExportManager
from app.business.export_publish_list.config import ExportConfig


class ExportPublishListRPA(AsyncBaseRPA):
    """
    刊登成功列表导出RPA
    
    自动化导出刊登成功列表数据并存储到数据库的完整流程：
    1. 智能模式优先：无需Web驱动直接调用接口，避免504超时
    2. 标准模式备用：完整的RPA+HTTP流程，确保认证有效性
    3. 智能时间范围计算：环境变量配置 > 数据库最新时间+昨天 > 默认昨天
    4. CSV数据处理：编码检测、去重、字段映射、批量导入数据库
    """
    
    def __init__(self):
        """初始化导出RPA"""
        super().__init__(
            business_type="export_publish_list",
            script_name="export_publish_list"
        )
        
        # 初始化导出管理器
        self.export_manager = ExportManager(
            logger=self.logger,
            business_type=self.business_type,
            script_name=self.script_name
        )
        
        # 加载配置
        self.config = ExportConfig.get_default_config()
        
        # 显示环境信息
        from app.config.config import ConfigManager
        ConfigManager.print_environment_summary(self.business_type, self.script_name)
        
        self.logger.info("导出RPA初始化完成")
    
    async def _do_execute(self) -> dict:
        """
        执行导出任务并存储到数据库（支持智能504错误恢复）
        
        Returns:
            dict: 执行结果（包含数据库操作结果）
        """
        try:
            self.logger.info("开始执行导出任务并存储到数据库（智能模式）")
            
            # 使用智能时间范围计算替代固定范围
            try:
                date_range = await self.export_manager.calculate_smart_date_range_async()
                self.logger.info(f"智能计算的导出时间范围: {date_range}")
            except Exception as e:
                if "跳过任务" in str(e):
                    self.logger.warning(f"任务跳过: {str(e)}")
                    return {
                        'success': True,
                        'message': str(e),
                        'skipped': True
                    }
                # 如果智能计算失败，回退到昨天的时间范围
                self.logger.warning(f"智能时间范围计算失败: {str(e)}，使用默认昨天范围")
                date_range = ExportConfig.get_date_range_yesterday()
                self.logger.info(f"回退到默认导出时间范围(昨天): {date_range}")
            
            # 🎯 首先尝试智能模式（无需Web驱动，避免504错误）
            self.logger.info("🎯 首先尝试智能模式（无Web驱动）")
            smart_result = await self.export_manager.execute_smart_export_and_store(
                platform_code=self.config['platform_code'],
                date_range=date_range
            )
            
            if smart_result['success']:
                self.logger.info("✅ 智能模式成功完成", extra_data=smart_result)
                
                # 显示结果统计
                stats = self.export_manager.get_statistics()
                self.logger.info("导出统计信息", extra_data=stats)
                
                # 🔧 清理下载产生的临时文件
                await self._cleanup_temp_files()
                
                return {
                    'success': True,
                    'message': smart_result.get('message', '智能模式导出并存储到数据库完成'),
                    'file_size': smart_result['file_size'],
                    'export_info': smart_result['export_info'],
                    'db_result': smart_result['db_result'],
                    'used_existing_export': smart_result.get('used_existing_export', False),
                    'smart_mode': True,
                    'statistics': stats
                }
            
            # 智能模式失败，尝试标准模式
            self.logger.info("⚠️ 智能模式失败，尝试标准Web驱动模式", extra_data={
                'smart_mode_error': smart_result.get('error')
            })
            
            # 使用浏览器上下文执行导出任务
            async with self.web_driver_context() as driver:
                # 执行完整的导出并存储到数据库流程（包含智能504错误处理）
                # 使用已计算的智能时间范围
                self.logger.info(f"标准模式使用相同的时间范围: {date_range}")
                result = await self.export_manager.execute_export_and_store_to_db(
                    driver=driver,
                    platform_code=self.config['platform_code'],
                    date_range=date_range,
                    store_to_db=True
                )
            
            if result['success']:
                self.logger.info("标准模式导出并存储任务执行成功", extra_data=result)
                
                # 显示结果统计
                stats = self.export_manager.get_statistics()
                self.logger.info("导出统计信息", extra_data=stats)
                
                # 🔧 清理下载产生的临时文件
                await self._cleanup_temp_files()
                
                return {
                    'success': True,
                    'message': result.get('message', '标准模式导出并存储到数据库完成'),
                    'file_size': result['file_size'],
                    'export_info': result['export_info'],
                    'db_result': result['db_result'],
                    'used_existing_export': result.get('used_existing_export', False),
                    'smart_mode': False,
                    'statistics': stats
                }
            else:
                error_msg = result.get('message') or result.get('error', '未知错误')
                self.logger.error(f"标准模式导出并存储任务执行失败: {error_msg}")
                
                # 🔧 即使失败也要清理临时文件
                await self._cleanup_temp_files()
                
                return {
                    'success': False,
                    'message': error_msg,
                    'error': result.get('error', error_msg),
                    'smart_mode': False
                }
                
        except Exception as e:
            error_str = str(e)
            is_504_error = "504" in error_str or "Gateway Time-out" in error_str
            
            self.logger.error(f"导出并存储任务异常: {error_str}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': error_str,
                'is_504_error': is_504_error
            })
            
            # 🔧 发生异常时也要清理临时文件
            await self._cleanup_temp_files()
            
            # 🔄 如果是504错误，记录特殊标识便于监控
            error_result = {
                'success': False,
                'message': f'导出并存储任务异常: {error_str}',
                'error': error_str,
                'is_504_error': is_504_error
            }
            
            if is_504_error:
                error_result['message'] = f'检测到504网关超时错误: {error_str}'
                self.logger.warning("检测到504错误，建议检查网络状况或等待后重试")
            
            return error_result
    
    async def _cleanup_temp_files(self):
        """
        清理临时文件（仅清理下载产生的临时文件）
        
        export_publish_list业务会下载CSV文件，需要清理下载临时目录，
        但不清理项目文件。
        """
        try:
            import tempfile
            from pathlib import Path
            
            cleaned_items = []
            
            # 1. 清理下载目录中的临时文件
            download_dirs_to_check = [
                # 检查downloads目录
                Path.cwd() / "downloads",
                Path.cwd() / "temp",
                # 检查系统临时目录中的相关文件
                Path(tempfile.gettempdir()) / "export_csv_*",
                Path(tempfile.gettempdir()) / "publish_list_*"
            ]
            
            for download_dir in download_dirs_to_check:
                if "*" in str(download_dir):
                    # 处理通配符模式
                    parent_dir = download_dir.parent
                    pattern = download_dir.name
                    if parent_dir.exists():
                        for temp_file in parent_dir.glob(pattern):
                            if temp_file.is_file():
                                try:
                                    temp_file.unlink()
                                    cleaned_items.append(f"临时文件: {temp_file.name}")
                                    self.logger.debug(f"已清理临时文件: {temp_file}")
                                except Exception as e:
                                    self.logger.debug(f"清理临时文件失败: {temp_file}, 错误: {str(e)}")
                elif download_dir.exists():
                    if download_dir.is_dir():
                        # 清理目录中的CSV文件
                        for csv_file in download_dir.glob("*.csv"):
                            try:
                                csv_file.unlink()
                                cleaned_items.append(f"下载文件: {csv_file.name}")
                                self.logger.debug(f"已清理下载文件: {csv_file}")
                            except Exception as e:
                                self.logger.debug(f"清理下载文件失败: {csv_file}, 错误: {str(e)}")
                        
                        # 如果下载目录为空，删除目录本身
                        try:
                            if not any(download_dir.iterdir()):
                                download_dir.rmdir()
                                cleaned_items.append(f"空目录: {download_dir.name}")
                                self.logger.debug(f"已清理空目录: {download_dir}")
                        except Exception as e:
                            self.logger.debug(f"清理空目录失败: {download_dir}, 错误: {str(e)}")
            
            # 2. 清理ExportManager中跟踪的临时文件
            if hasattr(self.export_manager, 'temp_files') and self.export_manager.temp_files:
                for temp_file_path in self.export_manager.temp_files[:]:  # 使用切片复制避免修改时的迭代问题
                    temp_file = Path(temp_file_path)
                    if temp_file.exists():
                        try:
                            temp_file.unlink()
                            cleaned_items.append(f"管理器临时文件: {temp_file.name}")
                            self.logger.debug(f"已清理管理器临时文件: {temp_file}")
                        except Exception as e:
                            self.logger.debug(f"清理管理器临时文件失败: {temp_file}, 错误: {str(e)}")
                
                # 清空临时文件跟踪列表
                self.export_manager.temp_files.clear()
            
            if cleaned_items:
                self.logger.info(f"任务完成，已清理 {len(cleaned_items)} 个临时文件", extra_data={
                    'cleaned_items': cleaned_items[:10],  # 只显示前10个文件
                    'total_cleaned': len(cleaned_items)
                })
            else:
                self.logger.debug("没有找到需要清理的下载临时文件")
                
        except Exception as e:
            self.logger.warning(f"清理临时文件时发生异常: {str(e)}")  # 不影响主流程，只记录警告


async def main():
    """
    主函数 - 执行导出并存储到数据库
    """
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("启动导出功能")
    
    try:
        # 创建导出RPA实例并执行
        export_rpa = ExportPublishListRPA()
        result = await export_rpa.execute()
        
        # 处理执行结果
        if result.get('status') == 'success':
            # AsyncBaseRPA.execute()返回的格式
            data = result.get('data', {})
            logger.info("任务执行成功")
            print(f"\n✅ 任务执行成功")
            print(f"📄 消息: {data.get('message', '任务完成')}")
            
            # 显示数据库操作结果
            if data.get('db_result'):
                db_result = data['db_result']
                if db_result.get('success'):
                    print(f"\n💾 数据库操作成功:")
                    print(f"   📊 总记录数: {db_result.get('total_records', 0)}")
                    print(f"   ➕ 新增记录: {db_result.get('insert_count', 0)}")
                    print(f"   🔄 更新记录: {db_result.get('update_count', 0)}")
                    print(f"   ⏭️ 跳过记录: {db_result.get('skip_count', 0)}")
                    
                    # 显示表统计信息
                    table_stats = db_result.get('table_statistics', {})
                    if table_stats:
                        print(f"   📈 表总记录数: {table_stats.get('total_count', 0)}")
                        print(f"   📅 今日新增: {table_stats.get('today_count', 0)}")
                else:
                    print(f"\n❌ 数据库操作失败: {db_result.get('message', '未知错误')}")
            
            # 显示文件信息
            if data.get('file_size'):
                print(f"📊 文件大小: {data['file_size']} 字节")
            
            # 显示统计信息
            if data.get('statistics'):
                stats = data['statistics']
                print(f"📈 成功率: {stats.get('success_rate', 0):.1f}%")
                print(f"📦 总导出次数: {stats.get('total_exports', 0)}")
            
            return True
            
        else:
            # 执行失败
            logger.error("任务执行失败")
            print(f"\n❌ 任务执行失败")
            
            if result.get('status') == 'failed' and 'error' in result:
                error_info = result['error']
                print(f"📄 错误类型: {error_info.get('error_type', 'Unknown')}")
                print(f"📄 错误消息: {error_info.get('error_message', 'Unknown error')}")
            else:
                print(f"📄 消息: {result.get('message', '未知错误')}")
            
            return False
    
    except Exception as e:
        logger.error(f"主函数异常: {e}")
        print(f"\n💥 程序异常: {str(e)}")
        return False


if __name__ == "__main__":
    """
    亿迈系统刊登成功列表导出工具
    
    功能：
    - 自动导出刊登成功列表数据
    - 智能CSV数据处理和去重
    - 批量存储到数据库
    - 支持智能504错误恢复
    
    使用方法:
       python main.py
    
    特性：
    - 智能模式优先（无Web驱动，高效稳定）
    - 标准模式备用（完整RPA流程）
    - 数据库唯一索引 seller_sku_child + account 自动识别新增/更新
    - 支持智能编码检测（GBK/UTF-8）和跨平台兼容
    """
    
    # 运行异步主函数
    success = asyncio.run(main())
    
    # 设置退出代码
    sys.exit(0 if success else 1) 