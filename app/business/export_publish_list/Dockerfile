# RPA-K8s Dockerfile - export_publish_list业务专用 (优化版)
# 支持export_publish_list业务脚本的超快速构建（预计30-60秒）

# 使用预构建的基础镜像（包含所有依赖）
FROM crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-k8s-base:latest

# 设置构建参数
ARG BUSINESS_TYPE=export_publish_list
ARG SCRIPT_NAME=export_publish_list
ARG BUILD_DATE
ARG GIT_COMMIT
ARG INSTALL_BROWSER_AT_BUILD=false

# 设置标签
LABEL maintainer="RPA-K8s Team"
LABEL description="RPA automation business - export_publish_list - speed optimized"
LABEL business_type="${BUSINESS_TYPE}"
LABEL script_name="${SCRIPT_NAME}"
LABEL build_date="${BUILD_DATE}"
LABEL git_commit="${GIT_COMMIT}"
LABEL version="3.0-speed-optimized"

# 设置业务环境变量
ENV BUSINESS_TYPE=${BUSINESS_TYPE} \
    SCRIPT_NAME=${SCRIPT_NAME} \
    RPA_EXECUTION_MODE=docker \
    PLAYWRIGHT_HEADLESS=true \
    PLAYWRIGHT_TIMEOUT=45 \
    PLAYWRIGHT_PAGE_LOAD_WAIT=15 \
    TZ=Asia/Shanghai

# 切换到root用户进行文件操作
USER root

# 工作目录
WORKDIR /app

# 创建必要的目录结构
RUN mkdir -p /app/app/business/export_publish_list \
    /app/app/core \
    /app/app/config \
    /app/app/utils \
    /app/app/shared \
    /app/downloads \
    /app/logs

# 🚀 核心优化：只复制必要的业务代码（最小化传输）
# 分层复制，优先复制不常变的文件
COPY app/core/ /app/app/core/
COPY app/config/ /app/app/config/
COPY app/utils/ /app/app/utils/
COPY app/shared/ /app/app/shared/

# 最后复制经常变化的业务代码
COPY app/business/export_publish_list/ /app/app/business/export_publish_list/

# ❌ 移除重复的aiohttp安装（基础镜像已包含）
# RUN pip install --no-cache-dir aiohttp>=3.9.0

# ✅ 浏览器已在基础镜像中预安装，无需重复安装

# 确保浏览器缓存目录权限正确（基础镜像中已包含浏览器和用户）
RUN chown -R rpauser:rpauser /home/<USER>
    # 验证浏览器缓存目录存在
    ls -la /home/<USER>/.cache/ms-playwright/ && \
    echo "✅ 浏览器缓存目录验证成功"

# 验证文件复制是否成功
RUN ls -la /app/app/business/export_publish_list/ && \
    echo "✅ 文件复制验证成功" && \
    chown -R rpauser:rpauser /app

# 复制启动脚本（避免复杂的转义问题）
COPY app/business/export_publish_list/start.sh /app/start.sh
RUN chmod +x /app/start.sh

# 健康检查（轻量级）
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; print('Health check passed'); sys.exit(0)" || exit 1

# 切换到非root用户
USER rpauser

# ❌ 移除重复的浏览器安装（基础镜像已包含或运行时按需安装）
# 条件性安装浏览器（构建时）
# RUN if [ "$INSTALL_BROWSER_AT_BUILD" = "true" ] ; then echo "构建时浏览器安装已跳过" ; fi

# 默认入口点
ENTRYPOINT ["/app/start.sh"] 