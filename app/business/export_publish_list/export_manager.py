"""
导出管理器

整合所有导出功能，提供完整的导出下载流程：
1. RPA登录获取认证信息
2. HTTP触发导出任务
3. 轮询等待导出完成
4. 下载导出文件
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import tempfile
from app.business.export_publish_list.export_client import ExportClient
from app.shared.clients import TokenExtractor
from app.shared.managers import YimaiLoginManager
from app.core.web_driver.base_driver import BaseWebDriver


class ExportManager:
    """
    导出管理器
    
    提供完整的导出下载功能：
    - 登录管理
    - Token提取
    - 导出请求
    - 状态监控
    - 文件下载
    - 跨平台兼容和文件清理
    """
    
    def __init__(self, logger: logging.Logger = None, business_type: str = None, script_name: str = None):
        """
        初始化导出管理器
        
        Args:
            logger: 日志记录器
            business_type: 业务类型
            script_name: 脚本名称
        """
        self.logger = logger or logging.getLogger(__name__)
        self.business_type = business_type or "export_publish_list"
        self.script_name = script_name or "export_publish_list"
        
        # 初始化各个组件
        self.export_client = ExportClient(self.logger)
        self.token_extractor = TokenExtractor(self.logger)
        self.login_manager = YimaiLoginManager(
            self.logger, 
            self.business_type, 
            self.script_name
        )
        
        # 配置参数
        self.config = {
            'download_dir': 'downloads',
            'platform_code': 'AMAZON',
            'data_type_filter': '【刊登成功列表】-列表导出',
            'max_retry_count': 3,
            'retry_delay': 5,
            'auto_login': True,
            'use_temp_files': True,  # 🔧 新增：是否使用临时文件
            'auto_cleanup': True     # 🔧 新增：是否自动清理临时文件
        }
        
        # 初始化数据库操作器（用于获取最新创建时间）
        from .db_operations import PublishSuccessDBOperator
        self.db_operator = PublishSuccessDBOperator(
            business_type=self.business_type,
            script_name=self.script_name,
            logger=self.logger
        )
        
        # 状态跟踪
        self.last_tokens = None
        self.last_login_time = None
        self.export_history = []
        self.temp_files = []  # 🔧 新增：临时文件跟踪列表
        
        self.logger.info("导出管理器初始化完成", extra_data={
            'business_type': self.business_type,
            'script_name': self.script_name,
            'download_dir': self.config['download_dir'],
            'platform_code': self.config['platform_code'],
            'use_temp_files': self.config['use_temp_files'],
            'auto_cleanup': self.config['auto_cleanup']
        })
    
    async def get_latest_create_time_from_db(self) -> Optional[str]:
        """
        从数据库获取最新的创建时间
        
        Returns:
            Optional[str]: 最新创建时间（YYYY-MM-DD格式），如果没有记录返回None
        """
        try:
            self.logger.info("从数据库获取最新创建时间")
            
            # 优化后的查询 - 直接使用索引获取最新时间
            sql = """
                SELECT MAX(create_time) as latest_datetime 
                FROM publish_success_list 
                WHERE create_time IS NOT NULL
            """
            
            results = self.db_operator.db_manager.execute_query(sql)
            
            if results and results[0]['latest_datetime']:
                latest_datetime = results[0]['latest_datetime']
                # 提取日期部分（YYYY-MM-DD格式）
                if hasattr(latest_datetime, 'strftime'):
                    latest_date_str = latest_datetime.strftime('%Y-%m-%d')
                elif hasattr(latest_datetime, 'date'):
                    latest_date_str = latest_datetime.date().strftime('%Y-%m-%d')
                else:
                    # 字符串格式处理
                    latest_date_str = str(latest_datetime)[:10]  # 取前10个字符 YYYY-MM-DD
                
                self.logger.info(f"数据库最新创建时间: {latest_date_str} (优化查询)")
                return latest_date_str
            else:
                self.logger.info("数据库中没有找到记录")
                return None
                
        except Exception as e:
            self.logger.error(f"获取数据库最新创建时间失败: {str(e)}")
            return None
    
    def calculate_smart_date_range(self) -> Tuple[str, str]:
        """
        智能计算导出时间范围
        
        逻辑：
        1. 优先从环境变量获取配置
        2. 如果没有配置：
           - 开始时间：从数据库获取最新创建时间（年月日）
           - 结束时间：昨天
           - 如果数据库最新时间晚于昨天，则跳过任务
           - 其他情况默认：开始和结束时间都为昨天
        
        Returns:
            Tuple[str, str]: (start_date, end_date) 格式：YYYY-MM-DD HH:MM:SS
        
        Raises:
            Exception: 当检测到应该跳过任务时
        """
        try:
            # Step 1: 检查环境变量配置
            env_start_date = os.getenv('EXPORT_START_DATE')
            env_end_date = os.getenv('EXPORT_END_DATE')
            
            if env_start_date and env_end_date:
                self.logger.info(f"使用环境变量配置的时间范围: {env_start_date} ~ {env_end_date}")
                
                # 确保时间格式包含时分秒
                start_date = env_start_date if ' ' in env_start_date else f"{env_start_date} 00:00:00"
                end_date = env_end_date if ' ' in env_end_date else f"{env_end_date} 23:59:59"
                
                return (start_date, end_date)
            
            # Step 2: 计算昨天
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_str = yesterday.strftime('%Y-%m-%d')
            
            self.logger.info(f"计算的昨天日期: {yesterday_str}")
            
            # Step 3: 从数据库获取最新创建时间
            import asyncio
            if asyncio.get_event_loop().is_running():
                # 如果已经在异步环境中，直接调用
                latest_db_date = asyncio.create_task(self.get_latest_create_time_from_db())
                latest_db_date = latest_db_date.result() if hasattr(latest_db_date, 'result') else None
            else:
                # 如果不在异步环境中，创建新的事件循环
                latest_db_date = asyncio.run(self.get_latest_create_time_from_db())
            
            if latest_db_date:
                self.logger.info(f"数据库最新创建时间: {latest_db_date}")
                
                # Step 4: 检查数据库最新时间是否晚于昨天
                if latest_db_date > yesterday_str:
                    raise Exception(f"数据库最新创建时间({latest_db_date})晚于昨天({yesterday_str})，跳过任务")
                
                # Step 5: 使用数据库时间作为开始时间，昨天作为结束时间
                start_date = f"{latest_db_date} 00:00:00"
                end_date = f"{yesterday_str} 23:59:59"
                
                self.logger.info(f"使用智能计算的时间范围: {start_date} ~ {end_date}")
                return (start_date, end_date)
            
            else:
                # Step 6: 没有数据库记录，使用昨天作为开始和结束时间
                start_date = f"{yesterday_str} 00:00:00"
                end_date = f"{yesterday_str} 23:59:59"
                
                self.logger.info(f"使用默认时间范围（昨天）: {start_date} ~ {end_date}")
                return (start_date, end_date)
                
        except Exception as e:
            self.logger.error(f"智能时间范围计算失败: {str(e)}")
            raise
    
    async def calculate_smart_date_range_async(self) -> Tuple[str, str]:
        """
        异步版本的智能计算导出时间范围
        
        Returns:
            Tuple[str, str]: (start_date, end_date) 格式：YYYY-MM-DD HH:MM:SS
        
        Raises:
            Exception: 当检测到应该跳过任务时
        """
        try:
            # Step 1: 检查环境变量配置
            env_start_date = os.getenv('EXPORT_START_DATE')
            env_end_date = os.getenv('EXPORT_END_DATE')
            
            if env_start_date and env_end_date:
                self.logger.info(f"使用环境变量配置的时间范围: {env_start_date} ~ {env_end_date}")
                
                # 确保时间格式包含时分秒
                start_date = env_start_date if ' ' in env_start_date else f"{env_start_date} 00:00:00"
                end_date = env_end_date if ' ' in env_end_date else f"{env_end_date} 23:59:59"
                
                return (start_date, end_date)
            
            # Step 2: 计算昨天
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_str = yesterday.strftime('%Y-%m-%d')
            
            self.logger.info(f"计算的昨天日期: {yesterday_str}")
            
            # Step 3: 从数据库获取最新创建时间
            latest_db_date = await self.get_latest_create_time_from_db()
            
            if latest_db_date:
                self.logger.info(f"数据库最新创建时间: {latest_db_date}")
                
                # Step 4: 检查数据库最新时间是否晚于昨天
                if latest_db_date > yesterday_str:
                    raise Exception(f"数据库最新创建时间({latest_db_date})晚于昨天({yesterday_str})，跳过任务")
                
                # Step 5: 使用数据库时间作为开始时间，昨天作为结束时间
                start_date = f"{latest_db_date} 00:00:00"
                end_date = f"{yesterday_str} 23:59:59"
                
                self.logger.info(f"使用智能计算的时间范围: {start_date} ~ {end_date}")
                return (start_date, end_date)
            
            else:
                # Step 6: 没有数据库记录，使用昨天作为开始和结束时间
                start_date = f"{yesterday_str} 00:00:00"
                end_date = f"{yesterday_str} 23:59:59"
                
                self.logger.info(f"使用默认时间范围（昨天）: {start_date} ~ {end_date}")
                return (start_date, end_date)
                
        except Exception as e:
            self.logger.error(f"异步智能时间范围计算失败: {str(e)}")
            raise
    
    async def execute_export_download(self, 
                                    driver: BaseWebDriver,
                                    platform_code: str = None,
                                    date_range: Tuple[str, str] = None,
                                    download_dir: str = None) -> Dict[str, Any]:
        """
        执行完整的导出下载流程
        
        Args:
            driver: Web驱动对象
            platform_code: 平台代码 (默认AMAZON)
            date_range: 时间范围 (start_date, end_date)
            download_dir: 下载目录
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            self.logger.info("开始执行导出下载流程")
            
            # 参数处理
            platform_code = platform_code or self.config['platform_code']
            download_dir = download_dir or self.config['download_dir']
            
            # 🎯 使用智能时间范围计算
            if not date_range:
                try:
                    date_range = await self.calculate_smart_date_range_async()
                    self.logger.info(f"智能计算时间范围: {date_range[0]} ~ {date_range[1]}")
                except Exception as e:
                    if "跳过任务" in str(e):
                        self.logger.warning(f"任务跳过: {str(e)}")
                        return {
                            'success': True,
                            'skipped': True,
                            'message': str(e),
                            'timestamp': datetime.now().isoformat()
                        }
                    else:
                        raise
            
            # 步骤1: 确保登录状态
            login_success = await self._ensure_login_status(driver)
            if not login_success:
                raise Exception("登录失败")
            
            # 步骤2: 提取认证Token
            tokens = await self._extract_tokens(driver)
            if not tokens:
                raise Exception("Token提取失败")
            
            # 步骤3: 执行导出流程（包含生成失败重试机制）
            export_result = await self._execute_export_with_retry(tokens, platform_code, date_range)
            completed_export = export_result.get('export_data')
            
            if not completed_export:
                raise Exception("导出流程失败，未获取到有效的导出任务数据")
            
            # 记录导出流程结果
            self.logger.info("导出流程完成", extra_data={
                'used_existing': export_result.get('used_existing', False),
                'retry_count': export_result.get('retry_count', 0),
                'task_id': completed_export.get('id'),
                'task_name': completed_export.get('task_name'),
                'status': completed_export.get('status')
            })
            
            # 步骤4: 下载文件
            file_path = await self._download_export_file(completed_export, tokens, download_dir)
            
            # 记录成功结果
            result = {
                'success': True,
                'export_info': completed_export,
                'file_path': file_path,
                'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                'platform_code': platform_code,
                'date_range': date_range,
                'timestamp': datetime.now().isoformat()
            }
            
            self.export_history.append(result)
            
            self.logger.info("导出下载流程完成", extra_data={
                'file_path': file_path,
                'file_size': result['file_size'],
                'total_rows': completed_export.get('rows_sum', 0)
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"导出下载流程失败: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e),
                'platform_code': platform_code,
                'date_range': date_range,
                'download_dir': download_dir
            })
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _ensure_login_status(self, driver: BaseWebDriver) -> bool:
        """
        确保登录状态
        
        Args:
            driver: Web驱动对象
            
        Returns:
            bool: 是否登录成功
        """
        try:
            self.logger.info("检查登录状态")
            
            # 使用现有的登录管理器
            login_success = await self.login_manager.ensure_login(driver)
            
            if login_success:
                self.last_login_time = datetime.now()
                self.logger.info("登录状态确认成功")
                return True
            else:
                self.logger.error("登录状态确认失败")
                return False
                
        except Exception as e:
            self.logger.error(f"登录状态检查异常: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e)
            })
            return False
    
    async def _extract_tokens(self, driver: BaseWebDriver) -> Optional[Dict[str, str]]:
        """
        提取认证Token
        
        Args:
            driver: Web驱动对象
            
        Returns:
            Optional[Dict[str, str]]: Token字典
        """
        try:
            self.logger.info("开始提取认证Token")
            
            # 等待页面稳定
            await asyncio.sleep(2)
            
            # 从驱动获取Playwright页面对象
            page = getattr(driver, 'page', None)
            if not page:
                raise Exception("无法从驱动获取页面对象")
                
            # 提取所有Token
            tokens = await self.token_extractor.extract_all_tokens(page)
            
            # 验证Token有效性
            if await self.token_extractor.validate_tokens(tokens):
                self.last_tokens = tokens
                self.logger.info("Token提取和验证成功")
                return tokens
            else:
                self.logger.error("Token验证失败")
                return None
                
        except Exception as e:
            self.logger.error(f"Token提取异常: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e)
            })
            return None
    
    async def _request_export(self, 
                            tokens: Dict[str, str],
                            platform_code: str,
                            date_range: Tuple[str, str]) -> Dict[str, Any]:
        """
        请求导出数据（智能重试机制）
        
        优先检查今日已有导出任务，避免重复请求。
        当遇到504网关超时等错误时，会智能检查是否已有今日完成的导出任务
        
        Args:
            tokens: 认证Token
            platform_code: 平台代码
            date_range: 时间范围
            
        Returns:
            Dict[str, Any]: 导出请求结果
        """
        # # 🎯 Step 1: 首先检查今日是否已有完成的导出任务（避免重复请求）
        # self.logger.info("🔍 首先检查今日是否已有完成的导出任务")
        # existing_export = await self._check_existing_today_export(tokens)
        # if existing_export:
        #     self.logger.info("✅ 发现今日已完成的导出任务，直接使用", extra_data={
        #         'task_id': existing_export.get('id'),
        #         'task_name': existing_export.get('task_name'),
        #         'status': existing_export.get('status'),
        #         'rows_sum': existing_export.get('rows_sum')
        #     })
        #
        #     return {
        #         'status': 1,
        #         'message': '使用已有今日导出任务',
        #         'existing_export_used': True,
        #         'export_data': existing_export
        #     }
        
        # Step 2: 没有现有任务，开始新的导出请求
        # self.logger.info("📝 未发现今日完成任务，开始新的导出请求")
        self.logger.info("📝 开始新的导出请求")
        retry_count = 0
        last_error = None
        
        while retry_count < self.config['max_retry_count']:
            try:
                self.logger.info(f"请求导出数据 (第{retry_count + 1}次尝试)")
                
                result = await self.export_client.request_export(
                    platform_code=platform_code,
                    date_range=date_range,
                    tokens=tokens
                )
                
                # 检查结果
                if result.get('status') == 1:
                    self.logger.info("导出请求成功")
                    return result
                else:
                    error_msg = result.get('error_mess', '未知错误')
                    raise Exception(f"导出请求失败: {error_msg}")
                    
            except Exception as e:
                last_error = e
                retry_count += 1
                
                # 特殊处理不同类型的异常
                if isinstance(e, asyncio.TimeoutError):
                    exception_details = f"请求超时 (第{retry_count}次重试)"
                elif hasattr(e, 'message'):
                    exception_details = e.message
                else:
                    exception_details = str(e) if str(e) else f"{type(e).__name__} occurred"
                
                # 🎯 智能错误处理：检查是否为504网关超时错误
                is_504_error = "504" in exception_details or "Gateway Time-out" in exception_details
                
                if is_504_error:
                    self.logger.warning(f"检测到504网关超时错误，尝试检查已有导出任务", extra_data={
                        'exception_details': exception_details,
                        'retry_count': retry_count,
                        'platform_code': platform_code,
                        'date_range': date_range
                    })
                    
                    # 🔍 检查今日是否已有完成的导出任务
                    existing_export = await self._check_existing_today_export(tokens)
                    if existing_export:
                        self.logger.info("发现今日已完成的导出任务，直接使用", extra_data={
                            'task_id': existing_export.get('id'),
                            'task_name': existing_export.get('task_name'),
                            'status': existing_export.get('status')
                        })
                        
                        # 返回模拟的成功结果，使用已有导出任务
                        return {
                            'status': 1,
                            'message': '使用已有今日导出任务',
                            'existing_export_used': True,
                            'export_data': existing_export
                        }
                    else:
                        self.logger.warning("未发现今日完成的导出任务，继续重试")
                
                self.logger.warning(f"导出请求失败 (第{retry_count}次): {exception_details}", extra_data={
                    'exception_type': type(e).__name__,
                    'exception_details': exception_details,
                    'exception_args': str(e.args) if hasattr(e, 'args') else 'N/A',
                    'retry_count': retry_count,
                    'max_retries': self.config['max_retry_count'],
                    'platform_code': platform_code,
                    'date_range': date_range,
                    'is_504_error': is_504_error
                })
                
                # 如果是504错误且已检查过现有任务，减少重试次数
                if is_504_error and retry_count >= 2:
                    self.logger.warning("504错误且已重试2次，停止重试避免资源浪费")
                    break
                
                if retry_count < self.config['max_retry_count']:
                    self.logger.info(f"等待{self.config['retry_delay']}秒后重试")
                    await asyncio.sleep(self.config['retry_delay'])
        
        raise Exception(f"导出请求最终失败: {last_error}")
    
    async def _execute_export_with_retry(self, 
                                       tokens: Dict[str, str],
                                       platform_code: str,
                                       date_range: Tuple[str, str]) -> Dict[str, Any]:
        """
        执行导出请求和等待的完整流程，包括生成失败后的重试机制
        
        Args:
            tokens: 认证Token
            platform_code: 平台代码
            date_range: 时间范围
            
        Returns:
            Dict[str, Any]: 包含完成的导出任务信息
            
        Raises:
            Exception: 当所有重试都失败时抛出异常
        """
        retry_count = 0
        max_retries = 3  # 🎯 生成失败后最多重试3次
        last_error = None
        
        self.logger.info(f"开始执行导出流程，支持生成失败重试，最大重试次数: {max_retries}")
        
        while retry_count <= max_retries:  # 总共最多4次尝试（初始 + 3次重试）
            try:
                attempt_info = f"第{retry_count + 1}次尝试" if retry_count == 0 else f"第{retry_count}次重试"
                self.logger.info(f"🚀 {attempt_info}执行导出流程")
                
                # 步骤1: 请求导出（智能重试，支持使用已有任务）
                export_result = await self._request_export(tokens, platform_code, date_range)
                self.logger.info("导出请求已提交", extra_data={
                    'export_result': export_result,
                    'attempt': attempt_info
                })
                
                # 步骤2: 检查是否使用了已有导出任务
                if export_result.get('existing_export_used'):
                    self.logger.info("使用已有今日导出任务，跳过等待阶段")
                    completed_export = export_result.get('export_data')
                    if not completed_export:
                        raise Exception("已有导出任务数据无效")
                    
                    return {
                        'success': True,
                        'export_data': completed_export,
                        'used_existing': True,
                        'retry_count': retry_count
                    }
                
                # 步骤3: 正常等待导出完成
                completed_export = await self._wait_for_completion(tokens)
                if completed_export:
                    self.logger.info(f"✅ 导出流程成功完成 ({attempt_info})", extra_data={
                        'task_id': completed_export.get('id'),
                        'retry_count': retry_count
                    })
                    
                    return {
                        'success': True,
                        'export_data': completed_export,
                        'used_existing': False,
                        'retry_count': retry_count
                    }
                else:
                    raise Exception("导出任务等待超时或失败")
                    
            except Exception as e:
                last_error = e
                
                # 🎯 检查是否为生成失败异常
                if str(e).startswith("EXPORT_GENERATION_FAILED:"):
                    retry_count += 1
                    
                    if retry_count <= max_retries:
                        self.logger.warning(f"⚠️ 导出生成失败，准备第{retry_count}次重试", extra_data={
                            'failure_reason': str(e),
                            'retry_count': retry_count,
                            'max_retries': max_retries,
                            'platform_code': platform_code,
                            'date_range': date_range
                        })
                        
                        # 等待一段时间后重试
                        retry_delay = self.config['retry_delay'] * retry_count  # 递增延迟
                        self.logger.info(f"等待{retry_delay}秒后重试")
                        await asyncio.sleep(retry_delay)
                        continue  # 继续下一次重试
                    else:
                        self.logger.error(f"❌ 导出生成失败且重试次数已达上限", extra_data={
                            'failure_reason': str(e),
                            'retry_count': retry_count,
                            'max_retries': max_retries
                        })
                        break  # 退出重试循环
                else:
                    # 其他类型的异常，不重试
                    self.logger.error(f"❌ 导出流程异常 (非生成失败): {str(e)}", extra_data={
                        'exception_type': type(e).__name__,
                        'exception_details': str(e),
                        'retry_count': retry_count
                    })
                    break  # 退出重试循环
        
        # 所有重试都失败了
        raise Exception(f"导出流程最终失败 (已重试{retry_count}次): {last_error}")
    
    async def _check_existing_today_export(self, tokens: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        检查今日是否已有导出任务（优化版本）
        
        新逻辑：
        1. 获取今日所有导出任务（除"生成失败"外）
        2. 按创建时间排序，取最新一条
        3. 如果状态不是"生成完成"，则等待至完成
        
        Args:
            tokens: 认证Token
            
        Returns:
            Optional[Dict[str, Any]]: 今日最新导出任务（完成状态），如果没有则返回None
        """
        try:
            self.logger.info("检查今日是否已有导出任务")
            
            # 获取今日导出列表
            today = datetime.now().strftime('%Y-%m-%d')
            result = await self.export_client.get_export_list(tokens, created_unix=today)
            export_list = result.get('data_list', {}).get('data', [])
            
            if not export_list:
                self.logger.info("导出列表为空")
                return None
            
            self.logger.info("获取导出列表成功", extra_data={
                'total_count': len(export_list)
            })
            
            # 筛选今日的有效导出任务（排除生成失败）
            today_valid_exports = []
            for export_item in export_list:
                # 检查日期 - 必须是今日创建
                created_time = export_item.get('created_unix', '')
                if created_time.startswith(today):
                    # 检查数据类型 - 必须匹配过滤条件
                    data_type = export_item.get('data_type', '')
                    if self.config['data_type_filter'] in data_type:
                        # 检查状态 - 排除生成失败，其他状态都接受
                        status = export_item.get('status', '')
                        if status != '生成失败':
                            today_valid_exports.append(export_item)
                            
                            self.logger.debug(f"找到候选任务", extra_data={
                                'task_id': export_item.get('id'),
                                'created_time': created_time,
                                'status': status,
                                'rows_sum': export_item.get('rows_sum')
                            })
            
            if not today_valid_exports:
                self.logger.info(f"未发现今日有效的导出任务", extra_data={
                    'today_date': today,
                    'total_exports': len(export_list),
                    'data_type_filter': self.config['data_type_filter']
                })
                return None
            
            # 按创建时间排序，获取最新的任务
            latest_export = max(today_valid_exports, 
                              key=lambda x: x.get('created_unix', ''))
            
            status = latest_export.get('status', '')
            self.logger.info(f"发现今日最新有效导出任务", extra_data={
                'task_id': latest_export.get('id'),
                'task_name': latest_export.get('task_name'),
                'created_time': latest_export.get('created_unix'),
                'rows_sum': latest_export.get('rows_sum'),
                'status': status,
                'total_candidates': len(today_valid_exports)
            })
            
            # 如果状态已经是"生成完成"，直接返回
            if status == '生成完成':
                self.logger.info("任务已完成，直接使用")
                return latest_export
            
            # 如果状态不是"生成完成"，等待完成
            self.logger.info(f"任务状态为'{status}'，等待生成完成")
            completed_export = await self.export_client.wait_for_export_completion(
                tokens=tokens,
                export_id=latest_export.get('id'),
                target_date=today
            )
            
            if completed_export:
                self.logger.info("任务等待完成成功", extra_data={
                    'task_id': completed_export.get('id'),
                    'final_status': completed_export.get('status'),
                    'rows_sum': completed_export.get('rows_sum')
                })
                return completed_export
            else:
                self.logger.warning("任务等待完成失败或超时")
                return None
                
        except Exception as e:
            self.logger.error(f"检查今日导出任务异常: {str(e)}")
            return None
    
    async def _wait_for_completion(self, tokens: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """
        等待导出完成
        
        Args:
            tokens: 认证Token
            
        Returns:
            Optional[Dict[str, Any]]: 完成的导出任务
            
        Raises:
            Exception: 当导出生成失败时，抛出特定异常以便上层重试
        """
        try:
            self.logger.info("开始等待导出完成")
            
            # 等待导出任务完成
            completed_export = await self.export_client.wait_for_export_completion(
                tokens=tokens,
                target_date=datetime.now().strftime('%Y-%m-%d')
            )
            
            if completed_export:
                self.logger.info("导出任务完成", extra_data={
                    'task_id': completed_export.get('id'),
                    'task_name': completed_export.get('task_name'),
                    'rows_sum': completed_export.get('rows_sum'),
                    'status': completed_export.get('status')
                })
                return completed_export
            else:
                self.logger.error("导出任务等待失败")
                return None
                
        except Exception as e:
            # 🎯 检查是否为生成失败异常，如果是则重新抛出以便上层重试
            if str(e).startswith("EXPORT_GENERATION_FAILED:"):
                self.logger.warning(f"导出生成失败，将触发重试: {str(e)}")
                raise  # 重新抛出异常让上层处理重试
            else:
                self.logger.error(f"等待导出完成异常: {e}")
                return None
    
    async def _download_export_file(self, 
                                  export_data: Dict[str, Any],
                                  tokens: Dict[str, str],
                                  download_dir: str) -> str:
        """
        下载导出文件（支持临时文件模式）
        
        Args:
            export_data: 导出任务数据
            tokens: 认证Token
            download_dir: 下载目录
            
        Returns:
            str: 下载文件的本地路径
        """
        retry_count = 0
        last_error = None
        
        while retry_count < self.config['max_retry_count']:
            try:
                self.logger.info(f"下载导出文件 (第{retry_count + 1}次尝试)")
                
                # 🔧 根据配置决定是否使用临时文件
                use_temp_dir = self.config.get('use_temp_files', True)
                
                file_path = await self.export_client.download_file(
                    export_data=export_data,
                    tokens=tokens,
                    save_dir=download_dir if not use_temp_dir else None,
                    use_temp_dir=use_temp_dir
                )
                
                # 🔧 如果使用临时文件，加入跟踪列表
                if use_temp_dir:
                    self.temp_files.append(file_path)
                    self.logger.debug(f"临时文件已记录: {file_path}")
                
                # 验证文件下载成功
                file_obj = Path(file_path)
                if file_obj.exists() and file_obj.stat().st_size > 0:
                    self.logger.info(f"文件下载成功: {file_path}")
                    return file_path
                else:
                    raise Exception("下载的文件不存在或为空")
                    
            except Exception as e:
                last_error = e
                retry_count += 1
                self.logger.warning(f"文件下载失败 (第{retry_count}次): {str(e)}", extra_data={
                    'exception_type': type(e).__name__,
                    'exception_details': str(e),
                    'retry_count': retry_count,
                    'max_retries': self.config['max_retry_count'],
                    'export_task_id': export_data.get('id'),
                    'file_path': export_data.get('path', '')
                })
                
                if retry_count < self.config['max_retry_count']:
                    self.logger.info(f"等待{self.config['retry_delay']}秒后重试")
                    await asyncio.sleep(self.config['retry_delay'])
        
        raise Exception(f"文件下载最终失败: {last_error}")
    
    async def get_export_list(self, 
                            driver: BaseWebDriver = None,
                            tokens: Dict[str, str] = None) -> List[Dict[str, Any]]:
        """
        获取导出任务列表
        
        Args:
            driver: Web驱动对象 (可选)
            tokens: 认证Token (可选)
            
        Returns:
            List[Dict[str, Any]]: 导出任务列表
        """
        try:
            # 如果没有提供tokens，尝试从驱动提取
            if not tokens and driver:
                tokens = await self._extract_tokens(driver)
            
            if not tokens:
                tokens = self.last_tokens
                
            if not tokens:
                raise Exception("无法获取认证Token")
            
            # 获取今日导出列表
            today = datetime.now().strftime('%Y-%m-%d')
            result = await self.export_client.get_export_list(tokens, created_unix=today)
            export_list = result.get('data_list', {}).get('data', [])
            
            self.logger.info(f"获取到{len(export_list)}个导出任务")
            return export_list
            
        except Exception as e:
            self.logger.error(f"获取导出列表异常: {e}")
            return []
    
    async def check_export_status(self, 
                                export_id: str = None,
                                driver: BaseWebDriver = None,
                                tokens: Dict[str, str] = None) -> Optional[Dict[str, Any]]:
        """
        检查特定导出任务的状态
        
        Args:
            export_id: 导出任务ID (可选)
            driver: Web驱动对象 (可选)
            tokens: 认证Token (可选)
            
        Returns:
            Optional[Dict[str, Any]]: 导出任务信息
        """
        try:
            # 获取导出列表
            export_list = await self.get_export_list(driver, tokens)
            
            if export_id:
                # 查找特定ID的任务
                target_export = next((item for item in export_list 
                                    if item.get('id') == export_id), None)
            else:
                # 获取创建时间最新的导出任务，不考虑状态
                today = datetime.now().strftime('%Y-%m-%d')
                
                # 筛选今日的指定类型导出任务
                filtered_exports = []
                for item in export_list:
                    # 检查日期
                    if item.get('created_unix', '').startswith(today):
                        # 检查数据类型
                        if self.config['data_type_filter'] in item.get('data_type', ''):
                            filtered_exports.append(item)
                
                if filtered_exports:
                    # 按创建时间降序排序，获取最新的
                    target_export = max(filtered_exports, 
                                      key=lambda x: x.get('created_unix', ''))
                else:
                    target_export = None
            
            if target_export:
                self.logger.info(f"导出任务状态: {target_export.get('status')}", extra_data={
                    'task_id': target_export.get('id'),
                    'task_name': target_export.get('task_name'),
                    'created_time': target_export.get('created_unix'),
                    'status': target_export.get('status')
                })
                return target_export
            else:
                self.logger.warning("未找到指定的导出任务")
                return None
                
        except Exception as e:
            self.logger.error(f"检查导出状态异常: {e}")
            return None
    
    def get_export_history(self) -> List[Dict[str, Any]]:
        """
        获取导出历史记录
        
        Returns:
            List[Dict[str, Any]]: 导出历史记录
        """
        return self.export_history.copy()
    
    def clear_export_history(self):
        """清空导出历史记录"""
        self.export_history.clear()
        self.logger.info("导出历史记录已清空")
    
    def update_config(self, config_updates: Dict[str, Any]):
        """
        更新配置参数
        
        Args:
            config_updates: 配置更新字典
        """
        old_config = self.config.copy()
        self.config.update(config_updates)
        
        self.logger.info("配置已更新", extra_data={
            'old_config': old_config,
            'new_config': self.config
        })
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        history = self.export_history
        success_count = len([item for item in history if item.get('success')])
        fail_count = len(history) - success_count
        
        total_files_size = sum(item.get('file_size', 0) for item in history if item.get('success'))
        
        return {
            'total_exports': len(history),
            'success_count': success_count,
            'fail_count': fail_count,
            'success_rate': (success_count / max(len(history), 1)) * 100,
            'total_files_size': total_files_size,
            'last_export_time': history[-1].get('timestamp') if history else None,
            'last_login_time': self.last_login_time.isoformat() if self.last_login_time else None,
            'has_valid_tokens': bool(self.last_tokens)
        }
    
    async def execute_export_and_store_to_db(self, 
                                           driver: BaseWebDriver,
                                           platform_code: str = None,
                                           date_range: Tuple[str, str] = None,
                                           store_to_db: bool = True) -> Dict[str, Any]:
        """
        执行完整的导出并存储到数据库流程（支持临时文件清理）
        
        Args:
            driver: Web驱动对象
            platform_code: 平台代码 (默认AMAZON)
            date_range: 时间范围 (start_date, end_date)，如果为None则使用智能计算
            store_to_db: 是否存储到数据库
            
        Returns:
            Dict[str, Any]: 执行结果（包含数据库操作结果）
        """
        temp_files_before = len(self.temp_files)
        
        try:
            self.logger.info("开始执行导出并存储到数据库流程")
            
            # 参数处理
            platform_code = platform_code or self.config['platform_code']
            
            # 🎯 使用智能时间范围计算
            if not date_range:
                try:
                    date_range = await self.calculate_smart_date_range_async()
                    self.logger.info(f"智能计算时间范围: {date_range[0]} ~ {date_range[1]}")
                except Exception as e:
                    if "跳过任务" in str(e):
                        self.logger.warning(f"任务跳过: {str(e)}")
                        return {
                            'success': True,
                            'skipped': True,
                            'message': str(e),
                            'timestamp': datetime.now().isoformat()
                        }
                    else:
                        raise
            
            # 步骤1: 确保登录状态
            login_success = await self._ensure_login_status(driver)
            if not login_success:
                raise Exception("登录失败")
            
            # 步骤2: 提取认证Token
            tokens = await self._extract_tokens(driver)
            if not tokens:
                raise Exception("Token提取失败")
            
            # 步骤3: 执行导出流程（包含生成失败重试机制）
            export_result = await self._execute_export_with_retry(tokens, platform_code, date_range)
            completed_export = export_result.get('export_data')
            
            if not completed_export:
                raise Exception("导出流程失败，未获取到有效的导出任务数据")
            
            # 记录导出流程结果
            self.logger.info("导出流程完成", extra_data={
                'used_existing': export_result.get('used_existing', False),
                'retry_count': export_result.get('retry_count', 0),
                'task_id': completed_export.get('id'),
                'task_name': completed_export.get('task_name'),
                'status': completed_export.get('status')
            })
            
            # 步骤4: 获取文件内容（内存处理，避免磁盘IO）
            file_content = await self._get_export_file_content(completed_export, tokens)
            file_size = len(file_content)
            
            # 步骤6: 存储到数据库（如果启用）
            db_result = None
            overall_success = True
            message = "导出文件获取完成"
            
            if store_to_db:
                db_result = await self.store_csv_to_database(file_content)
                # 检查数据库操作是否成功
                if db_result and not db_result.get('success', False):
                    overall_success = False
                    message = f"导出成功但数据库存储失败: {db_result.get('message', '未知错误')}"
                else:
                    message = "导出并存储到数据库完成"
            
            # 🔧 步骤7: 清理临时文件（在数据库操作完成后）
            await self._cleanup_temp_files(temp_files_before)
            
            # 记录最终结果（成功取决于所有步骤）
            result = {
                'success': overall_success,
                'message': message,
                'export_info': completed_export,
                'file_size': file_size,
                'platform_code': platform_code,
                'date_range': date_range,
                'db_result': db_result,
                'timestamp': datetime.now().isoformat()
            }
            
            self.export_history.append(result)
            
            # 根据最终成功状态记录日志
            if overall_success:
                self.logger.info("导出并存储流程完成", extra_data={
                    'file_size': file_size,
                    'total_rows': completed_export.get('rows_sum', 0),
                    'db_stored': store_to_db,
                    'db_result': db_result
                })
            else:
                self.logger.error("导出流程部分失败", extra_data={
                    'file_size': file_size,
                    'total_rows': completed_export.get('rows_sum', 0),
                    'db_stored': store_to_db,
                    'db_result': db_result,
                    'failure_reason': message
                })
            
            return result
            
        except Exception as e:
            # 🔧 发生异常时也要清理临时文件
            await self._cleanup_temp_files(temp_files_before)
            
            self.logger.error(f"导出并存储流程失败: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e),
                'platform_code': platform_code,
                'date_range': date_range,
                'store_to_db': store_to_db
            })
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _get_export_file_content(self, 
                                     export_data: Dict[str, Any],
                                     tokens: Dict[str, str]) -> bytes:
        """
        获取导出文件内容（内存处理）
        
        Args:
            export_data: 导出任务数据
            tokens: 认证Token
            
        Returns:
            bytes: 文件内容
        """
        retry_count = 0
        last_error = None
        
        while retry_count < self.config['max_retry_count']:
            try:
                self.logger.info(f"获取导出文件内容 (第{retry_count + 1}次尝试)")
                
                file_content = await self.export_client.get_file_content(
                    export_data=export_data,
                    tokens=tokens
                )
                
                # 验证文件内容
                if file_content and len(file_content) > 0:
                    self.logger.info(f"文件内容获取成功: {len(file_content)} 字节")
                    return file_content
                else:
                    raise Exception("获取的文件内容为空")
                    
            except Exception as e:
                last_error = e
                retry_count += 1
                self.logger.warning(f"文件内容获取失败 (第{retry_count}次): {str(e)}", extra_data={
                    'exception_type': type(e).__name__,
                    'exception_details': str(e),
                    'retry_count': retry_count,
                    'max_retries': self.config['max_retry_count'],
                    'export_task_id': export_data.get('id'),
                    'file_path': export_data.get('path', '')
                })
                
                if retry_count < self.config['max_retry_count']:
                    self.logger.info(f"等待{self.config['retry_delay']}秒后重试")
                    await asyncio.sleep(self.config['retry_delay'])
        
        raise Exception(f"文件内容获取最终失败: {last_error}")
    
    async def execute_smart_export_and_store(self,
                                          platform_code: str = None,
                                          date_range: Tuple[str, str] = None,
                                          tokens: Dict[str, str] = None) -> Dict[str, Any]:
        """
        智能导出并存储到数据库流程（无需Web驱动，支持504错误处理）
        
        优先检查今日已有导出任务，避免重复请求导出。
        特别适用于504错误恢复场景。
        
        Args:
            platform_code: 平台代码 (默认AMAZON)
            date_range: 时间范围 (start_date, end_date)
            tokens: 认证Token (可选，如无则使用上次保存的)
            
        Returns:
            Dict[str, Any]: 执行结果（包含数据库操作结果）
        """
        temp_files_before = len(self.temp_files)
        
        try:
            self.logger.info("开始智能导出并存储到数据库流程")
            
            # 参数处理
            platform_code = platform_code or self.config['platform_code']
            
            # 🎯 使用智能时间范围计算
            if not date_range:
                try:
                    date_range = await self.calculate_smart_date_range_async()
                    self.logger.info(f"智能计算时间范围: {date_range[0]} ~ {date_range[1]}")
                except Exception as e:
                    if "跳过任务" in str(e):
                        self.logger.warning(f"任务跳过: {str(e)}")
                        return {
                            'success': True,
                            'skipped': True,
                            'message': str(e),
                            'smart_mode': True,
                            'timestamp': datetime.now().isoformat()
                        }
                    else:
                        raise
            
            # 步骤1: 获取认证Token
            if not tokens:
                tokens = self.last_tokens
                if not tokens:
                    raise Exception("无法获取认证Token，请先通过Web驱动登录")
            
            # 步骤2: 首先检查今日是否已有完成的导出任务
            # self.logger.info("🔍 首先检查今日是否已有完成的导出任务")
            # existing_export = await self._check_existing_today_export(tokens)
            # 暂时不去检查是否已有导出任务
            existing_export = False
            
            # 步骤3: 没有现有任务，执行导出流程（包含生成失败重试机制）
            self.logger.info("未发现今日完成任务，执行完整导出流程")
            export_result = await self._execute_export_with_retry(tokens, platform_code, date_range)
            completed_export = export_result.get('export_data')
            
            if not completed_export:
                raise Exception("导出流程失败，未获取到有效的导出任务数据")
            
            # 记录导出流程结果
            self.logger.info("导出流程完成", extra_data={
                'used_existing': export_result.get('used_existing', False),
                'retry_count': export_result.get('retry_count', 0),
                'task_id': completed_export.get('id'),
                'task_name': completed_export.get('task_name'),
                'status': completed_export.get('status')
            })
            
            # 步骤5: 获取文件内容（内存处理）
            file_content = await self._get_export_file_content(completed_export, tokens)
            file_size = len(file_content)
            
            # 步骤6: 存储到数据库
            db_result = await self.store_csv_to_database(file_content)
            
            # 检查数据库操作是否成功
            overall_success = db_result and db_result.get('success', False)
            message = "智能模式导出并存储到数据库完成" if overall_success else f"智能模式导出成功但数据库存储失败: {db_result.get('message', '未知错误') if db_result else '数据库操作返回空结果'}"
            
            # 🔧 步骤7: 清理临时文件（在数据库操作完成后）
            await self._cleanup_temp_files(temp_files_before)
            
            # 记录最终结果（成功取决于所有步骤）
            result = {
                'success': overall_success,
                'message': message,
                'export_info': completed_export,
                'file_size': file_size,
                'platform_code': platform_code,
                'date_range': date_range,
                'db_result': db_result,
                'used_existing_export': export_result.get('existing_export_used', False),
                'smart_mode': True,
                'timestamp': datetime.now().isoformat()
            }
            
            self.export_history.append(result)
            
            # 根据最终成功状态记录日志
            if overall_success:
                self.logger.info("智能导出并存储流程完成", extra_data={
                    'file_size': file_size,
                    'total_rows': completed_export.get('rows_sum', 0),
                    'used_existing': export_result.get('existing_export_used', False),
                    'db_result': db_result
                })
            else:
                self.logger.error("智能导出流程部分失败", extra_data={
                    'file_size': file_size,
                    'total_rows': completed_export.get('rows_sum', 0),
                    'used_existing': export_result.get('existing_export_used', False),
                    'db_result': db_result,
                    'failure_reason': message
                })
            
            return result
            
        except Exception as e:
            # 🔧 发生异常时也要清理临时文件
            await self._cleanup_temp_files(temp_files_before)
            
            self.logger.error(f"智能导出并存储流程失败: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e),
                'platform_code': platform_code,
                'date_range': date_range
            })
            return {
                'success': False,
                'error': str(e),
                'smart_mode': True,
                'timestamp': datetime.now().isoformat()
            }

    async def store_csv_to_database(self, csv_content: bytes) -> Dict[str, Any]:
        """
        将CSV内容存储到数据库
        
        Args:
            csv_content: CSV文件字节内容
            
        Returns:
            Dict[str, Any]: 数据库操作结果
        """
        try:
            self.logger.info("开始将CSV内容存储到数据库")
            
            # 导入处理器
            from .csv_processor import CSVProcessor
            from .db_operations import PublishSuccessDBOperator
            
            # 初始化处理器
            csv_processor = CSVProcessor(self.logger)
            db_operator = PublishSuccessDBOperator(
                business_type=self.business_type,
                script_name=self.script_name,
                logger=self.logger
            )
            
            # 步骤1: 解析CSV内容
            self.logger.info("解析CSV内容")
            records = csv_processor.parse_csv_content(csv_content)
            
            if not records:
                return {
                    'success': False,
                    'message': 'CSV内容解析失败或没有有效记录',
                    'total_records': 0,
                    'insert_count': 0,
                    'update_count': 0
                }
            
            self.logger.info(f"CSV解析完成，共{len(records)}条记录")
            
            # 步骤2: 处理记录（自动识别新增/更新并执行）
            # 🔧 支持强制更新模式 - 强制更新所有已存在记录，不检查变化
            force_update = True  # 🎯 用户需求：强制更新所有记录
            mode_text = "强制更新模式" if force_update else "智能变化检测模式"
            self.logger.info(f"开始数据库批量操作 ({mode_text})")
            db_result = db_operator.process_records(records, force_update=force_update)
            
            # 步骤3: 获取表统计信息
            statistics = db_operator.get_table_statistics()
            
            # 合并结果
            final_result = {
                **db_result,
                'csv_records_count': len(records),
                'table_statistics': statistics,
                'processing_time': datetime.now().isoformat()
            }
            
            if db_result['success']:
                self.logger.info("CSV数据成功存储到数据库", extra_data=final_result)
            else:
                self.logger.error("CSV数据存储到数据库失败", extra_data=final_result)
            
            return final_result
            
        except Exception as e:
            result = {
                'success': False,
                'message': f'CSV存储到数据库失败: {str(e)}',
                'error': str(e),
                'total_records': 0,
                'insert_count': 0,
                'update_count': 0
            }
            
            self.logger.error("CSV存储到数据库异常", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e),
                'result': result
            })
            
            return result

    async def _cleanup_temp_files(self, files_before_count: int = 0) -> None:
        """
        清理临时文件
        
        Args:
            files_before_count: 操作前的临时文件数量（只清理操作后新增的文件）
        """
        if not self.config.get('auto_cleanup', True):
            self.logger.debug("自动清理功能已禁用，跳过临时文件清理")
            return
        
        try:
            # 获取需要清理的文件列表（只清理新增的文件）
            files_to_cleanup = self.temp_files[files_before_count:]
            
            if not files_to_cleanup:
                self.logger.debug("没有临时文件需要清理")
                return
            
            self.logger.info(f"开始清理 {len(files_to_cleanup)} 个临时文件")
            
            cleaned_count = 0
            failed_count = 0
            
            for file_path in files_to_cleanup:
                try:
                    file_obj = Path(file_path)
                    if file_obj.exists():
                        file_size = file_obj.stat().st_size
                        file_obj.unlink()  # 删除文件
                        cleaned_count += 1
                        self.logger.debug(f"已清理临时文件: {file_path} ({file_size} 字节)")
                    else:
                        self.logger.debug(f"临时文件不存在，跳过: {file_path}")
                        
                except Exception as e:
                    failed_count += 1
                    self.logger.warning(f"清理临时文件失败: {file_path}, 错误: {str(e)}")
            
            # 从跟踪列表中移除已处理的文件
            self.temp_files = self.temp_files[:files_before_count]
            
            self.logger.info(f"临时文件清理完成", extra_data={
                'total_files': len(files_to_cleanup),
                'cleaned_count': cleaned_count,
                'failed_count': failed_count,
                'remaining_temp_files': len(self.temp_files)
            })
            
        except Exception as e:
            self.logger.error(f"临时文件清理过程异常: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'files_before_count': files_before_count,
                'total_temp_files': len(self.temp_files)
            })