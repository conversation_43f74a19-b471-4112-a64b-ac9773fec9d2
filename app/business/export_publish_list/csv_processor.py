"""
CSV数据处理器

负责处理导出的CSV文件：
1. 智能编码检测和解析
2. 字段映射（中文→英文）
3. 数据验证和清洗
4. 转换为数据库记录格式
"""

import csv
import io
import chardet
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime


class CSVProcessor:
    """CSV数据处理器"""
    
    def __init__(self, logger: logging.Logger = None):
        """
        初始化CSV处理器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 中文字段到英文字段的映射
        self.field_mapping = {
            '平台': 'platform',
            '来源': 'source',
            'SKU': 'sku',
            'SPU': 'spu',
            'sellerSKU（父）': 'seller_sku_parent',
            'sellerSKU（子）': 'seller_sku_child',
            'Asin': 'asin',
            'UPC': 'upc',
            'EAN': 'ean',
            '品牌': 'brand',
            '零件号': 'part_number',
            '变体类型': 'variant_type',
            '变体名称1': 'variant_name_1',
            '变体名称2': 'variant_name_2',
            '变体名称3': 'variant_name_3',
            '价格（本币）': 'price_local_currency',
            '数量': 'quantity',
            '账号': 'account',
            '站点': 'site',
            '大仓': 'warehouse',
            'nodeID': 'node_id',
            '平台类目链': 'platform_category_path',
            '产品线': 'product_line',
            '商品类型': 'product_type',
            '中文标题': 'chinese_title',
            '销售员': 'sales_person',
            '平台返回信息': 'platform_return_message',
            '链接在线状态': 'link_online_status',
            '创建人': 'creator',
            '创建时间': 'create_time',
            '最后拉取时间': 'last_fetch_time',
            '同步时间': 'sync_time'
        }
        
        # 字段验证规则
        self.field_validators = {
            'seller_sku_child': self._validate_required_field,
            'account': self._validate_required_field
            # 'platform': self._validate_required_field,
            # 'source': self._validate_required_field,
            # 'brand': self._validate_required_field,
            # 'site': self._validate_required_field
        }
        
        self.logger.info("CSV处理器初始化完成", extra_data={
            'supported_fields': len(self.field_mapping),
            'validators': len(self.field_validators)
        })
    
    def detect_encoding(self, content: bytes) -> str:
        """
        检测文件编码（增强版，支持错误字节处理）
        
        Args:
            content: 文件字节内容
            
        Returns:
            str: 检测到的编码格式
        """
        try:
            # 使用chardet检测编码
            detection = chardet.detect(content)
            detected_encoding = detection.get('encoding', 'utf-8')
            confidence = detection.get('confidence', 0)
            
            self.logger.info(f"编码检测完成", extra_data={
                'detected_encoding': detected_encoding,
                'confidence': confidence,
                'content_size': len(content)
            })
            
            # 优先级编码列表（根据实际情况调整）
            encoding_candidates = []
            
            # 如果检测结果置信度高，优先使用
            if confidence >= 0.7 and detected_encoding:
                encoding_candidates.append(detected_encoding)
            
            # 添加常见的中文编码
            encoding_candidates.extend(['gbk', 'gb18030', 'utf-8', 'gb2312', 'cp936', 'cp1252'])
            
            # 尝试每种编码
            for encoding in encoding_candidates:
                if not encoding:
                    continue
                    
                try:
                    # 尝试解码全部内容
                    decoded_text = content.decode(encoding)
                    self.logger.info(f"成功使用编码: {encoding}")
                    return encoding
                except UnicodeDecodeError as e:
                    self.logger.debug(f"编码 {encoding} 解码失败: {str(e)}")
                    continue
                except Exception as e:
                    self.logger.debug(f"编码 {encoding} 处理异常: {str(e)}")
                    continue
            
            # 如果所有编码都失败，使用错误处理方式
            self.logger.warning("所有编码尝试失败，使用UTF-8和错误处理")
            return 'utf-8'
            
        except Exception as e:
            self.logger.warning(f"编码检测失败，使用默认UTF-8: {str(e)}")
            return 'utf-8'
    
    def parse_csv_content(self, content: bytes) -> List[Dict[str, Any]]:
        """
        解析CSV内容（增强版，支持编码错误处理）
        
        Args:
            content: CSV文件字节内容
            
        Returns:
            List[Dict[str, Any]]: 解析后的记录列表
        """
        try:
            # 检测并解码内容
            encoding = self.detect_encoding(content)
            
            # 尝试解码，使用错误处理机制
            text_content = None
            for error_strategy in ['strict', 'ignore', 'replace']:
                try:
                    text_content = content.decode(encoding, errors=error_strategy)
                    if error_strategy != 'strict':
                        self.logger.warning(f"使用编码错误处理策略: {error_strategy}")
                    break
                except UnicodeDecodeError as e:
                    if error_strategy == 'replace':
                        self.logger.error(f"所有解码策略失败: {str(e)}")
                        raise
                    continue
            
            if not text_content:
                raise Exception("无法解码CSV内容")
            
            self.logger.info(f"开始解析CSV内容", extra_data={
                'encoding': encoding,
                'content_length': len(text_content),
                'original_size': len(content)
            })
            
            # 使用StringIO创建文件对象
            csv_file = io.StringIO(text_content)
            
            # 读取CSV数据
            csv_reader = csv.DictReader(csv_file)
            
            records = []
            row_count = 0
            error_count = 0
            
            for row_num, row in enumerate(csv_reader, start=1):
                try:
                    # 转换字段名和清洗数据
                    converted_row = self._convert_and_clean_row(row, row_num)
                    
                    if converted_row:
                        records.append(converted_row)
                        row_count += 1
                    else:
                        error_count += 1
                        
                except Exception as e:
                    error_count += 1
                    self.logger.warning(f"第{row_num}行数据处理失败: {str(e)}", extra_data={
                        'row_number': row_num,
                        'error': str(e)
                    })
            
            self.logger.info(f"CSV解析完成", extra_data={
                'total_rows': row_count + error_count,
                'success_rows': row_count,
                'error_rows': error_count,
                'success_rate': f"{(row_count/(row_count+error_count)*100):.1f}%" if (row_count+error_count) > 0 else "0%"
            })
            
            # 对CSV数据进行去重处理（基于seller_sku_child+account唯一约束）
            deduplicated_records = self._deduplicate_csv_records(records)
            
            return deduplicated_records
            
        except Exception as e:
            self.logger.error(f"CSV解析失败: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'content_size': len(content),
                'error_details': str(e)
            })
            raise
    
    def _convert_and_clean_row(self, row: Dict[str, str], row_num: int) -> Optional[Dict[str, Any]]:
        """
        转换和清洗单行数据
        
        Args:
            row: 原始行数据
            row_num: 行号
            
        Returns:
            Optional[Dict[str, Any]]: 转换后的行数据，如果验证失败返回None
        """
        try:
            converted_row = {}
            
            # 字段映射和清洗
            for chinese_field, english_field in self.field_mapping.items():
                raw_value = row.get(chinese_field, '')
                
                # 清洗数据（传入字段名用于特殊处理）
                cleaned_value = self._clean_field_value(raw_value, english_field)
                converted_row[english_field] = cleaned_value
            
            # 添加处理时间戳
            converted_row['processed_at'] = datetime.now().isoformat()
            converted_row['source_row_number'] = row_num
            
            # 数据验证
            validation_result = self._validate_row(converted_row, row_num)
            if not validation_result['valid']:
                self.logger.warning(f"第{row_num}行数据验证失败: {validation_result['errors']}")
                return None
            
            return converted_row
            
        except Exception as e:
            self.logger.warning(f"第{row_num}行数据转换失败: {str(e)}")
            return None
    
    def _clean_field_value(self, value: str, field_name: str = None) -> Any:
        """
        清洗字段值（增强版：支持数据类型转换）
        
        Args:
            value: 原始字段值
            field_name: 字段名称（用于特殊处理）
            
        Returns:
            Any: 清洗后的字段值（字符串或None）
        """
        if not isinstance(value, str):
            value = str(value) if value is not None else ''
        
        # 去除首尾空白
        value = value.strip()
        
        # 如果值为空，针对不同类型字段进行处理
        if not value:
            # 日期时间字段：空值返回None（对应数据库NULL）
            if field_name in ['create_time', 'last_fetch_time', 'sync_time']:
                return None
            # 其他字段：返回空字符串
            return ''
        
        # 日期时间字段的特殊处理
        if field_name in ['create_time', 'last_fetch_time', 'sync_time']:
            return self._convert_datetime_field(value, field_name)
        
        # 处理特殊字符
        value = value.replace('\r\n', ' ').replace('\n', ' ').replace('\r', ' ')
        
        # 去除多余空格
        value = ' '.join(value.split())
        
        # 限制长度（防止数据库字段溢出）
        if len(value) > 10000:
            value = value[:10000]
            self.logger.debug(f"字段值过长已截断: {value[:200]}...")
        
        return value
    
    def _convert_datetime_field(self, value: str, field_name: str) -> Optional[str]:
        """
        转换日期时间字段
        
        Args:
            value: 原始日期时间值
            field_name: 字段名称
            
        Returns:
            Optional[str]: 转换后的日期时间字符串或None
        """
        if not value or value.strip() == '':
            return None
        
        try:
            # 尝试解析各种日期格式
            date_formats = [
                '%Y-%m-%d %H:%M:%S',  # 2024-06-30 15:30:00
                '%Y/%m/%d %H:%M:%S',  # 2024/06/30 15:30:00
                '%Y-%m-%d',           # 2024-06-30
                '%Y/%m/%d',           # 2024/06/30
                '%m/%d/%Y %H:%M:%S',  # 06/30/2024 15:30:00
                '%m/%d/%Y',           # 06/30/2024
                '%d/%m/%Y %H:%M:%S',  # 30/06/2024 15:30:00
                '%d/%m/%Y',           # 30/06/2024
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(value.strip(), fmt)
                    # 返回MySQL标准格式
                    return parsed_date.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue
            
            # 如果所有格式都失败，记录警告并返回None
            self.logger.warning(f"无法解析日期时间字段 {field_name}: '{value}'，将设为NULL")
            return None
            
        except Exception as e:
            self.logger.warning(f"日期时间字段 {field_name} 转换异常: {str(e)}，原值: '{value}'")
            return None
    
    def _validate_row(self, row: Dict[str, Any], row_num: int) -> Dict[str, Any]:
        """
        验证行数据
        
        Args:
            row: 转换后的行数据
            row_num: 行号
            
        Returns:
            Dict[str, Any]: 验证结果 {'valid': bool, 'errors': List[str]}
        """
        errors = []
        
        # 执行字段验证
        for field, validator in self.field_validators.items():
            try:
                if not validator(row.get(field, '')):
                    errors.append(f"{field}字段验证失败")
            except Exception as e:
                errors.append(f"{field}字段验证异常: {str(e)}")
        
        # 业务逻辑验证
        # 检查唯一索引字段
        if not row.get('seller_sku_child') or not row.get('account'):
            errors.append("缺少唯一索引字段(seller_sku_child或account)")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _validate_required_field(self, value: str) -> bool:
        """
        验证必填字段
        
        Args:
            value: 字段值
            
        Returns:
            bool: 是否通过验证
        """
        return bool(value and value.strip())
    
    def get_unique_key(self, record: Dict[str, Any]) -> str:
        """
        获取记录的唯一键（增强版：统一字段值标准化）
        
        Args:
            record: 数据记录
            
        Returns:
            str: 唯一键字符串
        """
        # 🔧 使用统一的字段值标准化处理
        seller_sku = self._normalize_field_value(record.get('seller_sku_child', ''))
        account = self._normalize_field_value(record.get('account', ''))
        
        # 🔧 确保字段值不为空
        if not seller_sku or not account:
            self.logger.warning(f"唯一键字段值为空", extra_data={
                'seller_sku_child': seller_sku,
                'account': account,
                'record_sample': {k: v for k, v in list(record.items())[:3]}
            })
        
        return f"{seller_sku}#{account}"
    
    def group_records_by_operation(self, records: List[Dict[str, Any]], 
                                 existing_records: List[Dict[str, Any]], 
                                 force_update: bool = False) -> Dict[str, List[Dict[str, Any]]]:
        """
        根据操作类型分组记录（新增vs更新）- 增强版支持强制更新
        
        Args:
            records: 新的记录列表
            existing_records: 数据库中已存在的记录列表
            force_update: 是否强制更新所有已存在记录
            
        Returns:
            Dict[str, List]: {'insert': [...], 'update': [...]}
        """
        # 构建已存在记录的键集合
        existing_keys = set()
        existing_lookup = {}
        
        for existing_record in existing_records:
            key = self.get_unique_key(existing_record)
            existing_keys.add(key)
            existing_lookup[key] = existing_record
        
        # 🔧 调试信息：显示已存在记录的唯一键样例
        sample_existing_keys = list(existing_keys)[:5]
        self.logger.info(f"已存在记录唯一键样例: {sample_existing_keys}", extra_data={
            'existing_keys_count': len(existing_keys),
            'existing_records_count': len(existing_records)
        })
        
        insert_records = []
        update_records = []
        skip_count = 0
        
        # 🔧 调试计数器
        match_found_count = 0
        match_not_found_count = 0
        
        for record in records:
            key = self.get_unique_key(record)
            
            if key in existing_keys:
                match_found_count += 1
                # 需要更新 - 检查是否真的有变化或强制更新
                existing_record = existing_lookup[key]
                if self._record_has_changes(record, existing_record, force_update):
                    # 添加ID用于更新
                    record['id'] = existing_record.get('id')
                    update_records.append(record)
                else:
                    # 没有变化，跳过
                    skip_count += 1
            else:
                match_not_found_count += 1
                # 需要新增
                insert_records.append(record)
        
        # 🔧 调试信息：显示匹配统计
        self.logger.info(f"唯一键匹配统计", extra_data={
            'total_csv_records': len(records),
            'match_found_count': match_found_count,
            'match_not_found_count': match_not_found_count,
            'existing_keys_count': len(existing_keys),
            'match_rate': f"{(match_found_count/len(records)*100):.1f}%" if len(records) > 0 else "0%"
        })
        
        # 🚨 如果匹配率异常低，显示详细调试信息
        if len(records) > 0 and match_found_count / len(records) < 0.1:  # 匹配率低于10%
            self.logger.warning("⚠️ 唯一键匹配率异常低，显示详细调试信息")
            
            # 显示前5个CSV记录的唯一键
            sample_csv_keys = [self.get_unique_key(record) for record in records[:5]]
            self.logger.warning(f"CSV记录唯一键样例: {sample_csv_keys}")
            
            # 显示前5个已存在记录的唯一键
            sample_existing_keys = list(existing_keys)[:5]
            self.logger.warning(f"数据库记录唯一键样例: {sample_existing_keys}")
            
            # 检查是否有格式不匹配问题
            if sample_csv_keys and sample_existing_keys:
                csv_key = sample_csv_keys[0]
                existing_key = sample_existing_keys[0]
                if '#' in csv_key and '#' in existing_key:
                    csv_parts = csv_key.split('#')
                    existing_parts = existing_key.split('#')
                    self.logger.warning(f"键格式比较 - CSV: {csv_parts}, 数据库: {existing_parts}")
                    
                    # 检查字段值是否存在差异
                    if len(csv_parts) == 2 and len(existing_parts) == 2:
                        if csv_parts[0] != existing_parts[0] or csv_parts[1] != existing_parts[1]:
                            self.logger.error(f"🚨 字段值不匹配！CSV: seller_sku='{csv_parts[0]}', account='{csv_parts[1]}' vs 数据库: seller_sku='{existing_parts[0]}', account='{existing_parts[1]}'")
                            
                            # 进一步检查是否是编码或空白字符问题
                            self.logger.error(f"字段长度比较 - CSV: seller_sku长度={len(csv_parts[0])}, account长度={len(csv_parts[1])} vs 数据库: seller_sku长度={len(existing_parts[0])}, account长度={len(existing_parts[1])}")
        
        # 详细日志记录
        mode_desc = "强制更新模式" if force_update else "智能变化检测模式"
        self.logger.info(f"记录分组完成 ({mode_desc})", extra_data={
            'total_records': len(records),
            'insert_count': len(insert_records),
            'update_count': len(update_records),
            'skip_count': skip_count,
            'force_update': force_update,
            'existing_records_count': len(existing_records)
        })
        
        return {
            'insert': insert_records,
            'update': update_records
        }
    
    def _record_has_changes(self, new_record: Dict[str, Any], 
                          existing_record: Dict[str, Any], force_update: bool = False) -> bool:
        """
        检查记录是否有变化（增强版：支持强制更新和智能比较）
        
        Args:
            new_record: 新记录
            existing_record: 已存在的记录
            force_update: 是否强制更新（忽略变化检测）
            
        Returns:
            bool: 是否有变化或需要更新
        """
        # 强制更新模式：直接返回True
        if force_update:
            return True
        
        # 比较关键字段（排除ID和时间戳字段）
        compare_fields = [field for field in self.field_mapping.values() 
                         if field not in ['id', 'created_at', 'updated_at', 'processed_at', 'source_row_number']]
        
        changes_detected = []
        
        for field in compare_fields:
            # 智能值处理：正确处理None和空字符串
            new_value = self._normalize_field_value(new_record.get(field))
            existing_value = self._normalize_field_value(existing_record.get(field))
            
            if new_value != existing_value:
                changes_detected.append({
                    'field': field,
                    'old_value': existing_value,
                    'new_value': new_value
                })
        
        # 记录变化详情（便于调试）
        if changes_detected:
            self.logger.debug(f"检测到记录变化", extra_data={
                'unique_key': self.get_unique_key(new_record),
                'changes_count': len(changes_detected),
                'changes': changes_detected[:5]  # 只显示前5个变化
            })
            return True
        
        return False
    
    def _normalize_field_value(self, value: Any) -> str:
        """
        标准化字段值用于比较
        
        Args:
            value: 字段值
            
        Returns:
            str: 标准化后的字符串值
        """
        # None值处理
        if value is None:
            return ''
        
        # 转换为字符串并清理
        str_value = str(value).strip()
        
        # 处理特殊值
        if str_value.lower() in ['none', 'null', 'undefined']:
            return ''
        
        return str_value
    
    def _deduplicate_csv_records(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对CSV记录进行去重处理（基于seller_sku_child+account唯一约束）
        
        Args:
            records: 原始记录列表
            
        Returns:
            List[Dict[str, Any]]: 去重后的记录列表
        """
        if not records:
            return records
        
        original_count = len(records)
        self.logger.info(f"开始CSV数据去重处理", extra_data={
            'original_count': original_count
        })
        
        # 使用字典存储唯一记录，key为唯一键，value为记录
        # 由于字典会保持插入顺序，后插入的值会覆盖前面的，实现"后面覆盖前面"的效果
        unique_records = {}
        duplicate_count = 0
        
        for i, record in enumerate(records):
            # 获取唯一键
            unique_key = self.get_unique_key(record)
            
            # 检查是否为重复记录
            if unique_key in unique_records:
                duplicate_count += 1
                original_row = unique_records[unique_key].get('source_row_number', 'unknown')
                current_row = record.get('source_row_number', 'unknown')
                
                self.logger.debug(f"发现重复记录，键: {unique_key}", extra_data={
                    'original_row': original_row,
                    'current_row': current_row,
                    'action': '用当前记录覆盖原记录'
                })
            
            # 存储记录（如果是重复的，会覆盖之前的记录）
            unique_records[unique_key] = record
        
        # 转换为列表
        deduplicated_records = list(unique_records.values())
        final_count = len(deduplicated_records)
        
        # 记录去重统计信息
        self.logger.info(f"CSV数据去重完成", extra_data={
            'original_count': original_count,
            'final_count': final_count,
            'duplicate_count': duplicate_count,
            'deduplication_rate': f"{(duplicate_count/original_count*100):.1f}%" if original_count > 0 else "0%",
            'unique_keys_count': len(unique_records)
        })
        
        # 如果有重复数据，提供详细信息
        if duplicate_count > 0:
            self.logger.warning(f"检测到 {duplicate_count} 条重复记录已被去除", extra_data={
                'duplicate_percentage': f"{(duplicate_count/original_count*100):.1f}%",
                'deduplication_strategy': '后面的记录覆盖前面的记录'
            })
        
        return deduplicated_records 