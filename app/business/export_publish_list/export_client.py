"""
亿迈系统导出HTTP客户端

负责所有导出相关的HTTP接口调用：
1. 导出请求接口 (publish_success_export)
2. 导出管理接口 (common_system_export_list)  
3. 文件下载接口
4. 轮询等待和状态检查
"""

import asyncio
import aiohttp
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import urlencode, quote
from pathlib import Path
import tempfile


class ExportClient:
    """
    亿迈系统导出HTTP客户端
    
    提供完整的导出功能：
    - 触发导出任务
    - 查询导出状态
    - 下载导出文件
    """
    
    def __init__(self, logger: logging.Logger = None):
        """
        初始化导出客户端
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 接口配置
        self.base_url = "https://salecentersaasapi.yibainetwork.com"
        self.export_url = f"{self.base_url}/publish/publish_success/publish_success_export"
        self.management_url = f"{self.base_url}/common/common_system_export/common_system_export_list"
        
        # 请求配置
        self.timeout = aiohttp.ClientTimeout(total=60)
        self.max_retries = 3
        self.retry_delay = 2
        
        # 轮询配置
        self.poll_interval = 60  # 轮询间隔(秒) - 1分钟
        self.max_poll_time = 5400  # 最大轮询时间(秒) - 90分钟
        
        self.logger.info("导出客户端初始化完成", extra_data={
            'base_url': self.base_url,
            'poll_interval': self.poll_interval,
            'max_poll_time': self.max_poll_time
        })
    
    async def request_export(self, 
                           platform_code: str = "AMAZON",
                           date_range: tuple = None,
                           tokens: Dict[str, str] = None) -> Dict[str, Any]:
        """
        请求导出数据
        
        Args:
            platform_code: 平台代码 (默认AMAZON)
            date_range: 时间范围 (start_date, end_date)
            tokens: 认证Token字典
            
        Returns:
            Dict[str, Any]: 导出请求结果
        """
        try:
            self.logger.info(f"开始请求导出数据", extra_data={
                'platform_code': platform_code,
                'date_range': date_range
            })
            
            # 构建请求数据
            payload = self._build_export_payload(platform_code, date_range, tokens)
            headers = self._build_export_headers(tokens)
            
            # 发送请求
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.post(
                    self.export_url,
                    data=payload,
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        self.logger.info("导出请求成功", extra_data={
                            'status': result.get('status'),
                            'message': result.get('error_mess')
                        })
                        return result
                    else:
                        error_text = await response.text()
                        raise Exception(f"导出请求失败: {response.status}, {error_text}")
                        
        except Exception as e:
            # 特殊处理不同类型的异常
            if isinstance(e, asyncio.TimeoutError):
                exception_details = f"请求超时 (超过{self.timeout.total}秒)"
            elif hasattr(e, 'message'):
                exception_details = e.message
            else:
                exception_details = str(e) if str(e) else f"{type(e).__name__} occurred"
                
            self.logger.error(f"导出请求异常: {exception_details}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': exception_details,
                'exception_args': str(e.args) if hasattr(e, 'args') else 'N/A',
                'platform_code': platform_code,
                'date_range': date_range,
                'url': self.export_url,
                'timeout_setting': f"{self.timeout.total}秒"
            })
            raise
    
    def _build_export_payload(self, 
                             platform_code: str,
                             date_range: tuple,
                             tokens: Dict[str, str]) -> str:
        """
        构建导出请求payload
        
        Args:
            platform_code: 平台代码
            date_range: 时间范围
            tokens: 认证Token
            
        Returns:
            str: URL编码的payload
        """
        # 处理时间范围
        if not date_range:
            # 默认为今天
            today = datetime.now()
            start_date = today.strftime('%Y-%m-%d 00:00:00')
            end_date = today.strftime('%Y-%m-%d 23:59:59')
        else:
            start_date, end_date = date_range
        
        # 构建payload数据
        data = {
            'platform_code': platform_code,
            'created_unix[0]': start_date,
            'created_unix[1]': end_date,
            'export_form': '1',
            'jwt_token': tokens.get('jwt_token', ''),
            'anticlimb_verify_code': tokens.get('anticlimb_verify_code', '')
        }
        
        # URL编码
        return urlencode(data)
    
    def _build_export_headers(self, tokens: Dict[str, str]) -> Dict[str, str]:
        """
        构建导出请求headers
        
        Args:
            tokens: 认证Token
            
        Returns:
            Dict[str, str]: 请求头
        """
        return {
            'authority': 'salecentersaasapi.yibainetwork.com',
            'method': 'POST',
            'path': '/publish/publish_success/publish_success_export',
            'scheme': 'https',
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
            'currentroute': '/publish_success_list',
            'devicenumber': tokens.get('device_number', ''),
            'loadingtype': 'showLoading',
            'origin': 'https://salecentersaas.yibainetwork.com',
            'priority': 'u=1, i',
            'referer': 'https://salecentersaas.yibainetwork.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not(A:Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'token1-check': tokens.get('token1_check', ''),
            'token2': tokens.get('token2', ''),
            'token2-timestamp': tokens.get('token2_timestamp', ''),
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
    
    async def get_export_list(self, 
                             tokens: Dict[str, str],
                             page: int = 1,
                             limit: int = 20,
                             created_unix: str = None) -> Dict[str, Any]:
        """
        获取导出管理列表
        
        Args:
            tokens: 认证Token
            page: 页码
            limit: 每页数量
            created_unix: 创建时间筛选（YYYY-MM-DD格式），如果为None则使用今天
            
        Returns:
            Dict[str, Any]: 导出列表数据
        """
        try:
            # 如果没有指定创建时间，使用今天
            if not created_unix:
                created_unix = datetime.now().strftime('%Y-%m-%d')
            
            self.logger.info(f"获取导出管理列表", extra_data={
                'page': page,
                'limit': limit,
                'created_unix': created_unix
            })
            
            # 构建请求数据
            payload = self._build_management_payload(tokens, page, limit, created_unix)
            headers = self._build_management_headers(tokens)
            self.logger.debug("发送的请求数据", extra_data={
                'payload': payload,
                'headers': headers
            })
            # 发送请求
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.post(
                    self.management_url,
                    data=payload, 
                    headers=headers
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        self.logger.info("获取导出列表成功", extra_data={
                            'total_count': result.get('data_list', {}).get('page_info', {}).get('total', 0)
                        })
                        return result
                    else:
                        error_text = await response.text()
                        raise Exception(f"获取导出列表失败: {response.status}, {error_text}")
                        
        except Exception as e:
            self.logger.error(f"获取导出列表异常: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e),
                'page': page,
                'limit': limit
            })
            raise
    
    def _build_management_payload(self, 
                                 tokens: Dict[str, str],  
                                 page: int,
                                 limit: int,
                                 created_unix: str = None) -> str:
        """
        构建导出管理请求payload
        
        Args:
            tokens: 认证Token
            page: 页码  
            limit: 每页数量
            created_unix: 创建时间（YYYY-MM-DD格式）
            
        Returns:
            str: URL编码的payload
        """
        # 基础数据
        data = {
            'page': str(page),
            'limit': str(limit),
            'platform_code': 'AMAZON',
            'task_name': '刊登成功列表',  # 🎯 添加任务名称筛选，确保找到的是刊登成功列表的任务
            'jwt_token': tokens.get('jwt_token', ''),
            'anticlimb_verify_code': tokens.get('anticlimb_verify_code', '')
        }
        
        # 添加创建时间筛选（设置为一天的开始和结束）
        if created_unix:
            data['created_unix[0]'] = f"{created_unix} 00:00:00"
            data['created_unix[1]'] = f"{created_unix} 23:59:59"
        
        return urlencode(data)
    
    def _build_management_headers(self, tokens: Dict[str, str]) -> Dict[str, str]:
        """
        构建导出管理请求headers
        
        Args:
            tokens: 认证Token
            
        Returns:
            Dict[str, str]: 请求头
        """
        return {
            'authority': 'salecentersaasapi.yibainetwork.com',
            'method': 'POST',
            'path': '/common/common_system_export/common_system_export_list',
            'scheme': 'https',
            'accept': 'application/json, text/plain, */*',
            'accept-encoding': 'gzip, deflate, br, zstd',
            'accept-language': 'zh-CN,zh;q=0.9',
            'content-type': 'application/x-www-form-urlencoded;charset=UTF-8',
            'currentroute': '/system_export_management',
            'devicenumber': tokens.get('device_number', ''),
            'loadingtype': 'showLoading',
            'origin': 'https://salecentersaas.yibainetwork.com',
            'priority': 'u=1, i',
            'referer': 'https://salecentersaas.yibainetwork.com/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not(A:Brand";v="24"',
            'sec-ch-ua-mobile': '?0', 
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'token1-check': tokens.get('token1_check', ''),
            'token2': tokens.get('token2', ''),
            'token2-timestamp': tokens.get('token2_timestamp', ''),
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
    
    def filter_latest_export(self, 
                           export_list: List[Dict[str, Any]],
                           target_date: str = None,
                           data_type_filter: str = "【刊登成功列表】-列表导出") -> Optional[Dict[str, Any]]:
        """
        筛选最新的导出任务
        
        Args:
            export_list: 导出任务列表
            target_date: 目标日期 (YYYY-MM-DD格式)
            data_type_filter: 数据类型筛选
            
        Returns:
            Optional[Dict[str, Any]]: 最新的导出任务
        """
        if not target_date:
            target_date = datetime.now().strftime('%Y-%m-%d')
        
        # 筛选今日的指定类型导出任务
        filtered_exports = []
        for item in export_list:
            # 检查日期
            if item.get('created_unix', '').startswith(target_date):
                # 检查数据类型
                if data_type_filter in item.get('data_type', ''):
                    filtered_exports.append(item)
        
        if not filtered_exports:
            self.logger.warning(f"未找到 {target_date} 的导出任务")
            return None
        
        # 按创建时间降序排序，获取最新的
        latest_export = max(filtered_exports, 
                           key=lambda x: x.get('created_unix', ''))
        
        self.logger.info(f"找到最新导出任务", extra_data={
            'task_id': latest_export.get('id'),
            'task_name': latest_export.get('task_name'),
            'status': latest_export.get('status'),
            'created_time': latest_export.get('created_unix')
        })
        
        return latest_export
    
    async def wait_for_export_completion(self,
                                       tokens: Dict[str, str],
                                       export_id: str = None,
                                       target_date: str = None) -> Optional[Dict[str, Any]]:
        """
        轮询等待导出完成
        
        Args:
            tokens: 认证Token
            export_id: 导出任务ID (可选)
            target_date: 目标日期 (可选)
            
        Returns:
            Optional[Dict[str, Any]]: 完成的导出任务
        """
        start_time = time.time()
        poll_count = 0
        
        self.logger.info("开始轮询等待导出完成", extra_data={
            'export_id': export_id,
            'target_date': target_date,
            'max_poll_time': self.max_poll_time
        })
        
        while time.time() - start_time < self.max_poll_time:
            try:
                poll_count += 1
                self.logger.info(f"第 {poll_count} 次轮询检查导出状态")
                
                # 获取今日导出列表
                today = datetime.now().strftime('%Y-%m-%d')
                export_result = await self.get_export_list(tokens, created_unix=today)
                export_list = export_result.get('data_list', {}).get('data', [])
                
                if export_id:
                    # 按ID查找特定任务
                    target_export = next((item for item in export_list 
                                        if item.get('id') == export_id), None)
                else:
                    # 查找最新的导出任务
                    target_export = self.filter_latest_export(export_list, target_date)
                
                if target_export:
                    status = target_export.get('status', '')
                    self.logger.info(f"导出任务状态: {status}")
                    
                    if status == '生成完成':
                        self.logger.info("导出任务完成!", extra_data={
                            'task_id': target_export.get('id'),
                            'task_name': target_export.get('task_name'),
                            'total_rows': target_export.get('rows_sum'),
                            'file_path': target_export.get('path')
                        })
                        return target_export
                    elif status == '生成失败':
                        # 🎯 特别处理生成失败状态，记录失败并抛出特定异常以便重试
                        self.logger.warning("导出任务生成失败", extra_data={
                            'task_id': target_export.get('id'),
                            'task_name': target_export.get('task_name'),
                            'status': status,
                            'created_time': target_export.get('created_unix')
                        })
                        # 抛出特定异常，标记为生成失败，可以重试
                        raise Exception(f"EXPORT_GENERATION_FAILED:{status}")
                    elif '失败' in status or '错误' in status:
                        # 其他类型的失败
                        self.logger.error("导出任务其他失败", extra_data={
                            'task_id': target_export.get('id'),
                            'task_name': target_export.get('task_name'),
                            'status': status
                        })
                        raise Exception(f"导出任务失败: {status}")
                
                # 等待下次轮询
                await asyncio.sleep(self.poll_interval)
                
            except Exception as e:
                self.logger.error(f"轮询检查异常: {str(e)}", extra_data={
                    'exception_type': type(e).__name__,
                    'exception_details': str(e),
                    'poll_count': poll_count,
                    'elapsed_time': time.time() - start_time
                })
                await asyncio.sleep(self.poll_interval)
        
        raise Exception(f"导出任务等待超时 ({self.max_poll_time}秒)")
    
    async def download_file(self,
                          export_data: Dict[str, Any],
                          tokens: Dict[str, str],
                          save_dir: str = None,
                          use_temp_dir: bool = False) -> str:
        """
        下载导出文件（支持跨平台路径和临时目录）
        
        Args:
            export_data: 导出任务数据
            tokens: 认证Token
            save_dir: 保存目录（如果为None且use_temp_dir=True，使用系统临时目录）
            use_temp_dir: 是否使用临时目录
            
        Returns:
            str: 下载文件的本地路径
        """
        try:
            # 构建下载URL
            file_path = export_data.get('path', '')
            jwt_token = tokens.get('jwt_token', '')
            
            if not file_path:
                raise Exception("导出数据中缺少文件路径")
            
            download_url = f"{self.base_url}{file_path}?jwt_token={jwt_token}"
            
            # 提取文件名
            file_name = file_path.split('/')[-1]
            
            # 🔧 跨平台兼容的目录处理
            if use_temp_dir or save_dir is None:
                # 使用系统临时目录
                save_path = Path(tempfile.gettempdir()) / "export_publish_list"
            else:
                save_path = Path(save_dir)
            
            # 确保保存目录存在
            save_path.mkdir(parents=True, exist_ok=True)
            local_path = save_path / file_name
            
            self.logger.info(f"开始下载文件", extra_data={
                'download_url': download_url[:100] + '...',  # 截断URL用于日志
                'local_path': str(local_path),
                'use_temp_dir': use_temp_dir,
                'save_dir': str(save_path)
            })
            
            # 下载文件
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(download_url) as response:
                    if response.status == 200:
                        content = await response.read()
                        
                        # 🔧 使用pathlib写入文件
                        local_path.write_bytes(content)
                        
                        file_size = len(content)
                        self.logger.info(f"文件下载成功", extra_data={
                            'local_path': str(local_path),
                            'file_size': file_size,
                            'is_temp_file': use_temp_dir
                        })
                        
                        return str(local_path)
                    else:
                        error_text = await response.text()
                        raise Exception(f"文件下载失败: {response.status}, {error_text}")
                        
        except Exception as e:
            self.logger.error(f"文件下载异常: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e),
                'export_task_id': export_data.get('id'),
                'file_path': export_data.get('path', '')
            })
            raise
    
    async def get_file_content(self,
                              export_data: Dict[str, Any],
                              tokens: Dict[str, str]) -> bytes:
        """
        获取导出文件内容（内存处理，不保存到磁盘）
        
        Args:
            export_data: 导出任务数据
            tokens: 认证Token
            
        Returns:
            bytes: 文件内容字节数据
        """
        try:
            # 构建下载URL
            file_path = export_data.get('path', '')
            jwt_token = tokens.get('jwt_token', '')
            
            if not file_path:
                raise Exception("导出数据中缺少文件路径")
            
            download_url = f"{self.base_url}{file_path}?jwt_token={jwt_token}"
            
            # 提取文件名用于日志
            file_name = file_path.split('/')[-1]
            
            self.logger.info(f"开始获取文件内容", extra_data={
                'file_name': file_name,
                'download_url': download_url[:100] + '...',  # 截断URL用于日志
                'export_task_id': export_data.get('id')
            })
            
            # 获取文件内容
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(download_url) as response:
                    if response.status == 200:
                        content = await response.read()
                        
                        file_size = len(content)
                        self.logger.info(f"文件内容获取成功", extra_data={
                            'file_name': file_name,
                            'file_size': file_size,
                            'content_type': response.headers.get('content-type', 'unknown')
                        })
                        
                        return content
                    else:
                        error_text = await response.text()
                        raise Exception(f"文件内容获取失败: {response.status}, {error_text}")
                        
        except Exception as e:
            self.logger.error(f"文件内容获取异常: {str(e)}", extra_data={
                'exception_type': type(e).__name__,
                'exception_details': str(e),
                'export_task_id': export_data.get('id'),
                'file_path': export_data.get('path', '')
            })
            raise 