#!/bin/bash
set -e
echo "=== RPA Container Starting (export_publish_list) ==="
echo "Timezone: $(date)"
echo "Python Path: $PYTHONPATH"
echo "Working Directory: $(pwd)"
echo "==================================================="
echo "Environment Variables:"
echo "BUSINESS_TYPE: $BUSINESS_TYPE"
echo "SCRIPT_NAME: $SCRIPT_NAME"
echo "TZ: $TZ"
echo "RPA_EXECUTION_MODE: $RPA_EXECUTION_MODE"
echo "==================================================="

# Verify browser installation
echo "Verifying browser installation..."
ls -la /home/<USER>/.cache/ms-playwright/ 2>/dev/null && echo "Browser cache directory found" || echo "Browser cache not found"

# Verify core dependencies
echo "Verifying core dependencies..."
python -c "import aiohttp; print(aiohttp.__version__)"
python -c "import playwright; print('playwright loaded')"

# Create download directory
echo "Creating download directory..."
mkdir -p /app/downloads

# Start main script
echo "Starting export_publish_list main script..."
echo "==================================================="
export PYTHONPATH=/app:$PYTHONPATH
cd /app && python /app/app/business/export_publish_list/main.py