"""
屏蔽管理器

核心业务逻辑协调器，整合各个组件完成屏蔽解屏蔽流程：
1. 登录认证管理
2. 公告通知接口调用
3. Excel文件处理
4. 数据库查询匹配
5. 集成批量导入（包含模板下载、生成和库存更新）
"""

import asyncio
import logging
import json
import os
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

from app.shared.managers import YimaiLoginManager
from app.shared.clients import TokenExtractor, UserInfoExtractor
from app.shared.models import UserInfo
from app.business.block_unblock_management.notice_client import NoticeClient
from app.business.block_unblock_management.excel_processor import BlockExcelProcessor
from app.business.block_unblock_management.stock_client import StockClient
from app.business.block_unblock_management.db_query_processor import DBQueryProcessor
from app.business.block_unblock_management.config import BlockUnblockConfig
from app.business.block_unblock_management.logging_enhancer import LoggingEnhancer, ErrorCodes
from app.core.web_driver.base_driver import BaseWebDriver


class BlockManager:
    """
    屏蔽管理器（简化版）
    
    整合核心组件，提供完整的屏蔽解屏蔽业务流程，使用集成批量导入方式
    """
    
    def __init__(self, logger: logging.Logger = None, business_type: str = None, script_name: str = None):
        """
        初始化屏蔽管理器
        
        Args:
            logger: 日志记录器
            business_type: 业务类型
            script_name: 脚本名称
        """
        self.logger = logger or logging.getLogger(__name__)
        self.business_type = business_type or "block_unblock_management"
        self.script_name = script_name or "block_unblock_management"
        
        # 初始化核心组件（仅保留必需的组件）
        self.login_manager = YimaiLoginManager(
            self.logger, self.business_type, self.script_name
        )
        self.token_extractor = TokenExtractor(self.logger)
        self.user_info_extractor = UserInfoExtractor(self.logger)
        self.notice_client = NoticeClient(self.logger)
        self.excel_processor = BlockExcelProcessor(self.logger)
        
        # 为库存客户端使用正确的API域名
        api_base_url = "https://salecentersaasapi.yibainetwork.com"
        self.stock_client = StockClient(self.logger, api_base_url)
        self.db_query_processor = DBQueryProcessor(self.logger)
        
        # 加载配置
        self.config = BlockUnblockConfig.get_default_config()
        
        # 状态跟踪
        self.last_tokens = None
        self.user_id = None  # 存储从登录获取的用户ID
        self.user_info = None  # 存储用户信息
        self.execution_stats = {
            'start_time': None,
            'end_time': None,
            'processed_files': 0,
            'block_skus_count': 0,
            'unblock_skus_count': 0,
            'stock_updates': 0
        }
        
        # 日志增强器  
        self.logging_enhancer = LoggingEnhancer()
        self.current_trace_id = None
        
        self.logger.info("屏蔽管理器初始化完成（简化版-集成批量导入）", step="manager_init", extra_data={
            'business_type': self.business_type,
            'script_name': self.script_name,
            'mode': 'integrated_batch_import',
            'components_initialized': [
                'login_manager',
                'token_extractor', 
                'user_info_extractor',
                'notice_client',
                'excel_processor',
                'stock_client',
                'db_query_processor',
                'logging_enhancer'
            ]
        })
    
    async def execute_block_unblock_flow(self, 
                                        driver: BaseWebDriver,
                                        date_range: Tuple[str, str]) -> Dict[str, Any]:
        """
        执行完整的屏蔽解屏蔽流程（使用集成批量导入）
        
        Args:
            driver: Web驱动对象
            date_range: 时间范围 (start_date, end_date)
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        # 生成链路追踪ID
        self.current_trace_id = self.logging_enhancer.generate_trace_id()
        
        async with self.logging_enhancer.trace_execution("屏蔽解屏蔽完整流程（集成批量导入）", self.current_trace_id) as context:
            try:
                self.execution_stats['start_time'] = datetime.now()
                
                # 记录流程开始检查点
                self.logging_enhancer.record_checkpoint(
                    self.current_trace_id, 
                    "流程启动", 
                    {
                        'date_range': date_range,
                        'driver_type': type(driver).__name__,
                        'mode': 'integrated_import',
                        'config_version': getattr(self.config, 'version', 'unknown')
                    }
                )
                
                # 步骤1: 确保登录状态并提取Token和用户信息
                tokens, user_info = await self._ensure_login_and_extract_info(driver)
                if not tokens:
                    raise Exception("登录或Token提取失败")
                
                # 步骤2: 获取公告通知列表，查找昨日屏蔽文件
                notice_files = await self._get_notice_files(tokens, date_range)
                if not notice_files:
                    raise Exception("未找到昨日屏蔽文件")
                
                # 步骤3: 下载并处理Excel文件
                processed_data = await self._process_excel_files(notice_files, tokens)
                if not processed_data:
                    raise Exception("Excel文件处理失败")
                
                # 步骤4: 查询数据库获取店铺信息
                enriched_data = await self._enrich_with_database_info(processed_data)
                
                # 步骤5: 使用集成批量导入（直接跳过模板生成，在导入器内部处理）
                stock_result = await self._execute_integrated_batch_import(enriched_data, tokens)
                
                # 记录执行统计
                self.execution_stats['end_time'] = datetime.now()
                self.execution_stats['processed_files'] = len(notice_files)
                self.execution_stats['block_skus_count'] = len(processed_data.get('block_skus', []))
                self.execution_stats['unblock_skus_count'] = len(processed_data.get('unblock_skus', []))
                
                # 记录成功完成检查点
                self.logging_enhancer.record_checkpoint(
                    self.current_trace_id,
                    "流程完成",
                    {
                        'processed_files': len(notice_files),
                        'block_skus': len(processed_data.get('block_skus', [])),
                        'unblock_skus': len(processed_data.get('unblock_skus', [])),
                        'import_mode': 'integrated_batch_import',
                        'stock_update_success': stock_result.get('success', False),
                        'user_info': user_info.to_dict() if user_info else None
                    }
                )
                
                # 构建结果字典
                result = {
                    'success': True,
                    'message': '屏蔽解屏蔽流程执行成功（集成批量导入）',
                    'trace_id': self.current_trace_id,
                    'processed_data': processed_data,
                    'stock_import_result': stock_result,
                    'statistics': self._get_execution_statistics(),
                    'execution_report': self.logging_enhancer.generate_execution_report(),
                    'timestamp': datetime.now().isoformat(),
                    'user_info': user_info.to_dict() if user_info else None
                }
                
                # 确保结果中不包含set类型（处理JSON序列化问题）
                result = self._convert_sets_to_lists(result)
                
                self.logger.info("屏蔽解屏蔽流程执行成功（集成批量导入）", step="flow_complete", extra_data=self._convert_sets_to_lists({
                    'trace_id': self.current_trace_id,
                    'summary': {
                        'block_skus': len(processed_data.get('block_skus', [])),
                        'unblock_skus': len(processed_data.get('unblock_skus', [])),
                        'import_mode': 'integrated_batch_import',
                        'user_account': user_info.account if user_info else None
                    }
                }))
                return result
                
            except Exception as e:
                # 记录失败检查点
                self.logging_enhancer.record_checkpoint(
                    self.current_trace_id,
                    "流程失败",
                    {
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                        'date_range': date_range
                    }
                )
                
                # 构建错误结果字典
                error_result = {
                    'success': False,
                    'error': str(e),
                    'error_code': self._get_error_code(e),
                    'message': f'屏蔽解屏蔽流程执行失败: {str(e)}',
                    'trace_id': self.current_trace_id,
                    'statistics': self._get_execution_statistics(),
                    'execution_report': self.logging_enhancer.generate_execution_report(),
                    'timestamp': datetime.now().isoformat()
                }
                
                # 确保结果中不包含set类型（处理JSON序列化问题）
                error_result = self._convert_sets_to_lists(error_result)
                
                return error_result
            
            finally:
                # 确保后置处理清理临时文件
                await self._cleanup_temporary_files()
    
    async def _ensure_login_and_extract_info(self, driver: BaseWebDriver) -> Tuple[Optional[Dict[str, str]], Optional[UserInfo]]:
        """
        确保登录状态并提取认证Token和用户信息
        
        Args:
            driver: Web驱动对象
            
        Returns:
            Tuple[Optional[Dict[str, str]], Optional[UserInfo]]: Token字典和用户信息对象
        """
        async with self.logging_enhancer.trace_execution("步骤1: 登录和信息提取", self.current_trace_id) as context:
            try:
                # 记录检查点
                self.logging_enhancer.record_checkpoint(
                    self.current_trace_id,
                    "开始登录验证",
                    {'driver_ready': bool(driver)}
                )
                
                # 注册响应处理器
                if hasattr(driver, 'page') and driver.page:
                    await driver.page.route('**/*', self.handle_response)
                    self.logger.info("已注册响应处理器，将拦截登录请求")
                
                # 确保登录状态
                self.logger.info("开始确保亿迈系统登录状态")
                await self.login_manager.ensure_login(driver)
                self.logger.info("亿迈系统登录状态确认完成")
                
                # 提取认证Token
                self.logger.info("开始提取认证Token")
                tokens = await self.token_extractor.extract_all_tokens(driver.page)
                
                # 记录提取的token信息（调试用）
                self.logger.info("Token提取完成", extra_data={
                    'has_jwt_token': bool(tokens.get('jwt_token')),
                    'jwt_token_length': len(tokens.get('jwt_token', '')),
                    'has_token1_check': bool(tokens.get('token1_check')),
                    'has_device_number': bool(tokens.get('device_number')),
                    'has_token2': bool(tokens.get('token2')),
                    'has_token2_timestamp': bool(tokens.get('token2_timestamp')),
                    'has_anticlimb_verify_code': bool(tokens.get('anticlimb_verify_code'))
                })
                
                # 验证Token有效性
                is_valid = await self.token_extractor.validate_tokens(tokens)
                if not is_valid:
                    self.logger.error("Token验证失败，无法继续执行")
                    return None, None
                
                # 提取用户信息
                self.logger.info("开始提取用户信息")
                
                # 尝试从登录响应中提取
                user_info = None
                
                # 检查是否已经在响应处理器中提取到了用户ID
                if hasattr(self, 'user_id') and self.user_id:
                    self.logger.info(f"使用已提取的用户信息，用户ID: {self.user_id}")
                    user_info = UserInfo(user_id=self.user_id)
                else:
                    # 尝试从登录响应数据中提取
                    if hasattr(self, 'login_response_data') and self.login_response_data:
                        self.logger.info("尝试从已保存的登录响应中提取用户信息")
                        user_info = await self.user_info_extractor.extract_user_info_from_response_data(self.login_response_data)
                        
                    # 如果仍然没有提取到，尝试从网络历史记录中提取
                    if not user_info or not user_info.user_id:
                        self.logger.info("尝试从网络历史记录中提取用户信息")
                        user_info = await self.user_info_extractor.extract_user_info_from_network_history(driver.page)
                        
                    # 如果仍然没有提取到，尝试从JWT Token中提取
                    if (not user_info or not user_info.user_id) and 'jwt_token' in tokens:
                        self.logger.info("尝试从JWT Token中提取用户信息")
                        user_info = await self.user_info_extractor.extract_user_info_from_jwt_token(tokens['jwt_token'])
                
                if not user_info or not user_info.user_id:
                    self.logger.error("未能提取到用户信息，无法继续执行")
                    return tokens, None
                
                self.logger.info(f"用户信息提取成功，用户ID: {user_info.user_id}", extra_data={
                    'user_id': user_info.user_id,
                    'account': user_info.account,
                    'account_name': user_info.account_name
                })
                
                return tokens, user_info
                
            except Exception as e:
                error_msg = f"登录或信息提取异常: {str(e)}"
                self.logger.error(error_msg)
                self.logging_enhancer.record_checkpoint(
                    self.current_trace_id,
                    "登录或信息提取失败",
                    {'error': error_msg}
                )
                return None, None
    
    async def _get_notice_files(self, 
                               tokens: Dict[str, str], 
                               date_range: Tuple[str, str]) -> List[Dict[str, Any]]:
        """
        获取公告通知列表中的屏蔽文件
        
        Args:
            tokens: 认证Token
            date_range: 时间范围
            
        Returns:
            List[Dict[str, Any]]: 文件列表，包含下载链接信息
        """
        try:
            self.logger.info("步骤2: 获取公告通知列表中的屏蔽文件")
            
            # 获取最新的屏蔽文件下载链接
            # 传递用户ID以确保请求参数正确
            user_id = getattr(self, 'user_id', None)
            file_url = await self.notice_client.get_latest_block_file(tokens, date_range, user_id)
            
            if not file_url:
                self.logger.warning("未找到屏蔽文件下载链接")
                return []
            
            # 构建文件信息
            notice_files = [{
                'file_url': file_url,
                'file_type': 'excel',
                'source': 'notice_api',
                'date_range': date_range,
                'title': '屏蔽与释放SKU明细'
            }]
            
            self.logger.info(f"找到 {len(notice_files)} 个屏蔽文件", extra_data={
                'file_url': file_url,
                'date_range': date_range
            })
            
            return notice_files
            
        except Exception as e:
            self.logger.error(f"获取公告通知文件失败: {str(e)}")
            raise
    
    async def _process_excel_files(self, 
                                  notice_files: List[Dict[str, Any]], 
                                  tokens: Dict[str, str]) -> Dict[str, Any]:
        """
        下载并处理Excel文件
        
        Args:
            notice_files: 公告文件列表
            tokens: 认证Token
            
        Returns:
            Dict[str, Any]: 处理后的数据
        """
        try:
            performance_start = datetime.now()
            
            self.logger.info("步骤3: 开始下载和处理Excel文件", step="excel_processing_start", extra_data={
                'files_count': len(notice_files),
                'files_info': [{'url': f.get('file_url', ''), 'size_estimate': 'unknown'} for f in notice_files],
                'performance_marker': 'excel_download_start'
            })
            
            block_skus = []
            unblock_skus = []
            processed_files = []
            total_download_time = 0
            total_parse_time = 0
            
            for i, file_info in enumerate(notice_files):
                file_start_time = datetime.now()
                file_url = file_info.get('file_url', '')
                
                self.logger.info(f"处理文件 {i+1}/{len(notice_files)}: {file_url}", extra_data={
                    'file_index': i + 1,
                    'total_files': len(notice_files),
                    'file_url': file_url,
                    'expected_content': 'block_unblock_data'
                })
                
                # 使用备用机制下载文件
                try:
                    download_start = datetime.now()
                    
                    # 使用新的备用下载方法
                    file_content = await self.notice_client.download_file_with_fallback(file_url, tokens)
                    
                    download_end = datetime.now()
                    download_duration = (download_end - download_start).total_seconds()
                    total_download_time += download_duration
                    
                    self.logger.info(f"文件下载完成", extra_data={
                        'file_index': i + 1,
                        'download_duration_seconds': download_duration,
                        'file_size_bytes': len(file_content),
                        'file_size_mb': len(file_content) / 1024 / 1024,
                        'download_speed_mbps': (len(file_content) / 1024 / 1024) / download_duration if download_duration > 0 else 0
                    })
                    
                except Exception as download_error:
                    self.logger.error(f"处理文件失败: {file_url} - {str(download_error)}")
                    continue
                
                # 解析Excel文件
                try:
                    parse_start = datetime.now()
                    
                    parsed_data = await self.excel_processor.process_excel_content(file_content)
                    
                    parse_end = datetime.now()
                    parse_duration = (parse_end - parse_start).total_seconds()
                    total_parse_time += parse_duration
                    
                    if parsed_data:
                        file_block_skus = parsed_data.get('block_skus', [])
                        file_unblock_skus = parsed_data.get('unblock_skus', [])
                        
                        block_skus.extend(file_block_skus)
                        unblock_skus.extend(file_unblock_skus)
                        processed_files.append({
                            'file_url': file_url,
                            'file_size': len(file_content),
                            'block_skus_count': len(file_block_skus),
                            'unblock_skus_count': len(file_unblock_skus),
                            'download_duration': download_duration,
                            'parse_duration': parse_duration,
                            'success': True
                        })
                        
                        self.logger.info(f"文件解析完成", extra_data={
                            'file_index': i + 1,
                            'parse_duration_seconds': parse_duration,
                            'block_skus_found': len(file_block_skus),
                            'unblock_skus_found': len(file_unblock_skus),
                            'total_records': len(file_block_skus) + len(file_unblock_skus)
                        })
                    else:
                        self.logger.warning(f"文件解析结果为空: {file_url}")
                        processed_files.append({
                            'file_url': file_url,
                            'file_size': len(file_content),
                            'error': 'Empty parse result',
                            'success': False
                        })
                        
                except Exception as parse_error:
                    self.logger.error(f"文件解析失败: {file_url} - {str(parse_error)}")
                    processed_files.append({
                        'file_url': file_url,
                        'file_size': len(file_content) if 'file_content' in locals() else 0,
                        'error': str(parse_error),
                        'success': False
                    })
                    continue
                
                file_end_time = datetime.now()
                file_total_duration = (file_end_time - file_start_time).total_seconds()
                
                self.logger.debug(f"文件处理完成", extra_data={
                    'file_index': i + 1,
                    'total_duration_seconds': file_total_duration,
                    'performance_breakdown': {
                        'download_seconds': download_duration,
                        'parse_seconds': parse_duration,
                        'overhead_seconds': file_total_duration - download_duration - parse_duration
                    }
                })
            
            performance_end = datetime.now()
            total_duration = (performance_end - performance_start).total_seconds()
            
            # 记录详细的性能统计
            performance_stats = {
                'total_duration_seconds': total_duration,
                'total_download_time_seconds': total_download_time,
                'total_parse_time_seconds': total_parse_time,
                'overhead_time_seconds': total_duration - total_download_time - total_parse_time,
                'average_file_processing_time': total_duration / len(notice_files) if notice_files else 0,
                'files_processed_successfully': len([f for f in processed_files if f.get('success', False)]),
                'files_failed': len([f for f in processed_files if not f.get('success', False)]),
                'total_data_downloaded_mb': sum(f.get('file_size', 0) for f in processed_files) / 1024 / 1024,
                'average_download_speed_mbps': (sum(f.get('file_size', 0) for f in processed_files) / 1024 / 1024) / total_download_time if total_download_time > 0 else 0
            }
            
            # 记录公告文件处理完成检查点
            self.logging_enhancer.record_checkpoint(
                self.current_trace_id, 
                "Excel文件处理完成", 
                {
                    'processed_files_count': len(processed_files),
                    'successful_files': performance_stats['files_processed_successfully'],
                    'failed_files': performance_stats['files_failed'],
                    'total_block_skus': len(block_skus),
                    'total_unblock_skus': len(unblock_skus),
                    'performance_stats': performance_stats
                }
            )
            
            self.logger.info("公告文件处理完成", step="excel_processing_complete", extra_data={
                'performance_stats': performance_stats,
                'summary': {
                    'block_skus_count': len(block_skus),
                    'unblock_skus_count': len(unblock_skus),
                    'files_processed': len(processed_files),
                    'success_rate': f"{(performance_stats['files_processed_successfully'] / len(notice_files) * 100):.1f}%" if notice_files else "0%"
                }
            })
            
            return {
                'block_skus': block_skus,
                'unblock_skus': unblock_skus,
                'processed_files': processed_files,
                'statistics': performance_stats
            }
            
        except Exception as e:
            self.logging_enhancer.record_checkpoint(
                self.current_trace_id, 
                "Excel文件处理失败", 
                {'error': str(e), 'error_type': type(e).__name__}
            )
            self.logger.error(f"Excel文件处理过程中发生异常: {str(e)}")
            raise
    
    async def _enrich_with_database_info(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询数据库获取店铺信息
        
        Args:
            processed_data: 处理后的数据
            
        Returns:
            Dict[str, Any]: 富化后的数据
        """
        try:
            self.logger.info("步骤4: 查询数据库获取店铺信息")
            
            # 提取屏蔽和解屏蔽SKU列表
            block_skus = processed_data.get('block_skus', [])
            unblock_skus = processed_data.get('unblock_skus', [])
            
            if not block_skus and not unblock_skus:
                self.logger.warning("没有SKU需要查询")
                enriched_data = processed_data.copy()
                enriched_data['database_mapping'] = {
                    'block_sku_mappings': {},
                    'unblock_sku_mappings': {},
                    'statistics': {}
                }
                return enriched_data
            
            # 使用数据库查询器获取SKU映射，区分屏蔽和解屏蔽
            self.logger.info(f"开始查询数据库 - 屏蔽SKU: {len(block_skus)}个, 解屏蔽SKU: {len(unblock_skus)}个")
            
            mapping_result = await self.db_query_processor.query_skus_with_operation_type(
                block_skus, unblock_skus
            )
            
            # 验证映射数据质量
            validation_result = await self.db_query_processor.validate_sku_mappings(mapping_result)
            
            # 富化数据
            enriched_data = processed_data.copy()
            enriched_data['database_mapping'] = mapping_result
            enriched_data['validation_result'] = validation_result
            
            # 记录统计信息
            stats = mapping_result.get('statistics', {})
            
            self.logger.info("数据库查询完成", step="database_query_complete", extra_data={
                'block_skus_mapped': f"{stats.get('block_skus_mapped', 0)}/{len(block_skus)}",
                'unblock_skus_mapped': f"{stats.get('unblock_skus_mapped', 0)}/{len(unblock_skus)}",
                'total_records_found': stats.get('total_records_found', 0),
                'query_time': f"{stats.get('query_time', 0):.2f}s",
                'data_quality': 'Valid' if validation_result.get('is_valid', False) else 'Warning'
            })
            
            if not validation_result.get('is_valid', False):
                self.logger.warning("SKU映射数据验证失败，部分数据可能不完整", extra_data={
                    'validation_errors': validation_result.get('errors', []),
                    'validation_warnings': len(validation_result.get('warnings', []))
                })
            
            return enriched_data
            
        except Exception as e:
            self.logger.error(f"数据库查询失败: {str(e)}")
            raise
    
    async def _execute_integrated_batch_import(self, 
                                     enriched_data: Dict[str, Any], 
                                                  tokens: Dict[str, str]) -> Dict[str, Any]:
        """
        使用集成批量导入处理器进行库存更新
        
        Args:
            enriched_data: 富化后的数据
            tokens: 认证Token
            
        Returns:
            Dict[str, Any]: 集成导入结果
        """
        try:
            self.logger.info("🚀 开始集成批量导入模式", extra_data={
                'enriched_data_keys': list(enriched_data.keys()),
                'has_database_mapping': 'database_mapping' in enriched_data
            })
            
            # 从数据库映射结果中转换业务数据
            mapping_result = enriched_data.get('database_mapping', {})
            if not mapping_result:
                raise ValueError("数据库映射结果为空，无法执行集成批量导入")
            
            # 转换数据库映射为业务数据格式
            business_data = self._convert_mapping_to_business_data(mapping_result)
            
            if not business_data:
                raise ValueError("转换后的业务数据为空")
            
            # 检查数据格式
            required_fields = ['*店铺别称', '*SellerSKU', '*在线库存']
            for i, record in enumerate(business_data[:3]):  # 检查前3条
                for field in required_fields:
                    if field not in record:
                        raise ValueError(f"业务数据第{i+1}条缺少必需字段: {field}")
            
            self.logger.info("✅ 业务数据格式验证通过", extra_data={
                'total_records': len(business_data),
                'sample_record': business_data[0] if business_data else None
            })
            
            # 导入集成批量导入处理器
            from app.business.block_unblock_management.integrated_batch_importer import IntegratedBatchImporter
            
            # 创建导入器实例
            importer = IntegratedBatchImporter(logger=self.logger)
                
            # 执行集成批量导入
            test_mode = enriched_data.get('test_mode', False)
            import_result = await importer.execute_integrated_batch_import(
                business_data=business_data,
                test_mode=test_mode
            )
            
            # 更新执行统计
            if import_result.get('success'):
                summary = import_result.get('summary', {})
                self.execution_stats['stock_updates'] = summary.get('successful_records', 0)
                self.execution_stats['update_mode'] = 'integrated_import'
                self.execution_stats['import_summary'] = summary
                self.execution_stats['successful_batches'] = summary.get('successful_imports', 0)
                self.execution_stats['failed_batches'] = summary.get('failed_imports', 0)
                self.execution_stats['total_batches'] = summary.get('total_batch_files', 0)
            else:
                self.execution_stats['stock_updates'] = 0
                self.execution_stats['update_errors'] = import_result.get('error', '集成导入失败')
            
            self.logger.info("🎉 集成批量导入处理完成", step="integrated_import_complete", extra_data={
                'success': import_result.get('success'),
                'summary': import_result.get('summary'),
                'test_mode': test_mode
            })
            
            return import_result
            
        except Exception as e:
            self.logger.error(f"集成批量导入失败: {str(e)}")
            raise

    def _convert_mapping_to_business_data(self, mapping_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        将数据库映射结果转换为业务数据格式
        
        Args:
            mapping_result: 数据库映射结果
            
        Returns:
            List[Dict[str, Any]]: 业务数据列表
        """
        business_data = []
        
        try:
            block_mappings = mapping_result.get('block_sku_mappings', {})
            unblock_mappings = mapping_result.get('unblock_sku_mappings', {})
            
            # 处理屏蔽SKU
            # 注意：数据结构是 {sku: [records]} 而不是 {sku: {'mappings': [records]}}
            for sku, records in block_mappings.items():
                # records 直接是列表，不需要通过 .get('mappings', [])
                for mapping in records:
                    # 构建店铺别称
                    shop_name = self._build_shop_name(mapping)
                    # 使用seller_sku_child作为SellerSKU
                    seller_sku = mapping.get('seller_sku_child') or sku
                    
                    business_data.append({
                        '*店铺别称': shop_name,
                        '*SellerSKU': seller_sku,
                        '*在线库存': 0  # 屏蔽SKU库存设为0
                    })
            
            # 处理解屏蔽SKU
            for sku, records in unblock_mappings.items():
                # records 直接是列表，不需要通过 .get('mappings', [])
                for mapping in records:
                    # 构建店铺别称
                    shop_name = self._build_shop_name(mapping)
                    # 使用seller_sku_child作为SellerSKU
                    seller_sku = mapping.get('seller_sku_child') or sku
                    
                    # 解屏蔽SKU恢复原有库存
                    original_stock = mapping.get('current_stock', 999)
                    if original_stock == 0:
                        original_stock = 999  # 如果当前库存为0，恢复到默认值
                    
                    business_data.append({
                        '*店铺别称': shop_name,
                        '*SellerSKU': seller_sku,
                        '*在线库存': original_stock
                    })
                
            self.logger.info(f"数据库映射转换完成", extra_data={
                'block_skus': len(block_mappings),
                'unblock_skus': len(unblock_mappings),
                'total_business_records': len(business_data)
            })
            
            return business_data
            
        except Exception as e:
            self.logger.error(f"数据库映射转换失败: {str(e)}")
            raise
    
    def _build_shop_name(self, mapping: Dict[str, Any]) -> str:
        """
        构建店铺别称
        
        Args:
            mapping: 映射记录
            
        Returns:
            str: 店铺别称（直接使用account字段）
        """
        try:
            # 直接使用account字段作为店铺别称
            account = mapping.get('account', '').strip()
            
            # 如果account为空，返回默认值
            if not account:
                self.logger.warning("account字段为空，使用默认店铺别称")
                return "Default"
            
            return account
            
        except Exception as e:
            self.logger.warning(f"获取店铺别称失败: {str(e)}")
            return "Default"
    
    def _get_execution_statistics(self) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.execution_stats.copy()
        
        if stats['start_time'] and stats['end_time']:
            execution_time = stats['end_time'] - stats['start_time']
            stats['execution_time'] = str(execution_time)
        else:
            stats['execution_time'] = 'N/A'
        
        return stats
    
    def _convert_sets_to_lists(self, data: Any) -> Any:
        """
        递归地将数据结构中的所有set类型转换为list类型
        
        Args:
            data: 任意数据结构
            
        Returns:
            Any: 转换后的数据结构，不包含set类型
        """
        if isinstance(data, set):
            return list(data)
        elif isinstance(data, list):
            return [self._convert_sets_to_lists(item) for item in data]
        elif isinstance(data, dict):
            return {k: self._convert_sets_to_lists(v) for k, v in data.items()}
        elif isinstance(data, tuple):
            return tuple(self._convert_sets_to_lists(item) for item in data)
        else:
            return data
    
    async def test_database_integration(self) -> Dict[str, Any]:
        """
        测试数据库集成
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            self.logger.info("开始测试数据库集成")
            
            # 测试数据库连接
            connection_result = await self.db_query_processor.test_database_connection()
            
            if not connection_result.get('connected', False):
                return {
                    'success': False,
                    'message': '数据库连接失败',
                    'connection_result': connection_result
                }
            
            # 测试SKU查询（使用示例SKU）
            test_skus = ['TEST_SKU_001', 'TEST_SKU_002']
            try:
                mapping_result = await self.db_query_processor.get_sku_mappings(test_skus)
                query_stats = self.db_query_processor.get_query_statistics()
            except Exception as e:
                self.logger.warning(f"SKU查询测试失败: {str(e)}")
                mapping_result = {}
                query_stats = {}
            
            result = {
                'success': True,
                'message': '数据库集成测试完成',
                'connection_result': connection_result,
                'query_test': {
                    'test_skus': test_skus,
                    'mapping_found': len(mapping_result),
                    'mapping_result': mapping_result
                },
                'query_statistics': query_stats,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info("数据库集成测试成功", extra_data=result)
            return result
            
        except Exception as e:
            self.logger.error(f"数据库集成测试失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'message': f'数据库集成测试异常: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def update_config(self, config_updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_updates: 配置更新字典
        """
        self.config.update(config_updates)
        self.logger.info("屏蔽管理器配置已更新", extra_data={
            'updated_keys': list(config_updates.keys())
        })
    
    def _get_error_code(self, exception: Exception) -> str:
        """
        根据异常推断错误码
        
        Args:
            exception: 异常对象
            
        Returns:
            str: 错误码
        """
        error_type = type(exception).__name__
        error_message = str(exception).lower()
        
        # 认证相关错误
        if 'login' in error_message or 'authentication' in error_message:
            return ErrorCodes.AUTH_LOGIN_FAILED
        elif 'token' in error_message and ('invalid' in error_message or 'expired' in error_message):
            return ErrorCodes.AUTH_TOKEN_INVALID
        
        # 网络和API错误
        elif 'timeout' in error_message or 'timeouterror' in error_type.lower():
            return ErrorCodes.API_TIMEOUT
        elif 'connection' in error_message or 'network' in error_message:
            return ErrorCodes.NETWORK_ERROR
        elif 'http' in error_message or 'api' in error_message:
            return ErrorCodes.API_REQUEST_FAILED
        
        # 文件处理错误
        elif 'file' in error_message and 'not found' in error_message:
            return ErrorCodes.FILE_NOT_FOUND
        elif 'download' in error_message:
            return ErrorCodes.FILE_DOWNLOAD_ERROR
        elif 'upload' in error_message:
            return ErrorCodes.FILE_UPLOAD_ERROR
        elif 'excel' in error_message or 'parse' in error_message:
            return ErrorCodes.FILE_PARSE_ERROR
        
        # 数据库错误
        elif 'database' in error_message or 'sql' in error_message:
            return ErrorCodes.DATABASE_ERROR
        
        # 数据处理错误
        elif 'not found' in error_message:
            return ErrorCodes.DATA_NOT_FOUND
        elif 'validation' in error_message:
            return ErrorCodes.DATA_VALIDATION_ERROR
        elif 'format' in error_message:
            return ErrorCodes.DATA_FORMAT_ERROR
        
        # 业务逻辑错误
        elif 'business' in error_message or 'rule' in error_message:
            return ErrorCodes.BUSINESS_RULE_VIOLATION
        
        # 默认系统错误
        else:
            return ErrorCodes.SYSTEM_ERROR
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """
        获取执行摘要报告
        
        Returns:
            Dict[str, Any]: 执行摘要
        """
        summary = {
            'trace_id': self.current_trace_id,
            'execution_stats': self._get_execution_statistics(),
            'performance_summary': self.logging_enhancer.get_performance_summary(),
            'trace_summary': self.logging_enhancer.get_trace_summary(self.current_trace_id) if self.current_trace_id else None,
            'component_status': {
                'login_manager': 'ready',
                'notice_client': 'ready',
                'excel_processor': 'ready',
                'db_query_processor': 'ready',
                'stock_client': 'ready'
            },
            'last_tokens_available': bool(self.last_tokens),
            'timestamp': datetime.now().isoformat()
        }
        
        return summary

    async def handle_response(self, route, request):
        """
        处理网络请求响应
        
        Args:
            route: Playwright路由对象
            request: Playwright请求对象
        """
        try:
            # 继续路由请求
            await route.continue_()
            
            # 获取URL
            url = request.url
            
            # 只处理登录相关请求
            login_urls = ['login/login/accountLogin', 'login/login', 'user/getUserInfo']
            if any(login_url in url for login_url in login_urls):
                self.logger.debug(f"拦截到登录相关请求: {url}")
                
                # 等待响应完成
                response = await request.response()
                if not response:
                    self.logger.warning(f"未能获取到响应: {url}")
                    return
                
                # 获取响应状态
                status = response.status
                self.logger.debug(f"拦截到登录相关响应: {url}", extra_data={
                    'status': status,
                    'url': url,
                    'response_preview': (await response.text())[:100] + '...' if status == 200 else None
                })
                
                # 只处理成功的响应
                if status == 200:
                    # 记录响应数据，用于后续提取用户信息
                    if 'accountLogin' in url or 'login/login' in url:
                        try:
                            text = await response.text()
                            self.login_response_data = json.loads(text)
                            self.logger.debug(f"成功解析登录响应数据，包含字段: {list(self.login_response_data.keys())}")
                            
                            # 检查是否包含用户ID
                            if self.login_response_data.get('code') == 200 and 'data' in self.login_response_data:
                                data = self.login_response_data['data']
                                if isinstance(data, dict) and 'account_data' in data:
                                    account_data = data['account_data']
                                    if isinstance(account_data, dict) and 'id' in account_data:
                                        user_id = account_data['id']
                                        self.user_id = user_id
                                        self.logger.info(f"从登录响应中提取到用户ID: {user_id}")
                        except Exception as e:
                            self.logger.error(f"解析登录响应数据异常: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"处理网络响应异常: {str(e)}")
            # 确保请求能继续，即使处理出错
            try:
                await route.continue_()
            except:
                pass

    async def _cleanup_temporary_files(self):
        """
        清理临时文件（后置处理）
        
        确保程序结束后始终清理生成的临时文件
        """
        try:
            self.logger.info("🧹 开始清理临时文件")
            
            # 清理集成批量导入处理器产生的临时文件
            try:
                from app.business.block_unblock_management.integrated_batch_importer import IntegratedBatchImporter
                
                # 创建导入器实例进行清理
                importer = IntegratedBatchImporter(logger=self.logger)
                cleanup_result = await importer.cleanup_temporary_files()
                
                if cleanup_result.get('success'):
                    self.logger.info("✅ 集成批量导入临时文件清理完成", extra_data={
                        'cleaned_files': cleanup_result.get('cleaned_files', 0),
                        'cleaned_dirs': cleanup_result.get('cleaned_dirs', 0)
                    })
                else:
                    self.logger.warning("⚠️ 集成批量导入临时文件清理部分失败", extra_data={
                        'error': cleanup_result.get('error')
                    })
                    
            except Exception as cleanup_error:
                self.logger.warning(f"集成批量导入临时文件清理异常: {str(cleanup_error)}")
            
            # 清理本地生成的其他临时文件
            import tempfile
            import os
            import shutil
            from pathlib import Path
            
            temp_dir = Path(tempfile.gettempdir())
            project_temp_patterns = [
                'block_unblock_management_*',
                'template_*_*.xlsx',
                'batch_import_*',
                'integrated_import_*'
            ]
            
            cleaned_count = 0
            for pattern in project_temp_patterns:
                try:
                    for temp_file in temp_dir.glob(pattern):
                        if temp_file.is_file():
                            temp_file.unlink()
                            cleaned_count += 1
                        elif temp_file.is_dir():
                            shutil.rmtree(temp_file, ignore_errors=True)
                            cleaned_count += 1
                except Exception as pattern_error:
                    self.logger.debug(f"清理模式 {pattern} 时出现错误: {str(pattern_error)}")
            
            self.logger.info("✅ 临时文件清理完成", extra_data={
                'total_cleaned': cleaned_count,
                'temp_dir': str(temp_dir)
            })
            
        except Exception as e:
            self.logger.warning(f"临时文件清理异常（不影响主流程）: {str(e)}")