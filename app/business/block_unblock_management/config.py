"""
屏蔽解屏蔽管理配置文件

定义屏蔽解屏蔽相关的配置和常量
"""

import os
from typing import Dict, Any
from datetime import datetime, timedelta


class BlockUnblockConfig:
    """屏蔽解屏蔽功能配置类"""
    
    # 接口配置
    BASE_URL = "https://dcmmaster.yibainetwork.com"  # 基于实际接口的正确域名
    NOTICE_LIST_ENDPOINT = "/message/message/getMessageList"  # 公告通知列表接口
    STOCK_UPDATE_ENDPOINT = "/stock/batch_update"  # 批量改库存接口
    
    # Sheet名称配置
    BLOCK_SHEET_PATTERN = "停售且可售天数<3天-全平台屏蔽"
    UNBLOCK_SHEET_PATTERN = "在售且可售天数<3天-分平台处理"
    
    # 列名配置（根据实际Excel文件调整）
    BLOCK_CONDITION_COLUMN = "屏蔽建议(可售天数>1同步库存)"
    UNBLOCK_CONDITION_COLUMN = "AMZ需要屏蔽/改变处理时间"
    SKU_COLUMN = "sku"
    
    # 筛选条件配置
    BLOCK_CONDITION_VALUE = "屏蔽"
    UNBLOCK_CONDITION_VALUE = "解屏蔽"
    
    # Excel模板列名配置
    TEMPLATE_COLUMNS = {
        'shop_alias': '*店铺别称',
        'seller_sku': '*SellerSKU', 
        'stock_quantity': '*在线库存'
    }
    
    # 库存设置配置
    BLOCK_STOCK_VALUE = 0      # 屏蔽时设置库存为0
    UNBLOCK_STOCK_VALUE = 100  # 解屏蔽时设置库存为100
    
    # 文件配置
    DEFAULT_DOWNLOAD_DIR = "downloads"
    SUPPORTED_FILE_FORMATS = [".xlsx", ".xls"]
    MAX_FILE_SIZE_MB = 1024  # 最大文件大小限制
    
    # 请求配置
    REQUEST_TIMEOUT = 60
    DOWNLOAD_TIMEOUT = 600  # 增加文件下载超时时间到10分钟，解决网络慢导致的下载超时问题
    MAX_RETRY_COUNT = 3
    RETRY_DELAY = 5
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            Dict[str, Any]: 默认配置字典
        """
        return {
            # 基础配置
            'base_url': cls.BASE_URL,
            'download_dir': cls.DEFAULT_DOWNLOAD_DIR,
            
            # Sheet配置
            'sheet_patterns': {
                'block': cls.BLOCK_SHEET_PATTERN,
                'unblock': cls.UNBLOCK_SHEET_PATTERN
            },
            
            # 列名配置
            'column_mapping': {
                'block_condition': cls.BLOCK_CONDITION_COLUMN,
                'unblock_condition': cls.UNBLOCK_CONDITION_COLUMN,
                'sku': cls.SKU_COLUMN
            },
            
            # 筛选条件配置
            'filter_conditions': {
                'block_value': cls.BLOCK_CONDITION_VALUE,
                'unblock_value': cls.UNBLOCK_CONDITION_VALUE
            },
            
            # 模板配置
            'template_columns': cls.TEMPLATE_COLUMNS,
            'stock_values': {
                'block': cls.BLOCK_STOCK_VALUE,
                'unblock': cls.UNBLOCK_STOCK_VALUE
            },
            
            # 请求配置
            'request_timeout': cls.REQUEST_TIMEOUT,
            'download_timeout': cls.DOWNLOAD_TIMEOUT,
            'max_retry_count': cls.MAX_RETRY_COUNT,
            'retry_delay': cls.RETRY_DELAY,
            
            # 文件配置
            'max_file_size_mb': 512,  # 修改文件大小限制为512MB
            
            # 功能开关
            'auto_login': True,
            'auto_create_download_dir': True,
            'enable_retry': True,
            'enable_detailed_logs': True
        }
    
    @classmethod
    def get_yesterday_date_range(cls) -> tuple:
        """
        获取昨天的时间范围（默认查询时间）
        
        Returns:
            tuple: (start_date, end_date)
        """
        yesterday = datetime.now() - timedelta(days=1)
        start_date = yesterday.strftime('%Y-%m-%d 00:00:00')
        end_date = yesterday.strftime('%Y-%m-%d 23:59:59')
        return start_date, end_date
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """
        验证配置的有效性
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置项
            required_keys = [
                'base_url', 'download_dir', 'sheet_patterns', 
                'column_mapping', 'filter_conditions', 'template_columns'
            ]
            
            for key in required_keys:
                if key not in config:
                    print(f"缺少必需的配置项: {key}")
                    return False
            
            # 检查下载目录
            download_dir = config['download_dir']
            if not os.path.exists(download_dir):
                if config.get('auto_create_download_dir', True):
                    try:
                        os.makedirs(download_dir, exist_ok=True)
                        print(f"已创建下载目录: {download_dir}")
                    except Exception as e:
                        print(f"无法创建下载目录 {download_dir}: {e}")
                        return False
                else:
                    print(f"下载目录不存在: {download_dir}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"配置验证异常: {e}")
            return False
    
    @classmethod
    def create_download_directory(cls, download_dir: str = None) -> str:
        """
        创建下载目录
        
        Args:
            download_dir: 下载目录路径
            
        Returns:
            str: 创建的目录路径
        """
        if not download_dir:
            download_dir = cls.DEFAULT_DOWNLOAD_DIR
        
        # 确保目录存在
        os.makedirs(download_dir, exist_ok=True)
        
        # 创建按日期分类的子目录
        today = datetime.now().strftime('%Y-%m-%d')
        date_dir = os.path.join(download_dir, today)
        os.makedirs(date_dir, exist_ok=True)
        
        return date_dir


# 全局配置实例
DEFAULT_BLOCK_UNBLOCK_CONFIG = BlockUnblockConfig.get_default_config() 