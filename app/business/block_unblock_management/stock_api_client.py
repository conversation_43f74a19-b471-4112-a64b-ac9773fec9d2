"""
批量库存修改API客户端

基于屏蔽与解屏蔽管理系统的成熟认证架构，实现：
1. 复用YimaiLoginManager进行RPA登录
2. 复用TokenExtractor提取完整认证信息
3. 复用UserInfoExtractor获取用户信息
4. 调用批量库存修改接口
5. 处理文件上传和响应结果
"""

import asyncio
import os
import sys
import json
import aiohttp
import aiofiles
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from urllib.parse import urljoin

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
sys.path.insert(0, project_root)

from app.core.base_rpa_async import AsyncBaseRPA
from app.shared.managers import YimaiLoginManager
from app.shared.clients import TokenExtractor, UserInfoExtractor
from app.shared.models import UserInfo
from app.utils.logger import get_rpa_logger


class StockAdjustApiClient(AsyncBaseRPA):
    """
    批量库存修改API客户端
    
    复用屏蔽与解屏蔽管理系统的成熟认证架构：
    - YimaiLoginManager: 智能登录状态检测和自动登录
    - TokenExtractor: 提取所有必需的认证Token
    - UserInfoExtractor: 多渠道用户信息提取
    - 完整的网络拦截和响应处理
    """
    
    def __init__(self, logger=None, business_type: str = None, script_name: str = None):
        """初始化API客户端"""
        super().__init__()
        self.logger = logger or get_rpa_logger("stock_api", "client")
        self.business_type = business_type or "stock_adjust_api"
        self.script_name = script_name or "stock_api_client"
        
        # API配置
        self.api_config = {
            'base_url': 'https://salecentersaasapi.yibainetwork.com',
            'stock_adjust_endpoint': '/listing/online/listing_management/listing_stock_adjust_import',
            'timeout': 30,
            'max_retries': 3
        }
        
        # 认证信息
        self.auth_info = {
            'tokens': None,
            'user_info': None,
            'cookies': None,
            'headers': None
        }
        
        # 初始化核心认证组件（复用屏蔽与解屏蔽系统）
        self.login_manager = YimaiLoginManager(
            self.logger, self.business_type, self.script_name
        )
        self.token_extractor = TokenExtractor(self.logger)
        self.user_info_extractor = UserInfoExtractor(self.logger)
        
        # 网络拦截状态
        self._network_intercepted = False
        self._login_response_data = None
        self._user_id = None
        
        self.logger.info("✅ StockAdjustApiClient 初始化完成（使用成熟认证架构）", extra_data={
            'business_type': self.business_type,
            'script_name': self.script_name,
            'api_base_url': self.api_config['base_url'],
            'components_initialized': [
                'login_manager',
                'token_extractor', 
                'user_info_extractor'
            ]
        })
    
    async def execute_rpa_login_and_extract_info(self) -> Dict[str, Any]:
        """
        执行RPA登录并提取认证信息（复用BlockManager的成熟实现）
        
        Returns:
            Dict[str, Any]: 包含所有认证Token和用户信息的字典
        """
        try:
            self.logger.info("🚀 开始执行RPA登录和完整认证信息提取...")
            
            # 使用web_driver_context获取driver
            async with self.web_driver_context() as driver:
                # 直接复用BlockManager中验证过的认证逻辑
                tokens, user_info = await self._ensure_login_and_extract_info(driver)
                
                if not tokens:
                    raise Exception("Token提取失败")
                
                if not user_info or not user_info.user_id:
                    raise Exception("用户信息提取失败")
                
                # 保存认证信息
                self.auth_info['tokens'] = tokens
                self.auth_info['user_info'] = user_info
                
                self.logger.info("✅ 完整认证信息提取成功", extra_data={
                    'user_id': user_info.user_id,
                    'account': user_info.account,
                    'has_jwt_token': bool(tokens.get('jwt_token')),
                    'has_token1_check': bool(tokens.get('token1_check')),
                    'has_device_number': bool(tokens.get('device_number')),
                    'has_token2': bool(tokens.get('token2')),
                    'has_sign': bool(tokens.get('sign')),
                    'total_tokens': len([k for k, v in tokens.items() if v])
                })
                
                return {
                    'success': True,
                    'tokens': tokens,
                    'user_info': user_info,
                    'auth_complete': True
                }
                
        except Exception as e:
            self.logger.error(f"❌ RPA登录和信息提取失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _ensure_login_and_extract_info(self, driver) -> Tuple[Optional[Dict[str, str]], Optional[UserInfo]]:
        """
        确保登录状态并提取认证Token和用户信息
        （直接复用BlockManager中的成熟实现）
        
        Args:
            driver: Web驱动对象
            
        Returns:
            Tuple[Optional[Dict[str, str]], Optional[UserInfo]]: Token字典和用户信息对象
        """
        try:
            self.logger.info("开始登录验证和认证信息提取")
            
            # 设置网络拦截（复用BlockManager的网络处理逻辑）
            if hasattr(driver, 'page') and driver.page:
                await driver.page.route('**/*', self._handle_response)
                self.logger.info("已注册响应处理器，将拦截登录请求")
            
            # 1. 确保登录状态（使用YimaiLoginManager）
            self.logger.info("开始确保亿迈系统登录状态")
            await self.login_manager.ensure_login(driver)
            self.logger.info("亿迈系统登录状态确认完成")
            
            # 2. 提取所有认证Token（使用TokenExtractor）
            self.logger.info("开始提取完整认证Token")
            tokens = await self.token_extractor.extract_all_tokens(driver.page)
            
            # 记录提取的token信息
            self.logger.info("Token提取完成", extra_data={
                'has_jwt_token': bool(tokens.get('jwt_token')),
                'jwt_token_length': len(tokens.get('jwt_token', '')),
                'has_token1_check': bool(tokens.get('token1_check')),
                'has_device_number': bool(tokens.get('device_number')),
                'has_token2': bool(tokens.get('token2')),
                'has_token2_timestamp': bool(tokens.get('token2_timestamp')),
                'has_anticlimb_verify_code': bool(tokens.get('anticlimb_verify_code')),
                'has_sign': bool(tokens.get('sign')),
                'has_cookies': bool(tokens.get('cookies'))
            })
            
            # 3. 验证Token有效性
            is_valid = await self.token_extractor.validate_tokens(tokens)
            if not is_valid:
                self.logger.error("Token验证失败，无法继续执行")
                return None, None
            
            # 4. 提取用户信息（使用UserInfoExtractor的多渠道提取）
            self.logger.info("开始提取用户信息")
            user_info = None
            
            # 检查是否已经在响应处理器中提取到了用户ID
            if hasattr(self, '_user_id') and self._user_id:
                self.logger.info(f"使用已提取的用户信息，用户ID: {self._user_id}")
                user_info = UserInfo(user_id=self._user_id)
            else:
                # 尝试从登录响应数据中提取
                if hasattr(self, '_login_response_data') and self._login_response_data:
                    self.logger.info("尝试从已保存的登录响应中提取用户信息")
                    user_info = await self.user_info_extractor.extract_user_info_from_response_data(self._login_response_data)
                    
                # 如果仍然没有提取到，尝试从网络历史记录中提取
                if not user_info or not user_info.user_id:
                    self.logger.info("尝试从网络历史记录中提取用户信息")
                    user_info = await self.user_info_extractor.extract_user_info_from_network_history(driver.page)
                    
                # 如果仍然没有提取到，尝试从JWT Token中提取
                if (not user_info or not user_info.user_id) and 'jwt_token' in tokens:
                    self.logger.info("尝试从JWT Token中提取用户信息")
                    user_info = await self.user_info_extractor.extract_user_info_from_jwt_token(tokens['jwt_token'])
            
            if not user_info or not user_info.user_id:
                self.logger.error("未能提取到用户信息，无法继续执行")
                return tokens, None
            
            self.logger.info(f"用户信息提取成功，用户ID: {user_info.user_id}", extra_data={
                'user_id': user_info.user_id,
                'account': user_info.account,
                'account_name': user_info.account_name
            })
            
            return tokens, user_info
            
        except Exception as e:
            error_msg = f"登录或信息提取异常: {str(e)}"
            self.logger.error(error_msg)
            return None, None
    
    async def _handle_response(self, route, request):
        """
        网络响应处理器（复用BlockManager的网络拦截逻辑）
        
        Args:
            route: 路由对象
            request: 请求对象
        """
        try:
            # 继续原始请求
            response = await route.fetch()
            
            # 检查是否是登录相关的响应
            if 'login' in request.url or 'user' in request.url:
                try:
                    response_text = await response.text()
                    if response_text:
                        response_data = json.loads(response_text)
                        await self._process_login_response(response_data, request.url)
                except:
                    pass  # 忽略解析错误
            
            # 完成路由
            await route.fulfill(response=response)
            
        except Exception as e:
            # 如果处理失败，继续原始请求
            await route.continue_()
    
    async def _process_login_response(self, data: Dict[str, Any], url: str):
        """
        处理登录响应数据（复用BlockManager的响应处理逻辑）
        
        Args:
            data: 响应数据
            url: 请求URL
        """
        try:
            # 保存登录响应数据用于后续用户信息提取
            if isinstance(data, dict):
                self._login_response_data = data
                
                # 尝试从响应中提取用户ID
                user_id = None
                if 'data' in data and isinstance(data['data'], dict):
                    user_id = data['data'].get('user_id') or data['data'].get('id')
                elif 'user_id' in data:
                    user_id = data['user_id']
                elif 'id' in data:
                    user_id = data['id']
                
                if user_id:
                    self._user_id = str(user_id)
                    self.logger.info(f"从登录响应中提取到用户ID: {user_id}")
                
        except Exception as e:
            self.logger.debug(f"处理登录响应时出错: {e}")
    

    
    async def upload_stock_adjust_file(self, file_path: str) -> Dict[str, Any]:
        """
        上传批量库存修改文件（使用完整认证信息）
        
        Args:
            file_path: 本地文件路径
            
        Returns:
            Dict[str, Any]: API响应结果
        """
        try:
            self.logger.info(f"📤 开始上传库存修改文件: {file_path}")
            
            # 验证文件存在
            if not os.path.exists(file_path):
                raise Exception(f"文件不存在: {file_path}")
            
            # 确保有完整的认证信息
            if not self.auth_info.get('tokens'):
                self.logger.info("🔑 认证信息不存在，开始提取...")
                auth_result = await self.execute_rpa_login_and_extract_info()
                if not auth_result.get('success'):
                    raise Exception("认证信息获取失败")
            
            tokens = self.auth_info['tokens']
            user_info = self.auth_info['user_info']
            
            # 构建API请求
            api_url = urljoin(self.api_config['base_url'], self.api_config['stock_adjust_endpoint'])
            
            # 准备请求参数（使用完整认证信息）
            form_data = aiohttp.FormData()
            
            # 核心认证字段
            if tokens.get('jwt_token'):
                form_data.add_field('jwt_token', tokens['jwt_token'])
                self.logger.debug("添加 JWT Token")
            
            # 关键认证字段（根据memory:2221002，这些是解决认证失败的关键）
            if tokens.get('token1_check'):
                form_data.add_field('token1_check', tokens['token1_check'])
                self.logger.debug("添加 token1_check")
            
            if tokens.get('token2'):
                form_data.add_field('token2', tokens['token2'])
                self.logger.debug("添加 token2")
                
            if tokens.get('token2_timestamp'):
                form_data.add_field('token2_timestamp', tokens['token2_timestamp'])
                self.logger.debug("添加 token2_timestamp")
            
            if tokens.get('sign'):
                form_data.add_field('sign', tokens['sign'])
                self.logger.debug("添加 sign")
            
            if tokens.get('device_number'):
                form_data.add_field('device_number', tokens['device_number'])
                self.logger.debug("添加 device_number")
            
            # 其他必需字段
            form_data.add_field('type', 'save')
            form_data.add_field('anticlimb_verify_code', tokens.get('anticlimb_verify_code', ''))
            
            # 用户ID（如果有）
            if user_info and user_info.user_id:
                form_data.add_field('user_id', user_info.user_id)
                self.logger.debug(f"添加 user_id: {user_info.user_id}")
            
            # 添加文件
            async with aiofiles.open(file_path, 'rb') as f:
                file_content = await f.read()
                file_name = os.path.basename(file_path)
                form_data.add_field('file', file_content, 
                                  filename=file_name, 
                                  # content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                                  content_type='multipart/form-data; boundary=----WebKitFormBoundaryXVdskO9BrWuNwHjj')

            # 准备请求头（包含必要的浏览器标识）
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Referer': 'https://salecentersaas.yibainetwork.com/',
                'Origin': 'https://salecentersaas.yibainetwork.com',
                'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-site'
            }
            
            # 添加Cookie信息
            if tokens.get('cookies'):
                cookie_parts = []
                for name, value in tokens['cookies'].items():
                    cookie_parts.append(f"{name}={value}")
                if cookie_parts:
                    headers['Cookie'] = '; '.join(cookie_parts)
                    self.logger.debug("添加 Cookie 信息")
            
            # 发送API请求
            self.logger.info(f"🌐 发送API请求到: {api_url}")
            self.logger.info("📋 认证信息摘要", extra_data={
                'has_jwt_token': bool(tokens.get('jwt_token')),
                'has_token1_check': bool(tokens.get('token1_check')),
                'has_token2': bool(tokens.get('token2')),
                'has_sign': bool(tokens.get('sign')),
                'has_device_number': bool(tokens.get('device_number')),
                'has_cookies': bool(tokens.get('cookies')),
                'user_id': user_info.user_id if user_info else None,
                'file_size': len(file_content)
            })
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.api_config['timeout'])) as session:
                async with session.post(api_url, data=form_data, headers=headers) as response:
                    # 获取响应
                    status_code = response.status
                    response_text = await response.text()
                    
                    self.logger.info(f"📡 API响应状态码: {status_code}")
                    self.logger.info(f"📄 API响应内容: {response_text}")
                    
                    # 尝试解析JSON响应
                    try:
                        response_data = json.loads(response_text)
                    except json.JSONDecodeError:
                        response_data = {'raw_response': response_text}
                    
                    result = {
                        'success': status_code == 200,
                        'status_code': status_code,
                        'response_data': response_data,
                        'raw_response': response_text,
                        'file_path': file_path,
                        'file_size': len(file_content),
                        'api_url': api_url,
                        'auth_fields_used': {
                            'jwt_token': bool(tokens.get('jwt_token')),
                            'token1_check': bool(tokens.get('token1_check')),
                            'token2': bool(tokens.get('token2')),
                            'sign': bool(tokens.get('sign')),
                            'device_number': bool(tokens.get('device_number')),
                            'cookies': bool(tokens.get('cookies'))
                        }
                    }
                    
                    if result['success']:
                        self.logger.info("✅ 文件上传成功")
                    else:
                        self.logger.error(f"❌ 文件上传失败，状态码: {status_code}")
                    
                    return result
                    
        except Exception as e:
            self.logger.error(f"❌ 文件上传异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'file_path': file_path
            }
    
    def print_response_result(self, result: Dict[str, Any]):
        """
        打印响应结果
        
        Args:
            result: API响应结果
        """
        print("\n" + "="*80)
        print("📊 批量库存修改API调用结果")
        print("="*80)
        
        print(f"🎯 成功状态: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        print(f"📁 文件路径: {result.get('file_path', 'N/A')}")
        print(f"📏 文件大小: {result.get('file_size', 0)} bytes")
        print(f"🌐 API地址: {result.get('api_url', 'N/A')}")
        print(f"📡 状态码: {result.get('status_code', 'N/A')}")
        
        # 显示使用的认证字段
        auth_fields = result.get('auth_fields_used', {})
        print(f"🔐 认证字段使用情况:")
        for field, used in auth_fields.items():
            print(f"  {field}: {'✅' if used else '❌'}")
        
        if result.get('error'):
            print(f"❌ 错误信息: {result['error']}")
        
        print("\n📄 API响应内容:")
        print("-" * 40)
        
        response_data = result.get('response_data', {})
        if isinstance(response_data, dict):
            for key, value in response_data.items():
                print(f"{key}: {value}")
        else:
            print(response_data)
        
        print("\n📝 原始响应:")
        print("-" * 40)
        print(result.get('raw_response', 'N/A'))
        
        print("="*80 + "\n")
    
    async def execute_stock_adjust_upload(self, file_path: str) -> Dict[str, Any]:
        """
        执行完整的库存修改上传流程（使用成熟认证架构）
        
        Args:
            file_path: 本地文件路径
            
        Returns:
            Dict[str, Any]: 完整的执行结果
        """
        try:
            self.logger.info("🚀 开始执行完整的库存修改上传流程（成熟认证架构）...")
            
            # 步骤1: RPA登录和完整认证信息提取
            auth_result = await self.execute_rpa_login_and_extract_info()
            if not auth_result.get('success'):
                return {
                    'success': False,
                    'stage': 'authentication',
                    'error': auth_result.get('error')
                }
            
            # 步骤2: 上传文件（使用完整认证信息）
            upload_result = await self.upload_stock_adjust_file(file_path)
            
            # 步骤3: 打印结果
            self.print_response_result(upload_result)
            
            return {
                'success': upload_result.get('success'),
                'stage': 'upload',
                'auth_info': auth_result,
                'upload_result': upload_result,
                'complete_auth_used': True  # 标识使用了完整认证
            }
            
        except Exception as e:
            self.logger.error(f"❌ 完整流程执行失败: {e}")
            return {
                'success': False,
                'stage': 'execution',
                'error': str(e)
            }


# 使用示例
async def main():
    """主函数示例"""
    
    # 创建API客户端（使用成熟认证架构）
    client = StockAdjustApiClient()
    
    # 指定要上传的文件路径
    file_path = r'E:\pycharm-project\rpa-k8s\app\business\block_unblock_management\test_templates\模板_small_小批量测试_20250705_150837_100条.xlsx'
    
    # 执行完整流程
    result = await client.execute_stock_adjust_upload(file_path)
    
    if result.get('success'):
        print("🎉 库存修改上传完成！")
        print("✅ 已使用完整认证信息，包含所有必需字段")
    else:
        print(f"❌ 上传失败: {result.get('error')}")


if __name__ == "__main__":
    asyncio.run(main()) 