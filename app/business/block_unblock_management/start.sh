#!/bin/bash
set -e
echo "=== RPA Container Starting (block_unblock_management) ==="
echo "Timezone: $(date)"
echo "Python Path: $PYTHONPATH"
echo "Working Directory: $(pwd)"
echo "======================================================"
echo "Environment Variables:"
echo "BUSINESS_TYPE: $BUSINESS_TYPE"
echo "SCRIPT_NAME: $SCRIPT_NAME"
echo "TZ: $TZ"
echo "RPA_MODE: $RPA_MODE"
echo "======================================================"

# Verify browser installation
echo "Verifying browser installation..."
ls -la /home/<USER>/.cache/ms-playwright/ 2>/dev/null && echo "Browser cache directory found" || echo "Browser cache not found"

# Verify core dependencies
echo "Verifying core dependencies..."
python -c "import playwright; print('playwright loaded')"

# Create necessary directories
mkdir -p /app/downloads /app/temp

# Start main business script
echo "Starting block_unblock_management main script..."
echo "======================================================"
export PYTHONPATH=/app:$PYTHONPATH
cd /app && python /app/app/business/block_unblock_management/main.py 