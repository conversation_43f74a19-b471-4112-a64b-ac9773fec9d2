"""
库存管理客户端

实现批量库存更新接口，支持Excel文件上传和异步结果处理
"""

import aiohttp
import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urljoin

from app.shared.clients.base_yimai_client import BaseYimaiClient
from app.utils.logger import get_rpa_logger


class StockClient(BaseYimaiClient):
    """
    库存管理客户端
    
    继承BaseYimaiClient，专门处理批量库存更新相关的接口调用
    """
    
    def __init__(self, logger=None, base_url: str = None):
        """
        初始化库存管理客户端
        
        Args:
            logger: 日志记录器
            base_url: 基础URL，如果未提供则使用默认配置
        """
        # 准备配置
        config = {}
        if base_url:
            config['base_url'] = base_url
            
        super().__init__(logger, config)
        
        # 库存管理接口配置
        self.stock_endpoints = {
            'batch_import': '/listing/online/listing_management/listing_stock_adjust_import',
            'import_status': '/listing/online/listing_management/listing_stock_adjust_import_status',
            'import_result': '/listing/online/listing_management/listing_stock_adjust_import_result'
        }
        
        # 上传配置
        self.upload_config = {
            'max_file_size': 50 * 1024 * 1024,  # 50MB
            'timeout': 300,  # 5分钟超时
            'retry_count': 3,
            'retry_delay': 2
        }
        
        self.logger.info("库存管理客户端初始化完成", step="stock_client_init", extra_data={
            'endpoints': list(self.stock_endpoints.keys()),
            'upload_config': self.upload_config
        })
    
    async def batch_update_stock(self, 
                                excel_bytes: bytes, 
                                filename: str,
                                tokens: Dict[str, str]) -> Dict[str, Any]:
        """
        批量更新库存
        
        Args:
            excel_bytes: Excel文件字节流
            filename: 文件名
            tokens: 认证Token字典
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            self.logger.info("开始批量库存更新", step="batch_update_start", extra_data={
                'filename': filename,
                'file_size': f"{len(excel_bytes) / 1024:.2f}KB"
            })
            
            # 验证文件大小
            if len(excel_bytes) > self.upload_config['max_file_size']:
                raise ValueError(f"文件大小超过限制: {len(excel_bytes) / 1024 / 1024:.2f}MB > {self.upload_config['max_file_size'] / 1024 / 1024}MB")
            
            # 执行上传
            upload_result = await self._upload_excel_file(excel_bytes, filename, tokens)
            
            if not upload_result.get('success'):
                return {
                    'success': False,
                    'message': f"文件上传失败: {upload_result.get('message', '未知错误')}",
                    'error_code': upload_result.get('error_code'),
                    'response': upload_result.get('response')
                }
            
            response_data = upload_result.get('response', {})
            status = response_data.get('status')
            
            if status == 1:
                # 异步导入成功
                self.logger.info("批量库存更新提交成功（异步模式）", step="batch_update_submitted", extra_data={
                    'response': response_data,
                    'import_id': upload_result.get('import_id')
                })
                
                return {
                    'success': True,
                    'mode': 'async',
                    'message': response_data.get('error_mess', ''),
                    'import_id': upload_result.get('import_id'),
                    'status': status,
                    'response': response_data,
                    'next_action': '请到【系统导入管理】模块查看导入结果'
                }
                
            elif status == 0:
                # 导入失败
                error_message = response_data.get('error_mess', '导入失败')
                self.logger.error("批量库存更新失败", step="batch_update_failed", extra_data={
                    'error_message': error_message,
                    'response': response_data
                })
                
                return {
                    'success': False,
                    'mode': 'sync',
                    'message': error_message,
                    'status': status,
                    'response': response_data,
                    'error_type': 'format_error' if '表头模板格式不正确' in error_message else 'unknown_error'
                }
            
            else:
                # 未知状态
                self.logger.warning("批量库存更新返回未知状态", step="batch_update_unknown", extra_data={
                    'status': status,
                    'response': response_data
                })
                
                return {
                    'success': False,
                    'mode': 'unknown',
                    'message': f"未知状态: {status}",
                    'status': status,
                    'response': response_data
                }
            
        except Exception as e:
            self.logger.error(f"批量库存更新异常: {str(e)}")
            return {
                'success': False,
                'message': f'批量库存更新异常: {str(e)}',
                'error': str(e),
                'exception_type': type(e).__name__
            }
    
    async def _upload_excel_file(self, 
                                excel_bytes: bytes, 
                                filename: str, 
                                tokens: Dict[str, str]) -> Dict[str, Any]:
        """
        上传Excel文件到批量导入接口
        
        Args:
            excel_bytes: Excel文件字节流
            filename: 文件名
            tokens: 认证Token字典
            
        Returns:
            Dict[str, Any]: 上传结果
        """
        # 构建完整的URL，包含jwt_token参数
        base_url = urljoin(self.config['base_url'], self.stock_endpoints['batch_import'])
        jwt_token = tokens.get('jwt_token', '')
        url = f"{base_url}?jwt_token={jwt_token}"
        
        # 准备表单数据
        form_data = aiohttp.FormData()
        
        # 添加必需的表单字段
        form_data.add_field('type', 'save')  # 关键字段：操作类型
        form_data.add_field('anticlimb_verify_code', tokens.get('anticlimb_verify_code', ''))
        
        # 添加文件
        form_data.add_field(
            'file',
            excel_bytes,
            filename=filename,
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # 准备完整的请求头（基于真实请求）
        headers = {
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Origin': 'https://salecentersaas.yibainetwork.com',
            'Referer': 'https://salecentersaas.yibainetwork.com/',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Priority': 'u=1, i',
            'Exclude': 'file'
        }
        
        # 添加认证相关头部（这些是关键的缺失字段）
        if tokens.get('token1_check'):
            headers['token1-check'] = tokens['token1_check']
        
        if tokens.get('token2'):
            headers['token2'] = tokens['token2']
            
        if tokens.get('token2_timestamp'):
            headers['token2-timestamp'] = str(tokens['token2_timestamp'])
            
        # 添加签名（如果存在）
        if tokens.get('sign'):
            headers['sign'] = tokens['sign']
            
        # 添加authorization头（即使为空也要包含）
        headers['authorization'] = tokens.get('authorization', '')
        
        retry_count = 0
        max_retries = self.upload_config['retry_count']
        
        while retry_count <= max_retries:
            try:
                self.logger.info(f"尝试上传Excel文件 (尝试 {retry_count + 1}/{max_retries + 1})", extra_data={
                    'url': url,
                    'filename': filename,
                    'file_size': f"{len(excel_bytes) / 1024:.2f}KB",
                    'has_token1_check': bool(tokens.get('token1_check')),
                    'has_token2': bool(tokens.get('token2')),
                    'has_sign': bool(tokens.get('sign'))
                })
                
                timeout = aiohttp.ClientTimeout(total=self.upload_config['timeout'])
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.post(url, data=form_data, headers=headers) as response:
                        # 记录响应信息
                        response_text = await response.text()
                        
                        self.logger.info("批量导入接口响应", step="upload_response", extra_data={
                            'status_code': response.status,
                            'content_type': response.content_type,
                            'response_length': len(response_text)
                        })
                        
                        if response.status == 200:
                            try:
                                response_data = json.loads(response_text)
                                
                                # 生成导入ID（用于追踪）
                                import_id = f"import_{int(time.time())}_{hash(filename) % 10000}"
                                
                                self.logger.info("批量库存更新提交成功（异步模式）", step="batch_update_submitted", extra_data={
                                    'response': response_data,
                                    'import_id': import_id
                                })
                                
                                return {
                                    'success': True,
                                    'response': response_data,
                                    'status_code': response.status,
                                    'import_id': import_id,
                                    'upload_time': datetime.now().isoformat()
                                }
                                
                            except json.JSONDecodeError as e:
                                self.logger.error(f"响应JSON解析失败: {str(e)}", extra_data={
                                    'response_text': response_text[:500]  # 只记录前500字符
                                })
                                
                                return {
                                    'success': False,
                                    'message': 'JSON解析失败',
                                    'error_code': 'json_parse_error',
                                    'response_text': response_text,
                                    'status_code': response.status
                                }
                        
                        else:
                            self.logger.error(f"上传请求失败: HTTP {response.status}", extra_data={
                                'response_text': response_text[:500]
                            })
                            
                            if retry_count < max_retries:
                                retry_count += 1
                                await asyncio.sleep(self.upload_config['retry_delay'])
                                continue
                            
                            return {
                                'success': False,
                                'message': f'HTTP请求失败: {response.status}',
                                'error_code': f'http_{response.status}',
                                'response_text': response_text,
                                'status_code': response.status
                            }
            
            except asyncio.TimeoutError:
                self.logger.warning(f"上传超时 (尝试 {retry_count + 1})")
                
                if retry_count < max_retries:
                    retry_count += 1
                    await asyncio.sleep(self.upload_config['retry_delay'])
                    continue
                
                return {
                    'success': False,
                    'message': '上传超时',
                    'error_code': 'timeout',
                    'timeout_seconds': self.upload_config['timeout']
                }
            
            except Exception as e:
                self.logger.error(f"上传异常 (尝试 {retry_count + 1}): {str(e)}")
                
                if retry_count < max_retries:
                    retry_count += 1
                    await asyncio.sleep(self.upload_config['retry_delay'])
                    continue
                
                return {
                    'success': False,
                    'message': f'上传异常: {str(e)}',
                    'error_code': 'upload_exception',
                    'exception_type': type(e).__name__
                }
        
        # 如果到达这里，说明所有重试都失败了
        return {
            'success': False,
            'message': f'上传失败，已重试{max_retries}次',
            'error_code': 'max_retries_exceeded',
            'retry_count': retry_count
        }
    
    async def check_import_status(self, 
                                 import_id: str, 
                                 tokens: Dict[str, str]) -> Dict[str, Any]:
        """
        检查导入状态（预留接口，用于后续扩展）
        
        Args:
            import_id: 导入ID
            tokens: 认证Token字典
            
        Returns:
            Dict[str, Any]: 状态查询结果
        """
        try:
            # TODO: 实现导入状态查询接口
            # 这需要找到相应的API接口
            
            self.logger.info("查询导入状态", step="check_import_status", extra_data={
                'import_id': import_id
            })
            
            return {
                'success': False,
                'message': '导入状态查询接口尚未实现',
                'import_id': import_id,
                'note': '请手动到【系统导入管理】模块查看'
            }
            
        except Exception as e:
            self.logger.error(f"查询导入状态异常: {str(e)}")
            return {
                'success': False,
                'message': f'查询导入状态异常: {str(e)}',
                'import_id': import_id,
                'error': str(e)
            }
    
    def validate_tokens(self, tokens: Dict[str, str]) -> Tuple[bool, List[str]]:
        """
        验证Token的完整性
        
        Args:
            tokens: Token字典
            
        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误列表)
        """
        errors = []
        required_tokens = ['jwt_token']
        
        for token_name in required_tokens:
            if not tokens.get(token_name):
                errors.append(f"缺少必需的Token: {token_name}")
        
        # 验证jwt_token格式
        jwt_token = tokens.get('jwt_token', '')
        if jwt_token and not jwt_token.startswith('eyJ'):
            errors.append("jwt_token格式不正确")
        
        is_valid = len(errors) == 0
        
        if is_valid:
            self.logger.debug("Token验证通过")
        else:
            self.logger.warning("Token验证失败", extra_data={'errors': errors})
        
        return is_valid, errors 