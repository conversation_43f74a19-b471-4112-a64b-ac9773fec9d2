"""
Excel数据处理器

负责解析多sheet Excel文件，提取屏蔽和解屏蔽SKU数据
"""

import asyncio
import logging
import pandas as pd
from io import BytesIO
from typing import Dict, List, Optional, Any, Set, Tuple

from app.shared.clients.base_yimai_client import BaseYimaiClient
from app.business.block_unblock_management.config import BlockUnblockConfig
from app.business.block_unblock_management.notice_client import NoticeClient


class BlockExcelProcessor:
    """
    屏蔽Excel处理器
    
    专门处理屏蔽与解屏蔽相关的Excel文件数据解析
    """
    
    def __init__(self, logger: logging.Logger = None):
        """
        初始化Excel处理器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.config = BlockUnblockConfig.get_default_config()
        
        # 初始化公告客户端用于文件下载
        self.notice_client = NoticeClient(self.logger)
        
        # 数据统计
        self.processing_stats = {
            'files_processed': 0,
            'sheets_processed': 0,
            'block_skus_found': 0,
            'unblock_skus_found': 0,
            'invalid_rows': 0,
            'errors': []
        }
        
        self.logger.info("Excel数据处理器初始化完成")
    
    async def process_notice_files(self, 
                                  notice_files: List[Dict[str, Any]], 
                                  tokens: Dict[str, str]) -> Dict[str, Any]:
        """
        处理公告文件列表
        
        Args:
            notice_files: 公告文件列表，每个元素包含file_url等信息
            tokens: 认证Token
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.logger.info(f"开始处理 {len(notice_files)} 个公告文件")
            
            # 重置统计
            self._reset_stats()
            
            all_block_skus = set()
            all_unblock_skus = set()
            processed_files = []
            
            for file_info in notice_files:
                try:
                    file_url = file_info.get('file_url')
                    if not file_url:
                        self.logger.warning("文件信息中缺少file_url，跳过")
                        continue
                    
                    # 检查是否为Excel文件（根据URL或文件类型判断）
                    file_type = file_info.get('file_type', 'unknown')
                    if file_type != 'excel' and not file_url.lower().endswith(('.xlsx', '.xls')):
                        self.logger.warning(f"跳过非Excel文件: {file_url}")
                        continue
                    
                    # 下载文件到内存
                    self.logger.info(f"开始下载Excel文件: {file_url}")
                    file_content = await self.notice_client.download_file_from_url(file_url, tokens)
                    
                    # 处理Excel文件
                    file_result = await self._process_excel_file(file_content, file_info)
                    
                    if file_result:
                        all_block_skus.update(file_result.get('block_skus', set()))
                        all_unblock_skus.update(file_result.get('unblock_skus', set()))
                        processed_files.append({
                            'file_url': file_url,
                            'title': file_info.get('title', ''),
                            'result': file_result
                        })
                        
                        self.processing_stats['files_processed'] += 1
                    
                except Exception as e:
                    error_msg = f"处理文件失败: {file_info.get('file_url', 'Unknown')} - {str(e)}"
                    self.logger.error(error_msg)
                    self.processing_stats['errors'].append(error_msg)
                    continue
            
            # 去重并转换为列表
            final_block_skus = list(all_block_skus)
            final_unblock_skus = list(all_unblock_skus)
            
            # 更新统计
            self.processing_stats['block_skus_found'] = len(final_block_skus)
            self.processing_stats['unblock_skus_found'] = len(final_unblock_skus)
            
            result = {
                'block_skus': final_block_skus,
                'unblock_skus': final_unblock_skus,
                'processed_files': processed_files,
                'statistics': self.processing_stats.copy()
            }
            
            self.logger.info("公告文件处理完成", extra_data={
                'block_skus_count': len(final_block_skus),
                'unblock_skus_count': len(final_unblock_skus),
                'files_processed': self.processing_stats['files_processed']
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理公告文件列表失败: {str(e)}")
            raise
    
    async def _process_excel_file(self, 
                                 file_content: bytes, 
                                 file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个Excel文件
        
        Args:
            file_content: 文件内容字节流
            file_info: 文件信息
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            file_url = file_info.get('file_url', 'Unknown')
            file_title = file_info.get('title', '屏蔽与释放SKU明细')
            
            self.logger.info(f"开始解析Excel文件: {file_title}")
            
            # 验证文件内容
            if not file_content or len(file_content) < 100:
                raise ValueError("文件内容为空或过小，可能下载失败")
            
            # 将字节流转换为BytesIO对象
            excel_buffer = BytesIO(file_content)
            
            # 读取Excel文件的所有sheet
            try:
                excel_file = pd.ExcelFile(excel_buffer)
                sheet_names = excel_file.sheet_names
            except Exception as e:
                raise ValueError(f"无法解析Excel文件，可能文件格式错误: {str(e)}")
            
            self.logger.info(f"Excel文件包含 {len(sheet_names)} 个sheet", extra_data={
                'sheet_names': sheet_names,
                'file_size': len(file_content)
            })
            
            block_skus = set()
            unblock_skus = set()
            processed_sheets = []
            
            # 处理每个sheet
            for sheet_name in sheet_names:
                try:
                    sheet_result = await self._process_excel_sheet(excel_file, sheet_name)
                    
                    if sheet_result:
                        block_skus.update(sheet_result.get('block_skus', set()))
                        unblock_skus.update(sheet_result.get('unblock_skus', set()))
                        
                        # 确保sheet_result中的set被转换为list
                        if 'block_skus' in sheet_result and isinstance(sheet_result['block_skus'], set):
                            sheet_result['block_skus'] = list(sheet_result['block_skus'])
                        if 'unblock_skus' in sheet_result and isinstance(sheet_result['unblock_skus'], set):
                            sheet_result['unblock_skus'] = list(sheet_result['unblock_skus'])
                            
                        processed_sheets.append(sheet_result)
                        self.processing_stats['sheets_processed'] += 1
                        
                except Exception as e:
                    error_msg = f"处理sheet失败: {sheet_name} - {str(e)}"
                    self.logger.warning(error_msg)
                    self.processing_stats['errors'].append(error_msg)
                    continue
            
            # 确保返回的数据结构中不包含set类型
            result = {
                'file_url': file_url,
                'title': file_title,
                'block_skus': list(block_skus),  # 转换为list
                'unblock_skus': list(unblock_skus),  # 转换为list
                'total_sheets': len(sheet_names),
                'processed_sheets': processed_sheets,
                'file_size': len(file_content)
            }
            
            self.logger.info(f"Excel文件解析完成", extra_data={
                'file_url': file_url,
                'block_skus_count': len(block_skus),
                'unblock_skus_count': len(unblock_skus),
                'processed_sheets_count': len(processed_sheets)
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理Excel文件失败: {str(e)}")
            raise
    
    async def _process_excel_sheet(self, 
                                  excel_file: pd.ExcelFile, 
                                  sheet_name: str) -> Optional[Dict[str, Any]]:
        """
        处理Excel单个sheet
        
        Args:
            excel_file: Excel文件对象
            sheet_name: Sheet名称
            
        Returns:
            Optional[Dict[str, Any]]: 处理结果
        """
        try:
            self.logger.debug(f"开始处理sheet: {sheet_name}")
            
            # 读取sheet数据
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            
            if df.empty:
                self.logger.warning(f"Sheet为空: {sheet_name}")
                return None
            
            # 判断sheet类型并处理
            if self._is_block_sheet(sheet_name):
                # 处理屏蔽sheet
                return await self._process_block_sheet(df, sheet_name)
            elif self._is_unblock_sheet(sheet_name):
                # 处理解屏蔽sheet
                return await self._process_unblock_sheet(df, sheet_name)
            else:
                self.logger.debug(f"跳过不相关的sheet: {sheet_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"处理sheet失败: {sheet_name} - {str(e)}")
            raise
    
    def _is_block_sheet(self, sheet_name: str) -> bool:
        """
        判断是否为屏蔽相关的sheet
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            bool: 是否为屏蔽sheet
        """
        block_pattern = self.config['sheet_patterns']['block']
        return block_pattern in sheet_name
    
    def _is_unblock_sheet(self, sheet_name: str) -> bool:
        """
        判断是否为解屏蔽相关的sheet
        
        Args:
            sheet_name: Sheet名称
            
        Returns:
            bool: 是否为解屏蔽sheet
        """
        unblock_pattern = self.config['sheet_patterns']['unblock']
        return unblock_pattern in sheet_name
    
    async def _process_block_sheet(self, 
                                  df: pd.DataFrame, 
                                  sheet_name: str) -> Dict[str, Any]:
        """
        处理屏蔽sheet数据
        
        Args:
            df: DataFrame数据
            sheet_name: Sheet名称
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.logger.info(f"处理屏蔽sheet: {sheet_name}")
            
            # 获取列名配置
            condition_column = self.config['column_mapping']['block_condition']
            sku_column = self.config['column_mapping']['sku']
            condition_value = self.config['filter_conditions']['block_value']
            
            self.logger.debug(f"查找列: 条件列='{condition_column}', SKU列='{sku_column}', 条件值='{condition_value}'")
            
            # 查找实际的列名
            actual_condition_column = self._find_actual_column_name(df, condition_column)
            actual_sku_column = self._find_actual_column_name(df, sku_column)
            
            if not actual_condition_column:
                raise ValueError(f"未找到条件列: {condition_column}")
            if not actual_sku_column:
                raise ValueError(f"未找到SKU列: {sku_column}")
            
            self.logger.info(f"使用列: 条件列='{actual_condition_column}', SKU列='{actual_sku_column}'")
            
            # 筛选符合条件的行
            filtered_df = df[df[actual_condition_column] == condition_value]
            
            if filtered_df.empty:
                self.logger.warning(f"在sheet '{sheet_name}' 中未找到符合屏蔽条件'{condition_value}'的数据")
                return {
                    'block_skus': [], 
                    'unblock_skus': [],
                    'sheet_name': sheet_name,
                    'sheet_type': 'block',
                    'total_rows': len(df),
                    'filtered_rows': 0
                }
            
            # 提取SKU列表
            block_skus = self._extract_valid_skus(filtered_df, actual_sku_column)
            
            self.logger.info(f"从sheet '{sheet_name}' 提取到 {len(block_skus)} 个屏蔽SKU")
            
            return {
                'block_skus': list(block_skus),  # 转换为list
                'unblock_skus': [],
                'sheet_name': sheet_name,
                'sheet_type': 'block',
                'total_rows': len(df),
                'filtered_rows': len(filtered_df),
                'condition_column': actual_condition_column,
                'sku_column': actual_sku_column
            }
            
        except Exception as e:
            self.logger.error(f"处理屏蔽sheet失败: {sheet_name} - {str(e)}")
            raise
    
    async def _process_unblock_sheet(self, 
                                    df: pd.DataFrame, 
                                    sheet_name: str) -> Dict[str, Any]:
        """
        处理解屏蔽sheet数据
        
        Args:
            df: DataFrame数据
            sheet_name: Sheet名称
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.logger.info(f"处理解屏蔽sheet: {sheet_name}")
            
            # 获取列名配置
            condition_column = self.config['column_mapping']['unblock_condition']
            sku_column = self.config['column_mapping']['sku']
            condition_value = self.config['filter_conditions']['unblock_value']
            
            self.logger.debug(f"查找列: 条件列='{condition_column}', SKU列='{sku_column}', 条件值='{condition_value}'")
            
            # 查找实际的列名
            actual_condition_column = self._find_actual_column_name(df, condition_column)
            actual_sku_column = self._find_actual_column_name(df, sku_column)
            
            if not actual_condition_column:
                raise ValueError(f"未找到条件列: {condition_column}")
            if not actual_sku_column:
                raise ValueError(f"未找到SKU列: {sku_column}")
            
            self.logger.info(f"使用列: 条件列='{actual_condition_column}', SKU列='{actual_sku_column}'")
            
            # 筛选符合条件的行
            filtered_df = df[df[actual_condition_column] == condition_value]
            
            if filtered_df.empty:
                self.logger.warning(f"在sheet '{sheet_name}' 中未找到符合解屏蔽条件'{condition_value}'的数据")
                return {
                    'block_skus': [], 
                    'unblock_skus': [],
                    'sheet_name': sheet_name,
                    'sheet_type': 'unblock',
                    'total_rows': len(df),
                    'filtered_rows': 0
                }
            
            # 提取SKU列表
            unblock_skus = self._extract_valid_skus(filtered_df, actual_sku_column)
            
            self.logger.info(f"从sheet '{sheet_name}' 提取到 {len(unblock_skus)} 个解屏蔽SKU")
            
            return {
                'block_skus': [],
                'unblock_skus': list(unblock_skus),  # 转换为list
                'sheet_name': sheet_name,
                'sheet_type': 'unblock',
                'total_rows': len(df),
                'filtered_rows': len(filtered_df),
                'condition_column': actual_condition_column,
                'sku_column': actual_sku_column
            }
            
        except Exception as e:
            self.logger.error(f"处理解屏蔽sheet失败: {sheet_name} - {str(e)}")
            raise
    
    def _check_required_columns(self, df: pd.DataFrame, required_columns: List[str]) -> List[str]:
        """
        检查DataFrame中是否存在必需的列，支持模糊匹配
        
        Args:
            df: DataFrame数据
            required_columns: 必需列名列表
            
        Returns:
            List[str]: 缺失的列名列表
        """
        missing_columns = []
        df_columns = df.columns.tolist()
        
        self.logger.debug(f"DataFrame列名: {df_columns}")
        
        for required_col in required_columns:
            # 先尝试精确匹配
            if required_col in df_columns:
                continue
            
            # 尝试模糊匹配（去除空格、大小写不敏感）
            found = False
            for col in df_columns:
                if isinstance(col, str) and isinstance(required_col, str):
                    if col.strip().lower() == required_col.strip().lower():
                        found = True
                        break
                    # 或者检查是否包含关键词
                    if required_col.strip().lower() in col.strip().lower():
                        found = True
                        self.logger.info(f"使用模糊匹配: 需要'{required_col}', 找到'{col}'")
                        break
            
            if not found:
                missing_columns.append(required_col)
                self.logger.warning(f"未找到列: {required_col}")
        
        return missing_columns
    
    def _extract_valid_skus(self, df: pd.DataFrame, sku_column: str) -> Set[str]:
        """
        从DataFrame中提取有效的SKU列表，支持模糊列名匹配
        
        Args:
            df: DataFrame数据
            sku_column: SKU列名
            
        Returns:
            Set[str]: 有效SKU集合
        """
        valid_skus = set()
        
        # 查找实际的SKU列名
        actual_sku_column = self._find_actual_column_name(df, sku_column)
        if not actual_sku_column:
            self.logger.error(f"未找到SKU列: {sku_column}")
            return valid_skus
        
        self.logger.debug(f"使用SKU列: {actual_sku_column}")
        
        for index, row in df.iterrows():
            try:
                sku = row[actual_sku_column]
                
                # 验证SKU有效性
                if self._is_valid_sku(sku):
                    valid_skus.add(str(sku).strip())
                else:
                    self.processing_stats['invalid_rows'] += 1
                    self.logger.debug(f"无效SKU (行{index + 2}): {sku}")
                    
            except Exception as e:
                self.logger.warning(f"提取SKU时出错 (行{index + 2}): {str(e)}")
                self.processing_stats['invalid_rows'] += 1
                continue
        
        return valid_skus
    
    def _find_actual_column_name(self, df: pd.DataFrame, target_column: str) -> Optional[str]:
        """
        查找实际的列名，支持模糊匹配
        
        Args:
            df: DataFrame数据
            target_column: 目标列名
            
        Returns:
            Optional[str]: 实际的列名，如果没找到返回None
        """
        df_columns = df.columns.tolist()
        
        # 精确匹配
        if target_column in df_columns:
            return target_column
        
        # 模糊匹配
        for col in df_columns:
            if isinstance(col, str) and isinstance(target_column, str):
                # 去除空格后比较
                if col.strip().lower() == target_column.strip().lower():
                    return col
                # 检查是否包含关键词
                if target_column.strip().lower() in col.strip().lower():
                    return col
                # 对于SKU列，特殊处理
                if target_column.lower() == 'sku' and 'sku' in col.lower():
                    return col
        
        return None
    
    def _is_valid_sku(self, sku: Any) -> bool:
        """
        验证SKU是否有效
        
        Args:
            sku: SKU值
            
        Returns:
            bool: 是否有效
        """
        if pd.isna(sku) or sku is None:
            return False
        
        sku_str = str(sku).strip()
        
        if not sku_str or sku_str.lower() in ['nan', 'none', 'null', '']:
            return False
        
        # 可以根据实际需求添加更多验证规则
        if len(sku_str) < 3:  # SKU长度至少3位
            return False
        
        return True
    
    def _reset_stats(self):
        """重置处理统计"""
        self.processing_stats = {
            'files_processed': 0,
            'sheets_processed': 0,
            'block_skus_found': 0,
            'unblock_skus_found': 0,
            'invalid_rows': 0,
            'errors': []
        }
    
    async def process_single_excel_file(self, 
                                       file_path: str) -> Dict[str, Any]:
        """
        处理单个本地Excel文件（用于测试）
        
        Args:
            file_path: 本地文件路径
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.logger.info(f"处理本地Excel文件: {file_path}")
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 构建文件信息
            file_info = {
                'filename': file_path.split('/')[-1],
                'is_excel': True
            }
            
            # 处理文件
            return await self._process_excel_file(file_content, file_info)
            
        except Exception as e:
            self.logger.error(f"处理本地Excel文件失败: {str(e)}")
            raise
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return self.processing_stats.copy()
    
    def update_config(self, config_updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            config_updates: 配置更新字典
        """
        self.config.update(config_updates)
        self.logger.info("Excel处理器配置已更新", extra_data={
            'updated_keys': list(config_updates.keys())
        })
    
    async def process_excel_content(self, file_content: bytes) -> Dict[str, Any]:
        """
        处理Excel文件内容
        
        Args:
            file_content: 文件内容字节流
            
        Returns:
            Dict[str, Any]: 处理结果，包含block_skus和unblock_skus列表
        """
        try:
            self.logger.info("开始处理Excel文件内容")
            
            # 构建文件信息
            file_info = {
                'file_url': 'memory_content',
                'title': 'Excel文件内容',
                'file_type': 'excel'
            }
            
            # 调用现有的处理方法
            result = await self._process_excel_file(file_content, file_info)
            
            self.logger.info("Excel文件内容处理完成", extra_data={
                'block_skus_count': len(result.get('block_skus', [])),
                'unblock_skus_count': len(result.get('unblock_skus', []))
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理Excel文件内容失败: {str(e)}")
            raise 