# 屏蔽解屏蔽管理模块 Dockerfile
# 基于项目基础镜像构建

FROM crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-k8s-base:latest

# 设置工作目录
WORKDIR /app

# 复制屏蔽解屏蔽管理模块相关文件
COPY app/business/block_unblock_management/ /app/app/business/block_unblock_management/
# 复制导出刊登成功列表模块（依赖模块）
COPY app/business/export_publish_list/ /app/app/business/export_publish_list/
COPY app/shared/ /app/app/shared/
COPY app/core/ /app/app/core/
COPY app/config/ /app/app/config/
COPY app/utils/ /app/app/utils/

# 复制主程序入口
COPY app/main.py /app/app/main.py

# ❌ 移除重复的依赖安装（基础镜像已包含所有依赖）
# COPY requirements.txt /app/
# RUN pip install --no-cache-dir -r requirements.txt

# 确保浏览器权限正确（基础镜像中已包含浏览器缓存）
RUN chown -R rpauser:rpauser /home/<USER>

# 创建必要的目录并设置权限
RUN mkdir -p /app/logs /app/downloads /app/temp && \
    chown -R rpauser:rpauser /app/logs /app/downloads /app/temp

# 设置环境变量
ENV BUSINESS_TYPE=block_unblock_management
ENV SCRIPT_NAME=block_unblock_management
ENV PYTHONPATH=/app
ENV LOG_LEVEL=INFO
ENV RPA_MODE=headless
ENV TZ=Asia/Shanghai
ENV PLAYWRIGHT_HEADLESS=true
ENV PLAYWRIGHT_TIMEOUT=45
ENV PLAYWRIGHT_PAGE_LOAD_WAIT=15

# 复制启动脚本（避免复杂的转义问题）
COPY app/business/block_unblock_management/start.sh /app/start.sh

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 切换到非root用户
USER rpauser

# ❌ 移除重复的浏览器预安装（基础镜像已包含或运行时安装）
# RUN python -m playwright install chromium

# 运行屏蔽解屏蔽管理程序
ENTRYPOINT ["bash", "/app/start.sh"] 