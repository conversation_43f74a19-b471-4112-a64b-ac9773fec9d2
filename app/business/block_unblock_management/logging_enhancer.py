"""
日志增强器和链路追踪模块

提供完整的日志追踪、错误定位和性能监控功能
确保在出现问题时能快速定位和解决
"""

import time
import uuid
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from contextlib import asynccontextmanager
from functools import wraps
from dataclasses import dataclass, asdict

from app.utils.logger import get_rpa_logger


@dataclass
class ExecutionContext:
    """执行上下文信息"""
    trace_id: str
    step_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    success: bool = False
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    performance_metrics: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['start_time'] = self.start_time.isoformat() if self.start_time else None
        data['end_time'] = self.end_time.isoformat() if self.end_time else None
        data['duration'] = (
            (self.end_time - self.start_time).total_seconds() 
            if self.end_time and self.start_time else None
        )
        return data


class ErrorCodes:
    """统一错误码定义"""
    
    # 系统级错误 (1xxx)
    SYSTEM_ERROR = "1000"
    CONFIG_ERROR = "1001"
    NETWORK_ERROR = "1002"
    DATABASE_ERROR = "1003"
    
    # 认证相关错误 (2xxx)
    AUTH_LOGIN_FAILED = "2001"
    AUTH_TOKEN_INVALID = "2002"
    AUTH_TOKEN_EXPIRED = "2003"
    AUTH_PERMISSION_DENIED = "2004"
    
    # 数据处理错误 (3xxx)
    DATA_NOT_FOUND = "3001"
    DATA_FORMAT_ERROR = "3002"
    DATA_VALIDATION_ERROR = "3003"
    DATA_PARSING_ERROR = "3004"
    
    # 文件处理错误 (4xxx)
    FILE_NOT_FOUND = "4001"
    FILE_DOWNLOAD_ERROR = "4002"
    FILE_PARSE_ERROR = "4003"
    FILE_UPLOAD_ERROR = "4004"
    FILE_FORMAT_ERROR = "4005"
    
    # 接口调用错误 (5xxx)
    API_REQUEST_FAILED = "5001"
    API_TIMEOUT = "5002"
    API_RESPONSE_ERROR = "5003"
    API_RATE_LIMIT = "5004"
    
    # 业务逻辑错误 (6xxx)
    BUSINESS_RULE_VIOLATION = "6001"
    BUSINESS_DATA_INCONSISTENT = "6002"
    BUSINESS_OPERATION_FAILED = "6003"
    
    @classmethod
    def get_error_description(cls, error_code: str) -> str:
        """获取错误码描述"""
        descriptions = {
            cls.SYSTEM_ERROR: "系统错误",
            cls.CONFIG_ERROR: "配置错误",
            cls.NETWORK_ERROR: "网络错误",
            cls.DATABASE_ERROR: "数据库错误",
            
            cls.AUTH_LOGIN_FAILED: "登录失败",
            cls.AUTH_TOKEN_INVALID: "Token无效",
            cls.AUTH_TOKEN_EXPIRED: "Token过期",
            cls.AUTH_PERMISSION_DENIED: "权限不足",
            
            cls.DATA_NOT_FOUND: "数据未找到",
            cls.DATA_FORMAT_ERROR: "数据格式错误",
            cls.DATA_VALIDATION_ERROR: "数据验证失败",
            cls.DATA_PARSING_ERROR: "数据解析错误",
            
            cls.FILE_NOT_FOUND: "文件未找到",
            cls.FILE_DOWNLOAD_ERROR: "文件下载失败",
            cls.FILE_PARSE_ERROR: "文件解析失败",
            cls.FILE_UPLOAD_ERROR: "文件上传失败",
            cls.FILE_FORMAT_ERROR: "文件格式错误",
            
            cls.API_REQUEST_FAILED: "API请求失败",
            cls.API_TIMEOUT: "API请求超时",
            cls.API_RESPONSE_ERROR: "API响应错误",
            cls.API_RATE_LIMIT: "API调用频率限制",
            
            cls.BUSINESS_RULE_VIOLATION: "业务规则违反",
            cls.BUSINESS_DATA_INCONSISTENT: "业务数据不一致",
            cls.BUSINESS_OPERATION_FAILED: "业务操作失败"
        }
        return descriptions.get(error_code, f"未知错误码: {error_code}")


class LoggingEnhancer:
    """日志增强器"""
    
    def __init__(self, logger_name: str = "block_unblock_management"):
        """
        初始化日志增强器
        
        Args:
            logger_name: 日志器名称
        """
        self.logger = get_rpa_logger("block_unblock_management", "logging_enhancer")
        self.trace_contexts: Dict[str, ExecutionContext] = {}
        self.performance_metrics: Dict[str, List[float]] = {}
        
        self.logger.info("日志增强器初始化完成", step="logging_enhancer_init")
    
    def generate_trace_id(self) -> str:
        """生成链路追踪ID"""
        return f"trace_{int(time.time())}_{str(uuid.uuid4())[:8]}"
    
    @asynccontextmanager
    async def trace_execution(self, step_name: str, trace_id: str = None):
        """
        执行链路追踪上下文管理器
        
        Args:
            step_name: 步骤名称
            trace_id: 链路追踪ID，如果未提供则自动生成
        """
        if not trace_id:
            trace_id = self.generate_trace_id()
        
        context = ExecutionContext(
            trace_id=trace_id,
            step_name=step_name,
            start_time=datetime.now(),
            performance_metrics={}
        )
        
        self.trace_contexts[trace_id] = context
        
        # 记录步骤开始
        self.logger.info(f"🚀 开始执行: {step_name}", step="execution_start", extra_data={
            'trace_id': trace_id,
            'step_name': step_name,
            'start_time': context.start_time.isoformat()
        })
        
        start_time = time.time()
        
        try:
            yield context
            
            # 执行成功
            context.end_time = datetime.now()
            context.success = True
            execution_time = time.time() - start_time
            context.performance_metrics['execution_time'] = execution_time
            
            # 记录性能指标
            if step_name not in self.performance_metrics:
                self.performance_metrics[step_name] = []
            self.performance_metrics[step_name].append(execution_time)
            
            self.logger.info(f"✅ 执行成功: {step_name}", step="execution_success", extra_data={
                'trace_id': trace_id,
                'step_name': step_name,
                'execution_time': f"{execution_time:.3f}s",
                'context': context.to_dict()
            })
            
        except Exception as e:
            # 执行失败
            context.end_time = datetime.now()
            context.success = False
            context.error_message = str(e)
            execution_time = time.time() - start_time
            context.performance_metrics['execution_time'] = execution_time
            
            # 获取详细错误信息
            error_details = self._extract_error_details(e)
            context.error_code = error_details['error_code']
            
            self.logger.error(f"❌ 执行失败: {step_name}", step="execution_failed", extra_data={
                'trace_id': trace_id,
                'step_name': step_name,
                'execution_time': f"{execution_time:.3f}s",
                'error_code': context.error_code,
                'error_message': context.error_message,
                'error_details': error_details,
                'context': context.to_dict()
            })
            
            raise
            
        finally:
            # 清理上下文（可选，用于内存管理）
            pass
    
    def _extract_error_details(self, exception: Exception) -> Dict[str, Any]:
        """
        提取详细错误信息
        
        Args:
            exception: 异常对象
            
        Returns:
            Dict[str, Any]: 错误详情
        """
        error_type = type(exception).__name__
        error_message = str(exception)
        
        # 根据异常类型和消息推断错误码
        error_code = self._infer_error_code(error_type, error_message)
        
        # 获取堆栈跟踪
        stack_trace = traceback.format_exc()
        
        return {
            'error_code': error_code,
            'error_type': error_type,
            'error_message': error_message,
            'error_description': ErrorCodes.get_error_description(error_code),
            'stack_trace': stack_trace,
            'exception_module': getattr(exception, '__module__', 'unknown'),
            'timestamp': datetime.now().isoformat()
        }
    
    def _infer_error_code(self, error_type: str, error_message: str) -> str:
        """
        根据异常类型和消息推断错误码
        
        Args:
            error_type: 异常类型
            error_message: 错误消息
            
        Returns:
            str: 错误码
        """
        error_message_lower = error_message.lower()
        
        # 网络相关错误
        if 'timeout' in error_message_lower or 'timeouterror' in error_type.lower():
            return ErrorCodes.API_TIMEOUT
        elif 'connection' in error_message_lower or 'network' in error_message_lower:
            return ErrorCodes.NETWORK_ERROR
        
        # 认证相关错误
        elif 'login' in error_message_lower:
            return ErrorCodes.AUTH_LOGIN_FAILED
        elif 'token' in error_message_lower and 'invalid' in error_message_lower:
            return ErrorCodes.AUTH_TOKEN_INVALID
        elif 'token' in error_message_lower and 'expired' in error_message_lower:
            return ErrorCodes.AUTH_TOKEN_EXPIRED
        elif 'permission' in error_message_lower or 'unauthorized' in error_message_lower:
            return ErrorCodes.AUTH_PERMISSION_DENIED
        
        # 文件相关错误
        elif 'file' in error_message_lower and 'not found' in error_message_lower:
            return ErrorCodes.FILE_NOT_FOUND
        elif 'download' in error_message_lower:
            return ErrorCodes.FILE_DOWNLOAD_ERROR
        elif 'upload' in error_message_lower:
            return ErrorCodes.FILE_UPLOAD_ERROR
        elif 'parse' in error_message_lower or 'parsing' in error_message_lower:
            return ErrorCodes.FILE_PARSE_ERROR
        elif 'format' in error_message_lower:
            return ErrorCodes.FILE_FORMAT_ERROR
        
        # 数据相关错误
        elif 'not found' in error_message_lower:
            return ErrorCodes.DATA_NOT_FOUND
        elif 'validation' in error_message_lower:
            return ErrorCodes.DATA_VALIDATION_ERROR
        elif 'format' in error_message_lower:
            return ErrorCodes.DATA_FORMAT_ERROR
        
        # 数据库相关错误
        elif 'database' in error_message_lower or 'sql' in error_message_lower:
            return ErrorCodes.DATABASE_ERROR
        
        # API相关错误
        elif 'api' in error_message_lower or 'http' in error_message_lower:
            return ErrorCodes.API_REQUEST_FAILED
        
        # 默认系统错误
        else:
            return ErrorCodes.SYSTEM_ERROR
    
    def record_checkpoint(self, trace_id: str, checkpoint_name: str, data: Dict[str, Any] = None):
        """
        记录检查点
        
        Args:
            trace_id: 链路追踪ID
            checkpoint_name: 检查点名称
            data: 附加数据
        """
        self.logger.info(f"📍 检查点: {checkpoint_name}", step="checkpoint", extra_data={
            'trace_id': trace_id,
            'checkpoint_name': checkpoint_name,
            'checkpoint_data': data or {},
            'timestamp': datetime.now().isoformat()
        })
    
    def record_performance_metric(self, metric_name: str, value: float, unit: str = "seconds"):
        """
        记录性能指标
        
        Args:
            metric_name: 指标名称
            value: 指标值
            unit: 单位
        """
        if metric_name not in self.performance_metrics:
            self.performance_metrics[metric_name] = []
        
        self.performance_metrics[metric_name].append(value)
        
        self.logger.info(f"📊 性能指标: {metric_name}", step="performance_metric", extra_data={
            'metric_name': metric_name,
            'value': value,
            'unit': unit,
            'timestamp': datetime.now().isoformat()
        })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取性能摘要
        
        Returns:
            Dict[str, Any]: 性能摘要
        """
        summary = {}
        
        for metric_name, values in self.performance_metrics.items():
            if values:
                summary[metric_name] = {
                    'count': len(values),
                    'average': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'total': sum(values)
                }
        
        return summary
    
    def get_trace_summary(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """
        获取链路追踪摘要
        
        Args:
            trace_id: 链路追踪ID
            
        Returns:
            Optional[Dict[str, Any]]: 链路摘要
        """
        context = self.trace_contexts.get(trace_id)
        if context:
            return context.to_dict()
        return None
    
    def generate_execution_report(self) -> Dict[str, Any]:
        """
        生成执行报告
        
        Returns:
            Dict[str, Any]: 执行报告
        """
        report = {
            'report_timestamp': datetime.now().isoformat(),
            'total_traces': len(self.trace_contexts),
            'successful_traces': sum(1 for ctx in self.trace_contexts.values() if ctx.success),
            'failed_traces': sum(1 for ctx in self.trace_contexts.values() if not ctx.success),
            'performance_summary': self.get_performance_summary(),
            'trace_details': [ctx.to_dict() for ctx in self.trace_contexts.values()],
            'error_summary': self._generate_error_summary()
        }
        
        return report
    
    def _generate_error_summary(self) -> Dict[str, Any]:
        """生成错误摘要"""
        error_codes = {}
        error_types = {}
        
        for context in self.trace_contexts.values():
            if not context.success and context.error_code:
                error_codes[context.error_code] = error_codes.get(context.error_code, 0) + 1
                
                if context.error_message:
                    error_type = context.error_message.split(':')[0] if ':' in context.error_message else 'Unknown'
                    error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            'error_codes': error_codes,
            'error_types': error_types,
            'most_common_error': max(error_codes.items(), key=lambda x: x[1]) if error_codes else None
        }


def trace_execution(step_name: str):
    """
    装饰器：自动追踪函数执行
    
    Args:
        step_name: 步骤名称
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 尝试从参数中获取日志增强器
            enhancer = None
            for arg in args:
                if hasattr(arg, 'logging_enhancer'):
                    enhancer = arg.logging_enhancer
                    break
            
            if not enhancer:
                # 创建临时增强器
                enhancer = LoggingEnhancer()
            
            async with enhancer.trace_execution(step_name) as context:
                result = await func(*args, **kwargs)
                return result
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 同步版本（暂时简化处理）
            return func(*args, **kwargs)
        
        # 根据函数是否为异步函数选择包装器
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


# 全局日志增强器实例（单例模式）
_global_enhancer = None

def get_global_enhancer() -> LoggingEnhancer:
    """获取全局日志增强器实例"""
    global _global_enhancer
    if _global_enhancer is None:
        _global_enhancer = LoggingEnhancer()
    return _global_enhancer 