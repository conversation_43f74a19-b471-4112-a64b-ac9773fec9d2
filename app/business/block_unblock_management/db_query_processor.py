"""
数据库查询处理器

专门负责publish_success_list表的查询操作，为屏蔽解屏蔽业务提供数据支持
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime

from app.business.export_publish_list.db_operations import PublishSuccessDBOperator
from app.business.block_unblock_management.config import BlockUnblockConfig


class DBQueryProcessor:
    """
    数据库查询处理器
    
    复用export_publish_list的数据库操作器，专门为屏蔽解屏蔽业务提供查询服务
    """
    
    def __init__(self, logger: logging.Logger = None):
        """
        初始化数据库查询处理器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        self.config = BlockUnblockConfig.get_default_config()
        
        # 初始化数据库操作器
        self.db_operator = PublishSuccessDBOperator(
            business_type="block_unblock_management",
            script_name="db_query_processor",
            logger=self.logger
        )
        
        # 查询统计
        self.query_stats = {
            'total_queries': 0,
            'successful_queries': 0,
            'failed_queries': 0,
            'total_records_found': 0,
            'missing_records': 0,
            'query_time_total': 0.0
        }
        
        self.logger.info("数据库查询处理器初始化完成")
    
    async def query_skus_with_operation_type(self, 
                                         block_skus: List[str], 
                                         unblock_skus: List[str]) -> Dict[str, Any]:
        """
        根据屏蔽和解屏蔽SKU列表查询数据库映射信息，区分操作类型
        使用批量查询优化性能
        
        Args:
            block_skus: 需要屏蔽的SKU列表
            unblock_skus: 需要解屏蔽的SKU列表
            
        Returns:
            Dict[str, Any]: 包含屏蔽和解屏蔽SKU映射的完整结果
        """
        try:
            self.logger.info(f"开始批量查询SKU映射信息 - 屏蔽: {len(block_skus)}个, 解屏蔽: {len(unblock_skus)}个")
            start_time = datetime.now()
            
            # 合并所有SKU进行批量查询
            all_skus = block_skus + unblock_skus
            if not all_skus:
                self.logger.warning("没有SKU需要查询")
                return self._get_empty_result(block_skus, unblock_skus)
            
            # 批量查询所有SKU数据
            all_sku_records = await self._batch_query_skus(all_skus)
            
            # 在内存中按操作类型分组
            block_mappings, unblock_mappings = self._group_records_by_operation_type(
                all_sku_records, block_skus, unblock_skus
            )
            
            # 统计结果
            end_time = datetime.now()
            query_time = (end_time - start_time).total_seconds()
            
            total_skus = len(block_skus) + len(unblock_skus)
            total_records = sum(len(records) for records in block_mappings.values()) + \
                           sum(len(records) for records in unblock_mappings.values())
            
            # 统计有映射的SKU数量
            mapped_block_skus = len(block_mappings)
            mapped_unblock_skus = len(unblock_mappings)
            total_mapped = mapped_block_skus + mapped_unblock_skus
            
            # 更新查询统计
            self.query_stats['total_queries'] += 1
            self.query_stats['successful_queries'] += 1
            self.query_stats['total_records_found'] += total_records
            self.query_stats['missing_records'] += total_skus - total_mapped
            self.query_stats['query_time_total'] += query_time
            
            result = {
                'block_sku_mappings': block_mappings,
                'unblock_sku_mappings': unblock_mappings,
                'statistics': {
                    'block_skus_count': len(block_skus),
                    'unblock_skus_count': len(unblock_skus),
                    'block_skus_mapped': mapped_block_skus,
                    'unblock_skus_mapped': mapped_unblock_skus,
                    'total_records_found': total_records,
                    'query_time': query_time,
                    'block_missing_skus': len(block_skus) - mapped_block_skus,
                    'unblock_missing_skus': len(unblock_skus) - mapped_unblock_skus,
                    'optimization': 'batch_query'  # 标记使用了批量查询优化
                }
            }
            
            self.logger.info("批量SKU映射查询完成", step="batch_sku_mapping_complete", extra_data={
                'total_skus_queried': len(all_skus),
                'block_skus_mapped': f"{mapped_block_skus}/{len(block_skus)}",
                'unblock_skus_mapped': f"{mapped_unblock_skus}/{len(unblock_skus)}",
                'total_records': total_records,
                'query_time': f"{query_time:.3f}s",
                'performance_improvement': f"{len(all_skus)}个SKU仅用1次查询"
            })
            
            return result
            
        except Exception as e:
            self.query_stats['total_queries'] += 1
            self.query_stats['failed_queries'] += 1
            self.logger.error(f"批量查询SKU映射失败: {str(e)}")
            raise
    
    async def _batch_query_skus(self, sku_list: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量查询SKU列表，一次性获取所有SKU的记录
        
        Args:
            sku_list: SKU列表
            
        Returns:
            Dict[str, List[Dict[str, Any]]]: SKU到记录列表的映射
        """
        try:
            if not sku_list:
                return {}
            
            # 去重SKU列表
            unique_skus = list(set(sku_list))
            self.logger.debug(f"批量查询 {len(unique_skus)} 个唯一SKU")
            
            # 分批查询避免SQL过长
            batch_size = 500  # 每批查询500个SKU
            all_records = []
            
            for i in range(0, len(unique_skus), batch_size):
                batch_skus = unique_skus[i:i + batch_size]
                batch_records = await self._execute_batch_query(batch_skus)
                all_records.extend(batch_records)
                
                if len(unique_skus) > batch_size:
                    self.logger.debug(f"已完成批次 {i//batch_size + 1}/{(len(unique_skus) + batch_size - 1)//batch_size}")
            
            # 按SKU分组记录
            sku_mappings = {}
            for record in all_records:
                sku = record.get('sku')
                if sku:
                    if sku not in sku_mappings:
                        sku_mappings[sku] = []
                    sku_mappings[sku].append(record)
            
            # 按create_time排序每个SKU的记录（最新的在前）
            for sku in sku_mappings:
                sku_mappings[sku].sort(
                    key=lambda x: x.get('create_time') or datetime.min, 
                    reverse=True
                )
            
            found_count = len(sku_mappings)
            missing_count = len(unique_skus) - found_count
            total_records = sum(len(records) for records in sku_mappings.values())
            
            self.logger.info(f"批量查询完成", step="batch_query_complete", extra_data={
                'unique_skus_queried': len(unique_skus),
                'skus_found': found_count,
                'skus_missing': missing_count,
                'total_records': total_records
            })
            
            if missing_count > 0:
                missing_skus = [sku for sku in unique_skus if sku not in sku_mappings]
                self.logger.debug(f"未找到记录的SKU示例: {missing_skus[:3]}{'...' if missing_count > 3 else ''}")
            
            return sku_mappings
            
        except Exception as e:
            self.logger.error(f"批量查询SKU失败: {str(e)}")
            raise
    
    async def _execute_batch_query(self, sku_batch: List[str]) -> List[Dict[str, Any]]:
        """
        执行批量查询
        
        Args:
            sku_batch: SKU批次
            
        Returns:
            List[Dict[str, Any]]: 查询结果记录列表
        """
        try:
            # 构建IN查询条件
            placeholders = ','.join(['%s'] * len(sku_batch))
            
            sql = f"""
                SELECT 
                    sku, 
                    seller_sku_child, 
                    account
                FROM publish_success_list 
                WHERE sku IN ({placeholders})
                ORDER BY sku, create_time DESC
            """
            
            # 执行查询
            db_results = self.db_operator.db_manager.execute_query(sql, sku_batch)
            
            # 转换查询结果
            records = []
            for record in db_results:
                records.append({
                    'sku': record.get('sku', ''),
                    'seller_sku_child': record.get('seller_sku_child', ''),
                    'account': record.get('account', '')
                })
            
            return records
            
        except Exception as e:
            self.logger.error(f"执行批量查询失败: {str(e)}")
            raise
    
    def _group_records_by_operation_type(self, 
                                        all_sku_records: Dict[str, List[Dict[str, Any]]], 
                                        block_skus: List[str], 
                                        unblock_skus: List[str]) -> Tuple[Dict[str, List[Dict[str, Any]]], Dict[str, List[Dict[str, Any]]]]:
        """
        按操作类型分组记录
        
        Args:
            all_sku_records: 所有SKU的记录
            block_skus: 屏蔽SKU列表
            unblock_skus: 解屏蔽SKU列表
            
        Returns:
            Tuple[Dict, Dict]: (屏蔽SKU映射, 解屏蔽SKU映射)
        """
        block_mappings = {}
        unblock_mappings = {}
        
        # 处理屏蔽SKU
        for sku in block_skus:
            if sku in all_sku_records:
                records = all_sku_records[sku].copy()
                # 为每条记录添加操作类型
                for record in records:
                    record['operation_type'] = 'block'
                block_mappings[sku] = records
        
        # 处理解屏蔽SKU
        for sku in unblock_skus:
            if sku in all_sku_records:
                records = all_sku_records[sku].copy()
                # 为每条记录添加操作类型
                for record in records:
                    record['operation_type'] = 'unblock'
                unblock_mappings[sku] = records
        
        return block_mappings, unblock_mappings
    
    def _get_empty_result(self, block_skus: List[str], unblock_skus: List[str]) -> Dict[str, Any]:
        """
        获取空结果
        
        Args:
            block_skus: 屏蔽SKU列表
            unblock_skus: 解屏蔽SKU列表
            
        Returns:
            Dict[str, Any]: 空结果
        """
        return {
            'block_sku_mappings': {},
            'unblock_sku_mappings': {},
            'statistics': {
                'block_skus_count': len(block_skus),
                'unblock_skus_count': len(unblock_skus),
                'block_skus_mapped': 0,
                'unblock_skus_mapped': 0,
                'total_records_found': 0,
                'query_time': 0.0,
                'block_missing_skus': len(block_skus),
                'unblock_missing_skus': len(unblock_skus)
            }
        }
    
    async def _query_single_sku(self, sku: str) -> List[Dict[str, Any]]:
        """
        查询单个SKU的所有记录
        
        Args:
            sku: SKU编号
            
        Returns:
            List[Dict[str, Any]]: 该SKU的所有记录列表
        """
        try:
            # 构建查询SQL
            sql = """
                SELECT 
                    sku, 
                    seller_sku_child, 
                    account
                FROM publish_success_list 
                WHERE sku = %s
                ORDER BY create_time DESC
            """
            
            # 执行查询
            db_results = self.db_operator.db_manager.execute_query(sql, (sku,))
            
            # 转换查询结果
            records = []
            for record in db_results:
                records.append({
                    'sku': record.get('sku', ''),
                    'seller_sku_child': record.get('seller_sku_child', ''),
                    'account': record.get('account', '')
                })
            
            return records
            
        except Exception as e:
            self.logger.error(f"查询单个SKU '{sku}' 失败: {str(e)}")
            return []
    
    async def validate_sku_mappings(self, mapping_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证SKU映射结果的数据质量
        
        Args:
            mapping_result: SKU映射查询结果
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            validation_result = {
                'is_valid': True,
                'warnings': [],
                'errors': [],
                'statistics': {}
            }
            
            block_mappings = mapping_result.get('block_sku_mappings', {})
            unblock_mappings = mapping_result.get('unblock_sku_mappings', {})
            
            # 验证必需字段完整性
            required_fields = ['seller_sku_child', 'account']
            
            incomplete_records = 0
            total_records = 0
            
            for sku, records in block_mappings.items():
                for record in records:
                    total_records += 1
                    missing_fields = [field for field in required_fields if not record.get(field)]
                    if missing_fields:
                        incomplete_records += 1
                        validation_result['warnings'].append(
                            f"屏蔽SKU '{sku}' 缺少字段: {missing_fields}"
                        )
            
            for sku, records in unblock_mappings.items():
                for record in records:
                    total_records += 1
                    missing_fields = [field for field in required_fields if not record.get(field)]
                    if missing_fields:
                        incomplete_records += 1
                        validation_result['warnings'].append(
                            f"解屏蔽SKU '{sku}' 缺少字段: {missing_fields}"
                        )
            
            # 计算完整性比例
            if total_records > 0:
                completeness_ratio = (total_records - incomplete_records) / total_records
                validation_result['statistics']['completeness_ratio'] = completeness_ratio
                validation_result['statistics']['incomplete_records'] = incomplete_records
                validation_result['statistics']['total_records'] = total_records
                
                if completeness_ratio < 0.9:  # 如果完整性低于90%，标记为无效
                    validation_result['is_valid'] = False
                    validation_result['errors'].append(
                        f"数据完整性过低: {completeness_ratio:.1%} (阈值: 90%)"
                    )
            
            # 检查是否有任何有效记录
            if total_records == 0:
                validation_result['is_valid'] = False
                validation_result['errors'].append("未找到任何有效的SKU映射记录")
            
            validation_result['statistics']['validation_time'] = datetime.now().isoformat()
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"验证SKU映射失败: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"验证过程异常: {str(e)}"],
                'warnings': [],
                'statistics': {}
            }
    
    async def test_database_connection(self) -> Dict[str, Any]:
        """
        测试数据库连接
        
        Returns:
            Dict[str, Any]: 连接测试结果
        """
        try:
            # 测试基本查询
            test_sql = "SELECT COUNT(*) as count FROM publish_success_list LIMIT 1"
            result = self.db_operator.db_manager.execute_query(test_sql)
            
            if result:
                return {
                    'connected': True,
                    'message': '数据库连接正常',
                    'table_exists': True,
                    'test_query_result': result[0]
                }
            else:
                return {
                    'connected': False,
                    'message': '数据库查询返回空结果',
                    'table_exists': False
                }
                
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {str(e)}")
            return {
                'connected': False,
                'message': f'数据库连接失败: {str(e)}',
                'error': str(e)
            }
    
    def get_query_statistics(self) -> Dict[str, Any]:
        """
        获取查询统计信息
        
        Returns:
            Dict[str, Any]: 查询统计
        """
        stats = self.query_stats.copy()
        
        # 计算平均查询时间
        if stats['successful_queries'] > 0:
            stats['avg_query_time'] = stats['query_time_total'] / stats['successful_queries']
        else:
            stats['avg_query_time'] = 0.0
        
        # 计算成功率
        if stats['total_queries'] > 0:
            stats['success_rate'] = stats['successful_queries'] / stats['total_queries']
        else:
            stats['success_rate'] = 0.0
        
        return stats