"""
公告通知客户端

负责调用公告通知列表接口，获取屏蔽与释放SKU明细文件信息
"""

import asyncio
import base64
import json
import logging
import re
import urllib.parse
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import platform

from app.shared.clients.base_yimai_client import BaseYimaiClient
from app.business.block_unblock_management.config import BlockUnblockConfig


class NoticeClient(BaseYimaiClient):
    """
    公告通知客户端
    
    继承BaseYimaiClient，专门处理公告通知列表相关接口
    基于实际接口 /message/message/getMessageList 实现
    """
    
    def __init__(self, logger: logging.Logger = None):
        """
        初始化公告通知客户端
        
        Args:
            logger: 日志记录器
        """
        # 首先获取配置
        self.config = BlockUnblockConfig.get_default_config()
        
        # 构建正确的客户端配置
        client_config = {
            'base_url': self.config['base_url'],  # 使用正确的base_url
            'request_timeout': self.config.get('request_timeout', 60),
            'max_retries': self.config.get('max_retry_count', 3),
            'retry_delay': self.config.get('retry_delay', 2)
        }
        
        # 初始化父类，传入正确的配置
        super().__init__(logger, client_config)
        
        # 公告通知接口配置
        self.notice_api_config = {
            'endpoint': '/message/message/getMessageList',
            'title_keyword': '屏蔽与释放',  # 固定搜索关键词
            'distributor_id': 6057,      # 分销商ID（从接口示例获取）
            'msg_type': 1,               # 消息类型
            'source_from': 1,            # 来源类型
            'page_size': 20,             # 默认页面大小
        }
        
        self.logger.info("公告通知客户端初始化完成", extra_data={
            'endpoint': self.notice_api_config['endpoint'],
            'title_keyword': self.notice_api_config['title_keyword'],
            'base_url': self.config['base_url']
        })
    
    async def get_notice_list(self, 
                             tokens: Dict[str, str], 
                             date_range: Tuple[str, str] = None,
                             page_size: int = None,
                             user_id: str = None,
                             local_mode: bool = False) -> List[Dict[str, Any]]:
        """
        获取公告通知列表
        
        Args:
            tokens: 认证Token字典
            date_range: 时间范围 (start_date, end_date) 格式: "2025-06-26 00:00:00"，本地模式或Windows环境下可为None
            page_size: 每页数量，默认使用配置值
            user_id: 用户ID（来自登录响应），如果为None则尝试从JWT Token解析
            local_mode: 本地模式，为True时不使用创建时间筛选条件
            
        Returns:
            List[Dict[str, Any]]: 符合条件的公告文件列表
        """
        try:
            if page_size is None:
                page_size = self.notice_api_config['page_size']
                
            # 检测是否为Windows本地环境
            is_local_windows = platform.system().lower() == 'windows'
            
            # 本地模式或Windows环境下处理时间范围
            if local_mode or is_local_windows:
                # 不使用时间筛选，设置为None
                effective_date_range = None
                mode_desc = "本地模式" if local_mode else "Windows本地环境"
                self.logger.info(f"🖥️ {mode_desc}：获取公告通知列表（无时间筛选）", extra_data={
                    'local_mode': local_mode,
                    'is_local_windows': is_local_windows,
                    'page_size': page_size,
                    'title_keyword': self.notice_api_config['title_keyword']
                })
            else:
                # 服务器模式：使用时间筛选
                if not date_range:
                    raise ValueError("服务器模式下必须提供date_range参数")
                effective_date_range = date_range
                self.logger.info("🌐 服务器模式：获取公告通知列表（带时间筛选）", extra_data={
                    'local_mode': local_mode,
                    'is_local_windows': is_local_windows,
                'date_range': date_range,
                'page_size': page_size,
                'title_keyword': self.notice_api_config['title_keyword']
            })
            
            # 调试：记录传入的tokens信息
            self.logger.info("开始构建请求参数", extra_data={
                'tokens_available': tokens is not None,
                'tokens_keys': list(tokens.keys()) if tokens else None,
                'jwt_token_present': 'jwt_token' in tokens if tokens else False,
                'jwt_token_length': len(tokens.get('jwt_token', '')) if tokens else 0,
                'auth_token_present': 'Authorization' in tokens if tokens else False,
                'token1_check_present': 'token1_check' in tokens if tokens else False,
                'token2_present': 'token2' in tokens if tokens else False,
                'token2_timestamp_present': 'token2_timestamp' in tokens if tokens else False,
                'device_number_present': 'device_number' in tokens if tokens else False,
                'local_mode': local_mode,
                'is_local_windows': is_local_windows
            })
            
            # 构建请求参数
            query_params = self._build_notice_query_params(
                date_range=effective_date_range, 
                page_size=page_size, 
                tokens=tokens, 
                user_id=user_id,
                local_mode=local_mode
            )
            
            # 调试：记录构建的参数
            self.logger.info("请求参数构建完成", extra_data={
                'uid_param': query_params.get('uid'),
                'uid_is_empty': query_params.get('uid') == '',
                'params_count': len(query_params),
                'params_keys': list(query_params.keys()),
                'has_time_filter': 'create_time_start' in query_params and query_params['create_time_start'],
                'local_mode': local_mode,
                'is_local_windows': is_local_windows
            })
            
            # 构建完整URL
            base_url = self.config.get('base_url', 'https://dcmmaster.yibainetwork.com')
            notice_list_url = f"{base_url}{self.notice_api_config['endpoint']}"
            
            self.logger.info(f"准备调用公告通知列表接口", extra_data={
                'url': notice_list_url,
                'method': 'GET',
                'local_mode': local_mode,
                'is_local_windows': is_local_windows
            })
            
            # 调用公告通知列表接口（使用GET方法，参数在URL中）
            response_data = await self.get_request(
                endpoint=notice_list_url,
                params=query_params,
                tokens=tokens
            )
            
            if not response_data:
                self.logger.warning("公告通知列表接口返回空数据")
                return []
            
            # 记录响应状态
            self.logger.info("公告通知列表接口调用成功", extra_data={
                'response_code': response_data.get('code'),
                'response_message': response_data.get('message', ''),
                'has_data': 'data' in response_data,
                'data_type': type(response_data.get('data')).__name__ if 'data' in response_data else None,
                'local_mode': local_mode,
                'is_local_windows': is_local_windows
            })
            
            # 解析响应数据
            notice_records = self._parse_notice_response(response_data)
            
            mode_desc = "本地模式" if local_mode else ("Windows本地环境" if is_local_windows else "服务器模式")
            self.logger.info(f"获取到 {len(notice_records)} 条公告记录", extra_data={
                'records_count': len(notice_records),
                'mode': mode_desc,
                'local_mode': local_mode,
                'is_local_windows': is_local_windows
            })
            
            return notice_records
            
        except Exception as e:
            mode_desc = "本地模式" if local_mode else ("Windows本地环境" if is_local_windows else "服务器模式")
            self.logger.error(f"获取公告通知列表失败（{mode_desc}）: {str(e)}", extra_data={
                'local_mode': local_mode,
                'is_local_windows': is_local_windows,
                'error_type': type(e).__name__
            })
            raise
    
    def _parse_user_id_from_jwt(self, jwt_token: str) -> Optional[str]:
        """
        从JWT Token中解析用户ID
        
        Args:
            jwt_token: JWT Token
            
        Returns:
            Optional[str]: 用户ID
        """
        try:
            if not jwt_token:
                return None
                
            # JWT格式：header.payload.signature
            parts = jwt_token.split('.')
            if len(parts) != 3:
                self.logger.warning("JWT Token格式不正确")
                return None
            
            # 解析payload部分
            payload = parts[1]
            
            # 添加必要的padding（JWT使用base64url编码）
            padding = 4 - (len(payload) % 4)
            if padding != 4:
                payload += '=' * padding
            
            # 解码
            decoded_bytes = base64.urlsafe_b64decode(payload)
            payload_data = json.loads(decoded_bytes.decode('utf-8'))
            
            # 尝试多个可能的用户ID字段
            user_id_fields = ['uid', 'user_id', 'id', 'userId', 'sub', 'userinfo', 'aud']
            
            for field in user_id_fields:
                if field in payload_data:
                    user_id = payload_data[field]
                    # 如果是数字或字符串数字，转换为字符串
                    if isinstance(user_id, (int, str)):
                        user_id_str = str(user_id)
                        self.logger.debug(f"从JWT Token中解析到用户ID: {user_id_str} (来源字段: {field})")
                        return user_id_str
                        
            # 如果没有找到直接的用户ID字段，尝试从嵌套对象中查找
            if 'userinfo' in payload_data and isinstance(payload_data['userinfo'], dict):
                userinfo = payload_data['userinfo']
                for field in user_id_fields:
                    if field in userinfo:
                        user_id = userinfo[field]
                        if isinstance(user_id, (int, str)):
                            user_id_str = str(user_id)
                            self.logger.debug(f"从JWT Token userinfo中解析到用户ID: {user_id_str}")
                            return user_id_str
            
            self.logger.warning("JWT Token中未找到用户ID字段", extra_data={
                'available_fields': list(payload_data.keys())
            })
            return None
            
        except Exception as e:
            self.logger.error(f"解析JWT Token用户ID失败: {str(e)}")
            return None

    def _build_notice_query_params(self, 
                                  date_range: Tuple[str, str], 
                                  page_size: int,
                                  tokens: Dict[str, str] = None,
                                  user_id: str = None,
                                  local_mode: bool = False) -> Dict[str, Any]:
        """
        构建公告通知列表请求参数
        
        根据实际接口格式构建参数：
        read_status=&title=%E5%B1%8F%E8%94%BD%E4%B8%8E%E9%87%8A%E6%94%BE&type=&is_top=
        &size=20&distributor_id=6057&msg_type=1&current=1&page=1&limit=20
        &create_time_start=2025-06-26+00:00:00&create_time_end=2025-07-02+23:59:59
        &uid=19311&source_from=1
        
        本地Windows环境下不传递时间筛选参数
        
        Args:
            date_range: 时间范围 (start_date, end_date)，本地模式下可为None
            page_size: 每页数量
            tokens: 认证Token字典
            user_id: 用户ID（优先使用），如果为None则尝试从tokens解析
            local_mode: 本地模式，为True时不使用创建时间筛选条件
            
        Returns:
            Dict[str, Any]: 请求参数
        """
        # 检测是否为本地Windows环境
        is_local_windows = platform.system().lower() == 'windows'
        
        # 确定用户ID来源
        final_user_id = ''
        
        # 优先使用传入的user_id参数（来自登录管理器）
        if user_id:
            final_user_id = str(user_id)
            self.logger.debug("使用传入的用户ID", extra_data={
                'user_id': final_user_id,
                'source': 'parameter',
                'local_mode': local_mode,
                'is_local_windows': is_local_windows
            })
        else:
            # 如果没有传入user_id，尝试从JWT Token解析
            self.logger.debug("尝试从JWT Token解析用户ID", extra_data={
                'tokens_available': tokens is not None,
                'has_jwt_token': tokens and 'jwt_token' in tokens if tokens else False,
                'local_mode': local_mode,
                'is_local_windows': is_local_windows
            })
            
            if tokens and tokens.get('jwt_token'):
                jwt_token = tokens['jwt_token']
                parsed_user_id = self._parse_user_id_from_jwt(jwt_token)
                if parsed_user_id:
                    final_user_id = str(parsed_user_id)
                    self.logger.debug("从JWT Token解析到用户ID", extra_data={
                        'user_id': final_user_id,
                        'source': 'jwt_token',
                        'local_mode': local_mode,
                        'is_local_windows': is_local_windows
                    })
                else:
                    self.logger.warning("JWT Token解析失败")
            else:
                self.logger.warning("没有可用的用户ID来源")
        
        # 构建基础参数（与实际接口一致）
        params = {
            'read_status': '',  # 空值表示不限制阅读状态
            'title': self.notice_api_config['title_keyword'],  # "屏蔽与释放"
            'type': '',  # 空值表示不限制类型
            'is_top': '',  # 空值表示不限制置顶状态
            'size': page_size,
            'distributor_id': self.notice_api_config['distributor_id'],
            'msg_type': self.notice_api_config['msg_type'],
            'current': 1,  # 当前页码
            'page': 1,     # 页码（兼容性）
            'limit': page_size,  # 每页限制数量
            'uid': final_user_id,  # 用户ID（优先来自登录管理器，其次JWT Token）
            'source_from': self.notice_api_config['source_from']
        }
        
        # 根据模式和环境决定是否添加时间筛选条件
        if local_mode or is_local_windows:
            # 本地模式或Windows环境：不添加时间筛选条件
            mode_desc = "本地模式" if local_mode else "Windows本地环境"
            self.logger.debug(f"🖥️ {mode_desc}：不添加时间筛选条件", extra_data={
                'local_mode': local_mode,
                'is_local_windows': is_local_windows,
                'params_count': len(params),
                'user_id': final_user_id
            })
        else:
            # 服务器模式：添加时间筛选条件
            if date_range:
                start_date, end_date = date_range
                params['create_time_start'] = start_date
                params['create_time_end'] = end_date
                
                self.logger.debug("🌐 服务器模式：添加时间筛选条件", extra_data={
                    'local_mode': local_mode,
                    'is_local_windows': is_local_windows,
                    'date_range': date_range,
                    'params_count': len(params),
                    'user_id': final_user_id
                })
            else:
                self.logger.warning("服务器模式下缺少时间范围参数")
        
        self.logger.debug("构建公告通知列表请求参数完成", extra_data={
            'params_count': len(params),
            'date_range': date_range if not (local_mode or is_local_windows) else 'N/A (无时间筛选)',
            'title_keyword': params['title'],
            'user_id': final_user_id,
            'has_user_id': bool(final_user_id),
            'local_mode': local_mode,
            'is_local_windows': is_local_windows,
            'has_time_filter': 'create_time_start' in params
        })
        return params
    
    def _parse_notice_response(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        解析公告通知列表响应数据
        
        根据实际接口响应格式解析：
        {
            "code": 200,
            "data": {
                "pageInfo": {
                    "records": [...]
                }
            }
        }
        
        Args:
            response_data: 响应数据
            
        Returns:
            List[Dict[str, Any]]: 公告记录列表
        """
        try:
            # 检查响应状态
            if response_data.get('code') != 200:
                error_msg = response_data.get('msg', '未知错误')
                self.logger.error(f"接口返回错误: {error_msg}")
                return []
            
            # 解析records数组
            page_info = response_data.get('data', {}).get('pageInfo', {})
            notice_records = page_info.get('records', [])
            
            total_count = page_info.get('total', 0)
            current_page = page_info.get('current', 1)
            page_size = page_info.get('size', 20)
            
            self.logger.info(f"解析公告列表响应成功", extra_data={
                'total_count': total_count,
                'current_page': current_page,
                'page_size': page_size,
                'records_count': len(notice_records)
            })
            
            return notice_records
            
        except Exception as e:
            self.logger.error(f"解析公告通知列表响应数据失败: {str(e)}", extra_data={
                'response_structure': list(response_data.keys()) if isinstance(response_data, dict) else type(response_data)
            })
            return []
    

    
    async def download_file_from_url(self, 
                                    file_url: str, 
                                    tokens: Dict[str, str]) -> bytes:
        """
        从URL下载文件到内存
        
        Args:
            file_url: 文件下载链接（字符串或数组）
            tokens: 认证Token
            
        Returns:
            bytes: 文件内容字节流
        """
        try:
            # 处理file_url可能是数组的情况
            if isinstance(file_url, list):
                if len(file_url) > 0:
                    actual_url = file_url[0]  # 取第一个URL
                    self.logger.debug(f"文件URL是数组，取第一个元素: {actual_url}")
                else:
                    raise ValueError("文件URL数组为空")
            elif isinstance(file_url, str):
                actual_url = file_url
                self.logger.debug(f"文件URL是字符串: {actual_url}")
            else:
                raise ValueError(f"文件URL格式不支持: {type(file_url)}")
            
            if not actual_url:
                raise ValueError("文件下载链接为空")
            
            # URL有效性检查
            if not actual_url.startswith(('http://', 'https://')):
                raise ValueError(f"无效的文件URL格式: {actual_url}")
            
            self.logger.info(f"开始下载文件: {actual_url}", extra_data={
                'url_type': 'array' if isinstance(file_url, list) else 'string',
                'url_length': len(actual_url),
                'domain': actual_url.split('/')[2] if len(actual_url.split('/')) > 2 else 'unknown'
            })
            
            # 下载文件到内存
            file_content = await self.get_file_content(
                file_url=actual_url,
                tokens=tokens
            )
            
            if not file_content:
                raise Exception("文件下载成功但内容为空")
            
            file_size = len(file_content)
            # 使用配置中的文件大小限制，提供默认值512MB
            max_size = self.config.get('max_file_size_mb', 512) * 1024 * 1024
            
            if file_size > max_size:
                raise Exception(f"文件过大: {file_size / 1024 / 1024:.2f}MB, 限制: {self.config.get('max_file_size_mb', 512)}MB")
            
            # 文件内容验证
            if file_size < 100:  # 小于100字节可能是错误响应
                content_preview = file_content[:50].decode('utf-8', errors='ignore')
                self.logger.warning(f"文件大小异常小: {file_size}字节", extra_data={
                    'content_preview': content_preview,
                    'file_url': actual_url
                })
            
            # 简单的Excel文件格式验证
            if actual_url.endswith('.xlsx') or actual_url.endswith('.xls'):
                # Excel文件应该以特定的magic bytes开始
                if not (file_content.startswith(b'PK') or file_content.startswith(b'\xd0\xcf')):
                    self.logger.warning("下载的文件可能不是有效的Excel格式", extra_data={
                        'file_url': actual_url,
                        'file_size': file_size,
                        'first_bytes': file_content[:10].hex()
                    })
            
            self.logger.info(f"文件下载成功", extra_data={
                'file_size_bytes': file_size,
                'file_size_kb': file_size / 1024,
                'file_size_mb': file_size / 1024 / 1024,
                'file_url': actual_url,
                'file_type': 'excel' if actual_url.endswith(('.xlsx', '.xls')) else 'unknown'
            })
            return file_content
            
        except Exception as e:
            display_url = file_url if isinstance(file_url, str) else (file_url[0] if isinstance(file_url, list) and len(file_url) > 0 else str(file_url))
            
            # 根据异常类型提供不同的诊断信息和建议
            if isinstance(e, ValueError):
                error_category = "参数错误"
                suggestions = [
                    "检查文件URL格式是否正确",
                    "确认URL不为空且格式有效",
                    "如果是数组，确保至少包含一个有效URL"
                ]
            elif 'timeout' in str(e).lower() or 'TimeoutError' in str(type(e).__name__):
                error_category = "网络超时"
                suggestions = [
                    "检查网络连接是否稳定",
                    "文件可能较大，请耐心等待",
                    "如果持续超时，考虑在网络较好时重试",
                    f"当前超时设置: {self.config.get('download_timeout', 600)}秒"
                ]
            elif '404' in str(e) or 'Not Found' in str(e):
                error_category = "文件不存在"
                suggestions = [
                    "文件可能已被删除或移动",
                    "检查URL是否正确",
                    "确认文件是否已过期",
                    "联系管理员确认文件状态"
                ]
            elif '403' in str(e) or 'Forbidden' in str(e):
                error_category = "访问权限不足"
                suggestions = [
                    "检查认证Token是否有效",
                    "确认用户权限是否足够",
                    "尝试重新登录获取新Token",
                    "联系管理员确认访问权限"
                ]
            elif '500' in str(e) or 'Internal Server Error' in str(e):
                error_category = "服务器错误"
                suggestions = [
                    "服务器可能出现临时故障",
                    "稍后重试",
                    "联系技术支持报告问题",
                    "检查服务器状态"
                ]
            elif 'DNS' in str(e) or 'Name resolution' in str(e):
                error_category = "域名解析错误"
                suggestions = [
                    "检查网络连接",
                    "确认DNS设置",
                    "尝试使用其他网络",
                    "检查域名是否正确"
                ]
            else:
                error_category = "未知错误"
                suggestions = [
                    "检查网络连接",
                    "确认文件URL是否有效",
                    "尝试重新登录",
                    "联系技术支持"
                ]
            
            self.logger.error(f"下载文件失败 [{error_category}]: {str(e)}", extra_data={
                'file_url': display_url,
                'error_category': error_category,
                'exception_type': type(e).__name__,
                'suggestions': suggestions,
                'diagnostic_info': {
                    'url_type': type(file_url).__name__,
                    'url_valid': display_url.startswith(('http://', 'https://')) if display_url else False,
                    'tokens_provided': tokens is not None,
                    'tokens_count': len(tokens) if tokens else 0
                }
            })
            
            # 重新抛出异常，但包含更多上下文信息
            enhanced_error_msg = f"{error_category}: {str(e)}"
            if suggestions:
                enhanced_error_msg += f"\n建议: {'; '.join(suggestions[:2])}"  # 只显示前两个建议避免日志过长
            
            raise Exception(enhanced_error_msg) from e
    
    async def get_latest_block_file(self, 
                                   tokens: Dict[str, str], 
                                   date_range: Tuple[str, str] = None,
                                   user_id: str = None,
                                   local_mode: bool = False) -> Optional[str]:
        """
        获取最新的屏蔽文件下载链接
        
        根据业务需求：在records中，获取最新created_at时间的一条记录中的file_path字段
        
        Args:
            tokens: 认证Token
            date_range: 时间范围 (start_date, end_date)，本地模式下可为None
            user_id: 用户ID（来自登录响应），用于API认证
            local_mode: 本地模式，为True时不使用创建时间筛选条件
            
        Returns:
            Optional[str]: 最新屏蔽文件的下载链接，如果没有找到则返回None
        """
        try:
            if local_mode:
                self.logger.info("🏠 本地模式：开始获取最新屏蔽文件（无时间筛选）")
            else:
                self.logger.info("🌐 正常模式：开始获取最新屏蔽文件", extra_data={'date_range': date_range})
            
            # 获取公告通知列表
            notice_records = await self.get_notice_list(
                tokens=tokens, 
                date_range=date_range, 
                user_id=user_id,
                local_mode=local_mode
            )
            
            if not notice_records:
                mode_desc = "本地模式" if local_mode else "正常模式"
                self.logger.warning(f"{mode_desc}：没有找到符合条件的公告记录")
                return None
            
            # 查找最新创建的记录
            latest_record = self._find_latest_created_record(notice_records)
            
            if not latest_record:
                mode_desc = "本地模式" if local_mode else "正常模式"
                self.logger.warning(f"{mode_desc}：没有找到有效的最新记录")
                return None
            
            # 提取文件下载链接
            file_path = latest_record.get('file_path')
            if not file_path:
                self.logger.warning("最新记录中没有找到file_path字段", extra_data={
                    'record_id': latest_record.get('id'),
                    'created_at': latest_record.get('created_at')
                })
                return None
            
            # 处理file_path可能是数组的情况
            if isinstance(file_path, list):
                if len(file_path) > 0:
                    file_url = file_path[0]  # 取第一个URL
                    self.logger.debug(f"file_path是数组，取第一个元素: {file_url}")
                else:
                    self.logger.warning("file_path数组为空")
                    return None
            elif isinstance(file_path, str):
                file_url = file_path
                self.logger.debug(f"file_path是字符串: {file_url}")
            else:
                self.logger.warning(f"file_path格式不支持: {type(file_path)}")
                return None

            mode_desc = "本地模式" if local_mode else "正常模式"
            self.logger.info(f"✅ {mode_desc}：获取最新屏蔽文件成功", extra_data={
                'file_path': file_path,  # 原始值
                'file_url': file_url,    # 处理后的URL
                'record_id': latest_record.get('id'),
                'created_at': latest_record.get('created_at'),
                'local_mode': local_mode
            })
            
            return file_url
            
        except Exception as e:
            mode_desc = "本地模式" if local_mode else "正常模式"
            self.logger.error(f"{mode_desc}：获取最新屏蔽文件失败: {str(e)}", extra_data={
                'local_mode': local_mode
            })
            raise
    
    def _find_latest_created_record(self, notice_records: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        从公告记录列表中找到发送时间最新的记录
        
        支持多种时间字段格式：created_at, createTime, create_time, sendTime, send_time
        按照发送时间降序排序，返回最新的一条记录
        
        Args:
            notice_records: 公告记录列表
            
        Returns:
            Optional[Dict[str, Any]]: 发送时间最新的记录，如果没有找到则返回None
        """
        try:
            if not notice_records:
                self.logger.warning("公告记录列表为空")
                return None
            
            # 过滤出有file_path字段的记录
            valid_records = [record for record in notice_records if record.get('file_path')]
            
            if not valid_records:
                self.logger.warning("没有找到包含file_path的有效记录", extra_data={
                    'total_records': len(notice_records)
                })
                return None
        
            # 支持的时间字段名（按优先级排序）
            time_field_candidates = [
                'created_at', 'createTime', 'create_time', 
                'sendTime', 'send_time', 'update_time', 'updateTime'
            ]
            
            # 为每个记录找到合适的时间字段并转换为标准格式
            processed_records = []
            
            for record in valid_records:
                time_value = None
                time_field_used = None
                
                # 按优先级查找时间字段
                for field_name in time_field_candidates:
                    if field_name in record and record[field_name]:
                        time_value = record[field_name]
                        time_field_used = field_name
                        break
                
                if not time_value:
                    self.logger.debug(f"记录 {record.get('id', 'unknown')} 没有找到有效的时间字段")
                    continue
                
                # 转换时间值为可比较的格式
                try:
                    if isinstance(time_value, str):
                        # 尝试解析时间字符串
                        try:
                            # 常见格式: "2025-07-04 15:56:35"
                            parsed_time = datetime.strptime(time_value, '%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            try:
                                # 其他可能的格式: "2025-07-04T15:56:35"
                                parsed_time = datetime.strptime(time_value.replace('T', ' '), '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                try:
                                    # ISO 格式: "2025-07-04T15:56:35.000Z"
                                    parsed_time = datetime.fromisoformat(time_value.replace('Z', '+00:00').replace('T', ' '))
                                except ValueError:
                                    self.logger.warning(f"无法解析时间格式: {time_value}")
                                    continue
                        
                        # 创建处理后的记录
                        processed_record = record.copy()
                        processed_record['_parsed_time'] = parsed_time
                        processed_record['_time_field_used'] = time_field_used
                        processed_records.append(processed_record)
                        
                    elif isinstance(time_value, (int, float)):
                        # 时间戳格式
                        parsed_time = datetime.fromtimestamp(time_value)
                        processed_record = record.copy()
                        processed_record['_parsed_time'] = parsed_time
                        processed_record['_time_field_used'] = time_field_used
                        processed_records.append(processed_record)
                        
                    else:
                        self.logger.warning(f"不支持的时间格式类型: {type(time_value)}")
                        continue
                        
                except Exception as parse_error:
                    self.logger.warning(f"解析时间失败: {time_value}, 错误: {str(parse_error)}")
                    continue
            
            if not processed_records:
                self.logger.warning("没有找到包含有效时间字段的记录", extra_data={
                    'total_records': len(notice_records),
                    'valid_records_with_file_path': len(valid_records)
                })
                return None
            
            # 按解析后的时间降序排序（最新的在前）
            sorted_records = sorted(
                processed_records,
                key=lambda x: x['_parsed_time'],
                reverse=True
            )
            
            latest_record = sorted_records[0]
            
            self.logger.info("找到发送时间最新的屏蔽文件记录", extra_data={
                'total_records': len(notice_records),
                'valid_records_with_file_path': len(valid_records),
                'processed_records': len(processed_records),
                'latest_record_id': latest_record.get('id'),
                'latest_time': latest_record['_parsed_time'].strftime('%Y-%m-%d %H:%M:%S'),
                'time_field_used': latest_record['_time_field_used'],
                'has_file_path': bool(latest_record.get('file_path'))
            })
            
            # 移除临时字段
            latest_record.pop('_parsed_time', None)
            latest_record.pop('_time_field_used', None)
            
            return latest_record
            
        except Exception as e:
            self.logger.error(f"查找发送时间最新记录失败: {str(e)}")
            return None
    
    async def get_yesterday_block_file_url(self, 
                                          tokens: Dict[str, str], 
                                          user_id: str = None,
                                          local_mode: bool = False) -> Optional[str]:
        """
        获取昨日屏蔽文件下载链接
        
        Args:
            tokens: 认证Token
            user_id: 用户ID
            local_mode: 本地模式，为True时获取所有文件而不限制时间
            
        Returns:
            Optional[str]: 昨日屏蔽文件的下载链接
        """
        try:
            if local_mode:
                self.logger.info("🏠 本地模式：获取最新屏蔽文件（无时间限制）")
                # 本地模式：不使用时间筛选，获取最新的文件
                return await self.get_latest_block_file(
                    tokens=tokens,
                    date_range=None,
                    user_id=user_id,
                    local_mode=True
                )
            else:
                # 正常模式：获取昨日时间范围
                from datetime import datetime, timedelta
                
                yesterday = datetime.now() - timedelta(days=1)
                start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
                
                date_range = (
                    start_time.strftime("%Y-%m-%d %H:%M:%S"),
                    end_time.strftime("%Y-%m-%d %H:%M:%S")
                )
                
                self.logger.info("🌐 正常模式：获取昨日屏蔽文件", extra_data={
                    'date_range': date_range
                })
            
                return await self.get_latest_block_file(
                    tokens=tokens,
                    date_range=date_range,
                    user_id=user_id,
                    local_mode=False
                )
            
        except Exception as e:
            mode_desc = "本地模式" if local_mode else "正常模式"
            self.logger.error(f"{mode_desc}：获取昨日屏蔽文件失败: {str(e)}")
            return None
    
    def update_distributor_id(self, new_distributor_id: int):
        """
        更新分销商ID
        
        Args:
            new_distributor_id: 新的分销商ID
        """
        self.notice_api_config['distributor_id'] = new_distributor_id
        self.logger.info("分销商ID已更新", extra_data={
            'new_distributor_id': new_distributor_id
        })

    def _generate_cache_filename(self, file_url: str) -> str:
        """
        生成缓存文件名
        
        Args:
            file_url: 文件URL
            
        Returns:
            str: 缓存文件名
        """
        import hashlib
        from datetime import datetime
        
        # 从URL提取文件名
        url_filename = file_url.split('/')[-1]
        if '?' in url_filename:
            url_filename = url_filename.split('?')[0]
        
        # 生成URL的hash作为唯一标识
        url_hash = hashlib.md5(file_url.encode()).hexdigest()[:8]
        
        # 组合成缓存文件名
        name_part = url_filename.split('.')[0] if '.' in url_filename else 'cached_file'
        ext_part = url_filename.split('.')[-1] if '.' in url_filename else 'xlsx'
        
        return f"{name_part}_{url_hash}.{ext_part}"
    
    def _get_cache_directory(self) -> Path:
        """
        获取缓存目录
        
        Returns:
            Path: 缓存目录路径
        """
        import tempfile
        from pathlib import Path
        
        cache_dir = Path(tempfile.gettempdir()) / "rpa_k8s_cache" / "block_files"
        cache_dir.mkdir(parents=True, exist_ok=True)
        return cache_dir
    
    async def _save_file_to_cache(self, file_url: str, file_content: bytes) -> str:
        """
        保存文件到本地缓存
        
        Args:
            file_url: 文件URL
            file_content: 文件内容
            
        Returns:
            str: 缓存文件路径
        """
        try:
            cache_dir = self._get_cache_directory()
            cache_filename = self._generate_cache_filename(file_url)
            cache_path = cache_dir / cache_filename
            
            with open(cache_path, 'wb') as f:
                f.write(file_content)
            
            self.logger.info(f"文件已保存到缓存: {cache_path}", extra_data={
                'cache_size': len(file_content),
                'cache_path': str(cache_path),
                'original_url': file_url
            })
            
            return str(cache_path)
            
        except Exception as e:
            self.logger.error(f"保存文件到缓存失败: {str(e)}", extra_data={
                'file_url': file_url,
                'content_size': len(file_content) if file_content else 0
            })
            return None
    
    def _check_local_cache(self, file_url: str) -> Optional[bytes]:
        """
        检查本地缓存中是否有文件
        
        Args:
            file_url: 文件URL
            
        Returns:
            Optional[bytes]: 缓存的文件内容，如果没有则返回None
        """
        try:
            cache_dir = self._get_cache_directory()
            cache_filename = self._generate_cache_filename(file_url)
            cache_path = cache_dir / cache_filename
            
            if cache_path.exists():
                # 检查文件修改时间（24小时内的缓存才有效）
                import time
                file_age = time.time() - cache_path.stat().st_mtime
                max_cache_age = 24 * 60 * 60  # 24小时
                
                if file_age < max_cache_age:
                    with open(cache_path, 'rb') as f:
                        content = f.read()
                    
                    self.logger.info(f"找到有效的缓存文件: {cache_path}", extra_data={
                        'cache_size': len(content),
                        'file_age_hours': file_age / 3600,
                        'original_url': file_url
                    })
                    return content
                else:
                    self.logger.debug(f"缓存文件已过期: {cache_path}", extra_data={
                        'file_age_hours': file_age / 3600,
                        'max_age_hours': max_cache_age / 3600
                    })
                    # 删除过期的缓存文件
                    cache_path.unlink(missing_ok=True)
            
            return None
            
        except Exception as e:
            self.logger.error(f"检查本地缓存失败: {str(e)}", extra_data={
                'file_url': file_url
            })
            return None
    
    def _record_download_failure(self, file_url: str, error_info: Dict[str, Any]):
        """
        记录下载失败信息
        
        Args:
            file_url: 文件URL
            error_info: 错误信息
        """
        try:
            import json
            from datetime import datetime
            
            failure_log = {
                'timestamp': datetime.now().isoformat(),
                'file_url': file_url,
                'error_category': error_info.get('error_category', 'unknown'),
                'exception_type': error_info.get('exception_type', 'unknown'),
                'suggestions': error_info.get('suggestions', []),
                'diagnostic_info': error_info.get('diagnostic_info', {})
            }
            
            # 保存到失败日志文件
            cache_dir = self._get_cache_directory()
            failure_log_path = cache_dir / "download_failures.jsonl"
            
            with open(failure_log_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(failure_log, ensure_ascii=False) + '\n')
            
            self.logger.debug(f"下载失败信息已记录: {failure_log_path}")
            
        except Exception as e:
            self.logger.error(f"记录下载失败信息时出错: {str(e)}")
    
    async def download_file_with_fallback(self, 
                                         file_url: str, 
                                         tokens: Dict[str, str]) -> bytes:
        """
        带备用机制的文件下载
        
        Args:
            file_url: 文件下载链接
            tokens: 认证Token
            
        Returns:
            bytes: 文件内容字节流
        """
        try:
            # 1. 首先检查本地缓存
            cached_content = self._check_local_cache(file_url)
            if cached_content:
                self.logger.info("使用本地缓存文件", extra_data={
                    'file_url': file_url,
                    'cache_size': len(cached_content)
                })
                return cached_content
            
            # 2. 尝试下载文件
            self.logger.info("本地缓存未找到，开始下载文件")
            file_content = await self.download_file_from_url(file_url, tokens)
            
            # 3. 下载成功，保存到缓存
            await self._save_file_to_cache(file_url, file_content)
            
            return file_content
            
        except Exception as e:
            # 4. 下载失败，记录失败信息
            error_info = {
                'error_category': '下载失败',
                'exception_type': type(e).__name__,
                'suggestions': ['检查网络连接', '稍后重试', '联系技术支持'],
                'diagnostic_info': {
                    'has_tokens': tokens is not None,
                    'url_valid': file_url.startswith(('http://', 'https://')) if file_url else False
                }
            }
            
            self._record_download_failure(file_url, error_info)
            
            # 5. 再次检查缓存（可能有稍旧但仍可用的文件）
            self.logger.info("下载失败，尝试使用较旧的缓存文件...")
            
            try:
                cache_dir = self._get_cache_directory()
                cache_filename = self._generate_cache_filename(file_url)
                cache_path = cache_dir / cache_filename
                
                if cache_path.exists():
                    with open(cache_path, 'rb') as f:
                        content = f.read()
                    
                    import time
                    file_age = time.time() - cache_path.stat().st_mtime
                    
                    self.logger.warning(f"使用较旧的缓存文件作为备用方案", extra_data={
                        'cache_path': str(cache_path),
                        'cache_size': len(content),
                        'file_age_hours': file_age / 3600,
                        'original_error': str(e)
                    })
                    
                    return content
                    
            except Exception as cache_e:
                self.logger.error(f"备用缓存读取也失败: {str(cache_e)}")
            
            # 6. 所有备用方案都失败，抛出原始异常
            self.logger.error("所有下载和备用方案都失败", extra_data={
                'file_url': file_url,
                'original_error': str(e)
            })
            
            raise