"""
Web驱动抽象基类

定义统一的Web自动化接口，所有具体驱动实现都必须遵循此接口。
支持异步操作，提供类型安全和完整的错误处理。
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, List, Tuple
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from pathlib import Path
import time


@dataclass
class WebDriverConfig:
    """Web驱动配置类"""
    
    # 基础配置
    headless: bool = True
    window_width: int = 1920
    window_height: int = 1080
    timeout: int = 30
    page_load_timeout: int = 30
    
    # 浏览器配置
    browser_type: str = "chromium"  # chromium, firefox, webkit
    user_agent: Optional[str] = None
    viewport: Optional[Dict[str, int]] = None
    
    # 性能配置
    slow_mo: int = 0  # 操作之间的延迟（毫秒）
    navigation_timeout: int = 30000  # 导航超时（毫秒）
    
    # 高级配置
    ignore_https_errors: bool = True
    bypass_csp: bool = True
    java_script_enabled: bool = True
    
    # 调试配置
    debug_mode: bool = False
    screenshots_enabled: bool = True
    trace_enabled: bool = False
    
    # 业务配置
    business_type: Optional[str] = None
    script_name: Optional[str] = None
    task_id: Optional[str] = None
    
    def __post_init__(self):
        """配置后处理"""
        if self.viewport is None:
            self.viewport = {"width": self.window_width, "height": self.window_height}
        
        if self.user_agent is None:
            self.user_agent = (
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )


class BaseWebDriver(ABC):
    """Web驱动抽象基类"""
    
    def __init__(self, config: WebDriverConfig):
        """
        初始化Web驱动
        
        Args:
            config: 驱动配置
        """
        self.config = config
        self._is_initialized = False
        self._start_time = None
        self._page_count = 0
        
    @property
    def is_initialized(self) -> bool:
        """检查驱动是否已初始化"""
        return self._is_initialized
    
    @property
    def execution_time(self) -> float:
        """获取执行时间（秒）"""
        if self._start_time is None:
            return 0.0
        return time.time() - self._start_time
    
    # 生命周期管理
    @abstractmethod
    async def initialize(self) -> None:
        """初始化驱动"""
        self._start_time = time.time()
        self._is_initialized = True
    
    @abstractmethod
    async def close(self) -> None:
        """关闭驱动"""
        self._is_initialized = False
    
    @asynccontextmanager
    async def context(self):
        """驱动上下文管理器"""
        try:
            if not self.is_initialized:
                await self.initialize()
            yield self
        finally:
            await self.close()
    
    # 页面导航
    @abstractmethod
    async def navigate_to(self, url: str, **options) -> None:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
            **options: 额外选项
        """
        pass
    
    @abstractmethod
    async def get_current_url(self) -> str:
        """获取当前页面URL"""
        pass
    
    @abstractmethod
    async def get_title(self) -> str:
        """获取页面标题"""
        pass
    
    @abstractmethod
    async def refresh(self) -> None:
        """刷新页面"""
        pass
    
    @abstractmethod
    async def go_back(self) -> None:
        """后退"""
        pass
    
    @abstractmethod
    async def go_forward(self) -> None:
        """前进"""
        pass
    
    # 元素操作
    @abstractmethod
    async def safe_click(self, selector: str, **options) -> bool:
        """
        安全点击元素
        
        Args:
            selector: 元素选择器
            **options: 额外选项（如timeout等）
            
        Returns:
            bool: 是否点击成功
        """
        pass
    
    @abstractmethod
    async def safe_input(self, selector: str, text: str, **options) -> bool:
        """
        安全输入文本
        
        Args:
            selector: 元素选择器
            text: 输入文本
            **options: 额外选项
            
        Returns:
            bool: 是否输入成功
        """
        pass
    
    @abstractmethod
    async def get_text(self, selector: str, **options) -> Optional[str]:
        """
        获取元素文本
        
        Args:
            selector: 元素选择器
            **options: 额外选项
            
        Returns:
            Optional[str]: 元素文本，如果元素不存在则返回None
        """
        pass
    
    @abstractmethod
    async def get_attribute(self, selector: str, attribute: str, **options) -> Optional[str]:
        """
        获取元素属性
        
        Args:
            selector: 元素选择器
            attribute: 属性名
            **options: 额外选项
            
        Returns:
            Optional[str]: 属性值，如果元素不存在或属性不存在则返回None
        """
        pass
    
    @abstractmethod
    async def is_visible(self, selector: str, **options) -> bool:
        """
        检查元素是否可见
        
        Args:
            selector: 元素选择器
            **options: 额外选项
            
        Returns:
            bool: 元素是否可见
        """
        pass
    
    @abstractmethod
    async def is_enabled(self, selector: str, **options) -> bool:
        """
        检查元素是否启用
        
        Args:
            selector: 元素选择器
            **options: 额外选项
            
        Returns:
            bool: 元素是否启用
        """
        pass
    
    # 等待操作
    @abstractmethod
    async def wait_for_element(self, selector: str, **options) -> bool:
        """
        等待元素出现
        
        Args:
            selector: 元素选择器
            **options: 额外选项（如timeout、state等）
            
        Returns:
            bool: 元素是否出现
        """
        pass
    
    @abstractmethod
    async def wait_for_page_load(self, **options) -> None:
        """
        等待页面加载完成
        
        Args:
            **options: 额外选项
        """
        pass
    
    @abstractmethod
    async def wait_for_timeout(self, timeout: int) -> None:
        """
        等待指定时间
        
        Args:
            timeout: 等待时间（毫秒）
        """
        pass
    
    # 页面信息
    @abstractmethod
    async def get_page_source(self) -> str:
        """获取页面源码"""
        pass
    
    @abstractmethod
    async def execute_javascript(self, script: str, *args) -> Any:
        """
        执行JavaScript代码
        
        Args:
            script: JavaScript代码
            *args: 传递给JavaScript的参数
            
        Returns:
            Any: JavaScript执行结果
        """
        pass
    
    # 截图和调试
    @abstractmethod
    async def take_screenshot(self, path: Optional[str] = None) -> str:
        """
        截图
        
        Args:
            path: 保存路径，如果为None则自动生成
            
        Returns:
            str: 截图文件路径
        """
        pass
    
    @abstractmethod
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标数据
        """
        pass
    
    # 高级操作
    @abstractmethod
    async def select_option(self, selector: str, value: Union[str, List[str]], **options) -> bool:
        """
        选择下拉框选项
        
        Args:
            selector: 下拉框选择器
            value: 选项值
            **options: 额外选项
            
        Returns:
            bool: 是否选择成功
        """
        pass
    
    @abstractmethod
    async def upload_file(self, selector: str, file_path: str, **options) -> bool:
        """
        上传文件
        
        Args:
            selector: 文件输入框选择器
            file_path: 文件路径
            **options: 额外选项
            
        Returns:
            bool: 是否上传成功
        """
        pass
    
    @abstractmethod
    async def hover(self, selector: str, **options) -> bool:
        """
        鼠标悬停
        
        Args:
            selector: 元素选择器
            **options: 额外选项
            
        Returns:
            bool: 是否悬停成功
        """
        pass
    
    @abstractmethod
    async def scroll_to(self, selector: str, **options) -> bool:
        """
        滚动到元素
        
        Args:
            selector: 元素选择器
            **options: 额外选项
            
        Returns:
            bool: 是否滚动成功
        """
        pass
    
    # 批量操作
    async def batch_click(self, selectors: List[str], **options) -> List[bool]:
        """
        批量点击元素
        
        Args:
            selectors: 元素选择器列表
            **options: 额外选项
            
        Returns:
            List[bool]: 每个元素的点击结果
        """
        results = []
        for selector in selectors:
            result = await self.safe_click(selector, **options)
            results.append(result)
        return results
    
    async def batch_input(self, selector_text_pairs: List[Tuple[str, str]], **options) -> List[bool]:
        """
        批量输入文本
        
        Args:
            selector_text_pairs: (选择器, 文本) 对列表
            **options: 额外选项
            
        Returns:
            List[bool]: 每个输入的结果
        """
        results = []
        for selector, text in selector_text_pairs:
            result = await self.safe_input(selector, text, **options)
            results.append(result)
        return results 