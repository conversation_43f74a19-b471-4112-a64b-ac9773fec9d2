apiVersion: batch/v1
kind: Job
metadata:
  name: rpa-shop-account-info-job
  namespace: rpa-automation
  labels:
    app: rpa-shop-account-info
    business-type: shop_account_info
    version: "3.0"
spec:
  # 并行度控制
  parallelism: 1
  completions: 1
  # 任务超时设置
  activeDeadlineSeconds: 3600  # 1小时超时
  # 重试策略
  backoffLimit: 2
  # 任务模板
  template:
    metadata:
      labels:
        app: rpa-shop-account-info
        business-type: shop_account_info
    spec:
      # 重启策略
      restartPolicy: Never
      # 节点选择器（可选）
      nodeSelector:
        kubernetes.io/os: linux
      # 容器配置
      containers:
      - name: rpa-shop-account-info
        image: crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai/rpa-k8s-shop-account-info:latest
        imagePullPolicy: Always
        
        # 环境变量配置
        env:
        - name: BUSINESS_TYPE
          value: "shop_account_info"
        - name: SCRIPT_NAME
          value: "shop_account_processor_async"
        - name: RPA_EXECUTION_MODE
          value: "k8s"
        - name: PLAYWRIGHT_HEADLESS
          value: "true"
        - name: PLAYWRIGHT_TIMEOUT
          value: "45"
        - name: PLAYWRIGHT_PAGE_LOAD_WAIT
          value: "15"
        - name: TZ
          value: "Asia/Shanghai"
        
        # 资源限制
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
        
        # 安全上下文
        securityContext:
          runAsUser: 1000
          runAsGroup: 1000
          fsGroup: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
        
        # 卷挂载
        volumeMounts:
        - name: downloads
          mountPath: /app/downloads
        - name: logs
          mountPath: /app/logs
        - name: browser-cache
          mountPath: /home/<USER>/.cache
        - name: browser-data
          mountPath: /home/<USER>/.local/share
        
        # 健康检查
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; print('Liveness check passed'); sys.exit(0)"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; print('Readiness check passed'); sys.exit(0)"
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # 日志配置
        command: ["/app/start.sh"]
        
      # 卷定义
      volumes:
      - name: downloads
        emptyDir: {}
      - name: logs
        emptyDir: {}
      - name: browser-cache
        emptyDir: {}
      - name: browser-data
        emptyDir: {}
      
      # 镜像拉取密钥（如果需要）
      imagePullSecrets:
      - name: aliyun-registry-secret 