# Cursor AI助手规则配置

## 核心原则
- 每次回答前标识模型信息
- 任务结束后删除测试文件
- 及时更新README.md记录重要变更

## MCP工具使用规范

### interactive_feedback工具
**参数规范**：
- message: string类型
- full_response: string类型  
- predefined_options: **必须是数组类型** `["选项1", "选项2"]`

**常见错误**：
- ❌ `"predefined_options": "选项1,选项2"`
- ✅ `"predefined_options": ["选项1", "选项2"]`

### 其他MCP工具
- 严格按照工具定义使用参数类型
- 调用前检查参数格式是否正确
- 遇到类型错误时立即调整格式

## 工作流程
1. 研究 -> 构思 -> 计划 -> 执行 -> 评审
2. 关键步骤使用interactive_feedback确认
3. 完成后标记<执行完毕>

## 技术规范
- 优先使用简体中文
- 技术术语保持原文
- 面向专业程序员，交互简洁专业
- 避免不必要的解释

## 内存管理
- 及时更新/删除错误记忆
- 用户纠正时立即调整记忆
- 引用记忆时使用[[memory:ID]]格式 