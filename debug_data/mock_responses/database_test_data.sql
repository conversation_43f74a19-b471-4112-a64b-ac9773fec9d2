
            INSERT INTO account_trade_detail (id, system_sku, source_order_no, creation_time, flow_status, execution_log)
            VALUES 
            ('debug_001', '*************', 'FXPK250524509609', '2025-05-25 10:00:00', 0, '待处理'),
            ('debug_002', '*************', 'FXPK250524509609', '2025-05-25 11:30:00', 0, '待处理'),
            ('debug_003', '*************', 'NOTFOUND123', '2025-05-25 12:00:00', 0, '待处理');
            

            -- 查询测试数据
            SELECT id, system_sku, source_order_no, order_no, shop, flow_status, execution_log
            FROM account_trade_detail 
            WHERE id IN ('debug_001', 'debug_002', 'debug_003');
            

            -- 清理测试数据
            DELETE FROM account_trade_detail 
            WHERE id IN ('debug_001', 'debug_002', 'debug_003');
            