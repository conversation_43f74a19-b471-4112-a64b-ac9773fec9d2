-- 插入测试数据
-- 用于店铺账户信息API增强功能测试

INSERT INTO account_trade_detail (
    id, system_sku, source_order_no, creation_time, flow_status, 
    execution_log, order_no, shop, update_time
) VALUES 
('test_task_001', '*************', 'FXPK250524509609', '2025-07-25 12:04:31', 0, '待处理 - 测试数据', NULL, NULL, '2025-07-25 12:04:31'),
('test_task_002', '*************', 'FXPK250524509610', '2025-07-25 10:04:31', 0, '待处理 - 测试数据', NULL, NULL, '2025-07-25 10:04:31'),
('test_task_003', '*************', 'FXPK250524509611', '2025-07-25 08:04:31', 0, '待处理 - 测试数据', NULL, NULL, '2025-07-25 08:04:31'),
('test_task_004', '*************', 'NOTFOUND123456', '2025-07-25 06:04:31', 0, '待处理 - 测试数据（预期不匹配）', NULL, NULL, '2025-07-25 06:04:31'),
('test_task_005', '*************', '', '2025-07-25 02:04:31', 0, '待处理 - 空包裹号测试', NULL, NULL, '2025-07-25 02:04:31'),
('test_task_006', '', 'TESTORDER789', '2025-07-25 00:04:31', 0, '待处理 - 空SKU测试', NULL, NULL, '2025-07-25 00:04:31');

-- 查询测试数据
SELECT 
    id, system_sku, source_order_no, creation_time, 
    flow_status, execution_log, order_no, shop
FROM account_trade_detail 
WHERE id LIKE 'test_task_%' OR id LIKE 'batch_%'
ORDER BY creation_time DESC;

-- 更新测试数据（模拟处理结果）
-- 模拟找到匹配的情况
UPDATE account_trade_detail SET 
    order_no = 'FXAM250524015033',
    shop = 'ICE CATCA(ICE CATCA)',
    flow_status = 1,
    execution_log = '✅ 处理成功：找到匹配的sourceOrderNo'
WHERE id IN ('test_task_001', 'test_task_002', 'test_task_003');

-- 模拟未找到匹配的情况
UPDATE account_trade_detail SET 
    flow_status = 2,
    execution_log = '❌ 处理完成：未找到匹配的sourceOrderNo'
WHERE id = 'test_task_004';

-- 清理测试数据
DELETE FROM account_trade_detail 
WHERE id LIKE 'test_task_%' OR id LIKE 'batch_%';

