[{"id": "test_task_001", "system_sku": "1411230353011", "source_order_no": "FXPK250524509609", "creation_time": "2025-07-25 12:04:31", "flow_status": 0, "execution_log": "待处理 - 测试数据", "order_no": null, "shop": null, "update_time": "2025-07-25 12:04:31", "scenario": "正常匹配测试"}, {"id": "test_task_002", "system_sku": "1411230353012", "source_order_no": "FXPK250524509610", "creation_time": "2025-07-25 10:04:31", "flow_status": 0, "execution_log": "待处理 - 测试数据", "order_no": null, "shop": null, "update_time": "2025-07-25 10:04:31", "scenario": "正常匹配测试"}, {"id": "test_task_003", "system_sku": "1411230353013", "source_order_no": "FXPK250524509611", "creation_time": "2025-07-25 08:04:31", "flow_status": 0, "execution_log": "待处理 - 测试数据", "order_no": null, "shop": null, "update_time": "2025-07-25 08:04:31", "scenario": "正常匹配测试"}, {"id": "test_task_004", "system_sku": "1411230353014", "source_order_no": "NOTFOUND123456", "creation_time": "2025-07-25 06:04:31", "flow_status": 0, "execution_log": "待处理 - 测试数据（预期不匹配）", "order_no": null, "shop": null, "update_time": "2025-07-25 06:04:31", "scenario": "不匹配测试"}, {"id": "test_task_005", "system_sku": "1411230353015", "source_order_no": "", "scenario": "空包裹号测试", "creation_time": "2025-07-25 02:04:31", "flow_status": 0, "execution_log": "待处理 - 空包裹号测试", "order_no": null, "shop": null, "update_time": "2025-07-25 02:04:31"}, {"id": "test_task_006", "system_sku": "", "source_order_no": "TESTORDER789", "scenario": "空SKU测试", "creation_time": "2025-07-25 00:04:31", "flow_status": 0, "execution_log": "待处理 - 空SKU测试", "order_no": null, "shop": null, "update_time": "2025-07-25 00:04:31"}]