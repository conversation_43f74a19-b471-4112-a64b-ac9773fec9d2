{"timestamp": "2025-06-19T14:26:00.059654", "operation": "query_processed_ids", "sql": "\n            SELECT id \n            FROM account_trade_detail \n            WHERE id IN (test_001,test_002,test_003) \n            AND flow_status = 2\n            ", "account_ids": ["test_001", "test_002", "test_003"], "status": "pending"}
{"timestamp": "2025-06-19T14:26:00.079662", "operation": "query_processed_ids", "sql": "\n            SELECT id \n            FROM account_trade_detail \n            WHERE id IN (test_001,test_002,test_003) \n            AND flow_status = 2\n            ", "account_ids": ["test_001", "test_002", "test_003"], "status": "pending"}
