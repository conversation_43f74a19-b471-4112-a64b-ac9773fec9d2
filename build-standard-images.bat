@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ============================================
echo RPA-K8s 标准镜像构建器 v2.0
echo Standard Images Builder (Config-Driven)
echo ============================================

:: 标准化镜像配置（基于docker-image-config.json）
set REGISTRY_PREFIX=crpi-24x92shfjpe9hdz2.cn-hangzhou.personal.cr.aliyuncs.com/lingyichuhai
set USERNAME=lingyichuhai
set PASSWORD=lingyichuhai2025

:: 标准镜像名称定义
set BASE_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-base:latest
set SHOP_ACCOUNT_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-shop-account-info:latest
set SYN_PUBLISH_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-syn-publish-list:latest
set BLOCK_UNBLOCK_IMAGE=%REGISTRY_PREFIX%/rpa-k8s-block-unblock:latest

echo [配置] 注册表: %REGISTRY_PREFIX%
echo [配置] 标准镜像名称:
echo [配置] - 基础镜像: rpa-k8s-base:latest
echo [配置] - 店铺账号信息: rpa-k8s-shop-account-info:latest
echo [配置] - 同步刊登成功列表: rpa-k8s-syn-publish-list:latest
echo [配置] - 屏蔽与解屏蔽管理: rpa-k8s-block-unblock:latest

:: 解析命令行参数
set BUILD_TYPE=%1
if "%BUILD_TYPE%"=="" set BUILD_TYPE=all

echo [执行] 构建类型: %BUILD_TYPE%

:: 1. 拉取基础镜像
echo ============================================
echo 1. 拉取基础镜像
echo ============================================
echo [拉取] 正在拉取基础镜像...
docker pull %BASE_IMAGE%

if !errorlevel! neq 0 (
    echo [警告] 基础镜像拉取失败，尝试使用本地镜像
    docker images | findstr "rpa-k8s-base" | findstr "latest"
    if !errorlevel! neq 0 (
        echo [错误] 本地也没有基础镜像，请先构建基础镜像
        exit /b 1
    )
)

:: 2. 根据构建类型执行
if "%BUILD_TYPE%"=="base" goto BUILD_BASE
if "%BUILD_TYPE%"=="shop" goto BUILD_SHOP
if "%BUILD_TYPE%"=="syn" goto BUILD_SYN
if "%BUILD_TYPE%"=="block" goto BUILD_BLOCK
if "%BUILD_TYPE%"=="all" goto BUILD_ALL
if "%BUILD_TYPE%"=="business" goto BUILD_BUSINESS

:BUILD_BASE
echo [构建] 跳过基础镜像构建，使用现有镜像
goto PUSH_IMAGES

:BUILD_SHOP
echo ============================================
echo 构建店铺账号信息收集RPA
echo ============================================
call :BUILD_IMAGE "shop_account_info" "%SHOP_ACCOUNT_IMAGE%" "app/business/shop_account_info/Dockerfile"
goto PUSH_IMAGES

:BUILD_SYN
echo ============================================
echo 构建同步刊登成功列表
echo ============================================
call :BUILD_IMAGE "syn_publish_list" "%SYN_PUBLISH_IMAGE%" "app/business/export_publish_list/Dockerfile"
goto PUSH_IMAGES

:BUILD_BLOCK
echo ============================================
echo 构建屏蔽与解屏蔽管理
echo ============================================
call :BUILD_IMAGE "block_unblock" "%BLOCK_UNBLOCK_IMAGE%" "app/business/block_unblock_management/Dockerfile"
goto PUSH_IMAGES

:BUILD_BUSINESS
echo ============================================
echo 构建所有业务镜像
echo ============================================
call :BUILD_IMAGE "shop_account_info" "%SHOP_ACCOUNT_IMAGE%" "app/business/shop_account_info/Dockerfile"
call :BUILD_IMAGE "syn_publish_list" "%SYN_PUBLISH_IMAGE%" "app/business/export_publish_list/Dockerfile"
call :BUILD_IMAGE "block_unblock" "%BLOCK_UNBLOCK_IMAGE%" "app/business/block_unblock_management/Dockerfile"
goto PUSH_IMAGES

:BUILD_ALL
echo ============================================
echo 构建所有镜像（包括基础镜像）
echo ============================================
call :BUILD_IMAGE "shop_account_info" "%SHOP_ACCOUNT_IMAGE%" "app/business/shop_account_info/Dockerfile"
call :BUILD_IMAGE "syn_publish_list" "%SYN_PUBLISH_IMAGE%" "app/business/export_publish_list/Dockerfile"
call :BUILD_IMAGE "block_unblock" "%BLOCK_UNBLOCK_IMAGE%" "app/business/block_unblock_management/Dockerfile"
goto PUSH_IMAGES

:BUILD_IMAGE
set MODULE_NAME=%~1
set IMAGE_NAME=%~2
set DOCKERFILE_PATH=%~3

echo [构建] 开始构建: %MODULE_NAME%
echo [构建] 镜像名称: %IMAGE_NAME%
echo [构建] Dockerfile: %DOCKERFILE_PATH%
set START_TIME=%TIME%

docker build -f "%DOCKERFILE_PATH%" -t "%IMAGE_NAME%" .

if !errorlevel! neq 0 (
    echo [错误] %MODULE_NAME% 镜像构建失败
    exit /b 1
)

set END_TIME=%TIME%
echo [构建] 完成: %MODULE_NAME%
echo [计时] 开始: %START_TIME% 结束: %END_TIME%
echo.
goto :eof

:PUSH_IMAGES
echo ============================================
echo 推送镜像到注册表
echo ============================================

echo [推送] 登录注册表...
echo %PASSWORD% | docker login --username %USERNAME% --password-stdin %REGISTRY_PREFIX%

if !errorlevel! neq 0 (
    echo [错误] 注册表登录失败
    exit /b 1
)

:: 推送相应镜像
if "%BUILD_TYPE%"=="shop" (
    docker push %SHOP_ACCOUNT_IMAGE%
) else if "%BUILD_TYPE%"=="syn" (
    docker push %SYN_PUBLISH_IMAGE%
) else if "%BUILD_TYPE%"=="block" (
    docker push %BLOCK_UNBLOCK_IMAGE%
) else (
    echo [推送] 推送所有业务镜像...
    docker push %SHOP_ACCOUNT_IMAGE%
    docker push %SYN_PUBLISH_IMAGE%
    docker push %BLOCK_UNBLOCK_IMAGE%
)

echo [成功] 镜像推送完成

:SHOW_RESULTS
echo ============================================
echo 标准镜像构建完成
echo ============================================
echo [标准镜像清单]
echo 1. 基础镜像: %BASE_IMAGE%
echo 2. 店铺账号信息: %SHOP_ACCOUNT_IMAGE%
echo 3. 同步刊登成功列表: %SYN_PUBLISH_IMAGE%
echo 4. 屏蔽与解屏蔽管理: %BLOCK_UNBLOCK_IMAGE%
echo.
echo [部署命令]
echo kubectl apply -f k8s-shop-account-job.yaml
echo kubectl apply -f k8s-export-publish-list-job.yaml
echo kubectl apply -f k8s-block-unblock-management-job.yaml
echo.
echo [使用说明]
echo 构建单个镜像: build-standard-images.bat [shop^|syn^|block]
echo 构建所有业务镜像: build-standard-images.bat business
echo 构建所有镜像: build-standard-images.bat all

pause 